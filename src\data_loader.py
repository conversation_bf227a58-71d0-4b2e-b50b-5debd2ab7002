from pathlib import Path
import logging
from typing import Optional, List
from llama_index.readers.confluence import ConfluenceReader
from llama_index.core import Document
import os

logger = logging.getLogger(__name__)

def load_html_content(file_path: str) -> Optional[str]:
    """
    Loads the raw content from a specified file path.

    Args:
        file_path (str): The full path to the file to be loaded.

    Returns:
        Optional[str]: The content of the file as a string if successful,
        otherwise None if an error occurs.
    """
    try:
        return Path(file_path).read_text(encoding='utf-8')
    except (FileNotFoundError, IOError, UnicodeDecodeError) as e:
        logger.error(f"Failed to load file {file_path}: {e}")
        return None

def load_pirs_from_confluence(page_ids: Optional[List[str]] = None, space_key: Optional[str] = None, label: Optional[str] = None) -> Optional[List[Document]]:
    """
    Loads PIR documents from Confluence using the ConfluenceReader.

    Returns:
        Optional[List[Document]]: A list of Document objects if successful, otherwise None.
    """
    # Retrieve Confluence credentials and configuration from environment variables
    url = os.getenv("CONFLUENCE_URL")
    username = os.getenv("CONFLUENCE_USERNAME")
    token = os.getenv("CONFLUENCE_API_TOKEN")

    # Check if all required environment variables are set
    if not all([url, username, token]):
        logger.error("Missing one or more required environment variables for Confluence loading.")
        return None

    try:
        # Initialize ConfluenceReader with OAuth2 authentication
        reader = ConfluenceReader(base_url=url, api_token=token)
        
        # Load documents from Confluence
        documents = reader.load_data(page_ids=page_ids, include_attachments=False)
        
        logger.info(f"Loaded {len(documents)} documents from Confluence space {space_key}.")
        return documents
    except Exception as e:
        logger.error(f"Failed to load documents from Confluence: {e}")
        return None

def load_confluence_child_pages(parent_page_id: str) -> Optional[List[Document]]:
    """
    Loads child pages from Confluence for a given parent page ID using CQL.

    Args:
        parent_page_id (str): The ID of the parent page in Confluence.

    Returns:
        Optional[List[Document]]: A list of Document objects representing the child pages,
                                  or None if loading fails.
    """
    # Retrieve Confluence credentials and configuration from environment variables
    url = os.getenv("CONFLUENCE_URL")
    username = os.getenv("CONFLUENCE_USERNAME")
    token = os.getenv("CONFLUENCE_API_TOKEN")

    # Check if all required environment variables are set
    if not all([url, username, token]):
        logger.error("Missing one or more required environment variables for Confluence loading.")
        return None

    try:
        # Initialize ConfluenceReader with OAuth2 authentication
        reader = ConfluenceReader(base_url=url, oauth2={'username': username, 'api_key': token})

        # Construct CQL to find direct child pages of the parent page ID
        cql = f"ancestor={parent_page_id} and type=page"
        # Optional Enhancement: Add label filtering if needed
        # cql = f"ancestor={parent_page_id} and type=page and label=pir"
        # CONFLUENCE_PIR_LABEL = os.getenv("CONFLUENCE_PIR_LABEL")
        # if CONFLUENCE_PIR_LABEL:
        #     cql = f"ancestor={parent_page_id} and type=page and label={CONFLUENCE_PIR_LABEL}"

        # Load data using CQL
        documents = reader.load_data(cql=cql, include_attachments=False, page_status='current')

        logger.info(f"Loaded {len(documents)} child documents from Confluence parent page ID {parent_page_id}.")
        return documents
    except Exception as e:
        logger.error(f"Failed to load documents from Confluence: {e}")
        return None
