import os
import logging
import chainlit as cl
from llama_index.core import Settings
from llama_index.core.schema import Document, QueryBundle, NodeWithScore
from llama_index.llms.openai_like import OpenAILike
from llama_index.llms.openai import OpenAI
from llama_index.core.postprocessor import SentenceTransformerRerank
from src.embedding import get_embedding_model
from src.indexing import get_vector_index
from src.retrieval import create_pir_retrieval_tool, get_advanced_query_engine, detect_pir_filters
from src.agent_config import create_pir_agent, LlamaIndexToolWrapper
from src.email_tool import create_email_tool
from src.data_loader import load_pirs_from_confluence, load_html_content, load_confluence_child_pages
from src.node_parser import PIRNodeParser
from langchain_openai import ChatOpenAI
from langchain_core.agents import AgentAction
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.output_parsers import <PERSON>r<PERSON><PERSON><PERSON><PERSON>arser
from typing import Dict, List, Any, Tuple
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)  # Define the logger object


@cl.on_chat_start
async def on_chat_start():
    """Initialize the application, load the persistent index, and set up the LangChain agent."""
    persist_dir = "./storage/pir_chroma_index"

    load_dotenv()

    # Using OpenAI-like embedding model EMBEDDING_MODEL_NAME, API_KEY and API_BASE_URL
    # defined in the environment variable to handle technical nuance found in PIRs.
    api_key = os.getenv("API_KEY")
    api_base = os.getenv("API_BASE_URL")

    # Notify user of initialization
    await cl.Message(content="Initializing application and loading index...").send()

    from llama_index.llms.openai_like import OpenAILike
    llamaindex_llm = OpenAILike(
        model="deepseek-r1-distill-qwen-32b-250120",
        api_base=api_base,
        api_key=api_key,
        max_tokens=4096,
        api_version="250120",
        temperature=0.1,
        is_chat_model=True
    )
    
    try:
        # Instantiate the LLM
        llm = ChatOpenAI(
            model="deepseek-r1-distill-qwen-32b-250120",
            openai_api_base=api_base,
            openai_api_key=api_key,
            temperature=0.1,
            disable_streaming=True
        )
        # Store the LLM in the session for RAG chain
        cl.user_session.set("llm_instance", llm)
    except Exception as e:
        await cl.Message(content=f"❌ **Error initializing LLM:** {e}").send()
        return

    # Set up embedding model
    embed_model = get_embedding_model()
    Settings.embed_model = embed_model
    Settings.llm = llamaindex_llm  # LLM for retrieval engine might be needed later

    # Check if mock data should be loaded
    LOAD_MOCK_DATA = os.getenv("LOAD_MOCK_DATA", "true").lower() == "true"
    nodes = []

    if LOAD_MOCK_DATA:
        # Load mock data from a file
        mock_file_path = "./tests/test_data/mock_pir_pages/mock_pir_INC12345678.html"  # Adjust the path as needed
        raw_content = load_html_content(mock_file_path)
        if raw_content:
            raw_doc = Document(text=raw_content)
            parser = PIRNodeParser()
            nodes = parser.get_nodes_from_documents([raw_doc])
            logger.info(f"Loaded and parsed {len(nodes)} nodes from mock data.")
        else:
            logger.error("Failed to load mock data.")
    else:
        # Load from Confluence
        parent_id = os.getenv("CONFLUENCE_PARENT_PAGE_ID")
        if not parent_id:
            logger.error("CONFLUENCE_PARENT_PAGE_ID not set in environment variables.")
            await cl.Message(content="❌ Error: CONFLUENCE_PARENT_PAGE_ID not set.").send()
            return

        await cl.Message(content=f"Loading child pages from Confluence parent ID: {parent_id}...").send()
        confluence_docs = await cl.make_async(load_pirs_from_confluence)([parent_id])
        if confluence_docs:
            parser = PIRNodeParser()  # Use custom parser
            nodes = parser.get_nodes_from_documents(confluence_docs)  # Process loaded LlamaIndex docs
            logging.info(f"Processed {len(nodes)} nodes from Confluence.")
            await cl.Message(content=f"✅ Loaded and processed {len(nodes)} pages from Confluence.").send()
        else:
            logging.warning(f"Failed to load from Confluence parent ID {parent_id} or no child documents found.")
            await cl.Message(content=f"⚠️ Failed to load documents from Confluence.").send()

    if not nodes:
        await cl.Message(content="❌ Error: No data loaded (mock or Confluence). Cannot build index.").send()
        return

    try:
        # Attempt to load the index
        index = get_vector_index(nodes=nodes if nodes else None, persist_dir=persist_dir)
    except ValueError as e:
        await cl.Message(content=f"❌ **Error:** Index not found at `{persist_dir}`. Please run `python run_pipeline.py --build` first.").send()
        return
    except Exception as e:
        await cl.Message(content=f"❌ **Error loading index:** {e}").send()
        return

    # Store the index in the session
    cl.user_session.set("vector_index", index)
    
    # Also store the configuration parameters for retrieval in the session
    cl.user_session.set("retrieval_config", {
        "similarity_top_k": 5,
        "rerank_top_n": 3
    })

    # Notify user that the index is loaded
    await cl.Message(content=f"✅ Index loaded. Ready to answer questions about indexed PIRs.").send()


def retrieveForRag(query: str, index=None, similarity_top_k: int = 5, rerank_top_n: int = 3) -> Tuple[str, List[NodeWithScore]]:
    """
    Retrieves and re-ranks relevant sections from PIR documents and returns both
    the context string and source nodes for citation.
    
    Args:
        query (str): The user's query
        index: The vector index to search (will be retrieved from session if None)
        similarity_top_k (int): Number of initial results to retrieve
        rerank_top_n (int): Number of results to keep after reranking
        
    Returns:
        Tuple[str, List[NodeWithScore]]: The context string and source nodes
    """
    logger.info(f"Retrieving content for: '{query}'")
    
    # Detect filters from query text
    filter_dict = detect_pir_filters(query)
    if filter_dict:
        logger.info(f"Detected filters: {filter_dict}")
    
    try:
        # Get advanced query engine with filters
        query_engine = get_advanced_query_engine(
            index, 
            filter_dict=filter_dict,
            similarity_top_k=similarity_top_k, 
            rerank_top_n=rerank_top_n
        )
        
        # Execute query
        response = query_engine.query(query)
        
        # Check if we got any results
        if not response.source_nodes:
            logger.warning("No source nodes found.")
            return "No relevant information found in PIR documents.", []
        
        # Format the context string
        context_str = "\n\n---\n\n".join([node.get_content() for node in response.source_nodes])
        logger.info(f"Retrieved context from {len(response.source_nodes)} sources.")
        
        # Return both the context string and source nodes
        return context_str, response.source_nodes
        
    except Exception as e:
        logger.error(f"Query engine failed: {e}", exc_info=True)
        return f"Error: Failed to retrieve documents ({e})", []


@cl.on_message
async def on_message(message: cl.Message):
    """Handle user messages using a direct LangChain Expression Language (LCEL) chain for RAG."""
    # Get the vector index from the session
    index = cl.user_session.get("vector_index")
    if not index:
        await cl.Message(content="Error: Vector index not initialized. Please restart the chat.").send()
        return
    
    # Get the LLM from the session
    llm = cl.user_session.get("llm_instance")
    if not llm:
        # Re-initialize if needed
        api_key = os.getenv("API_KEY")
        api_base = os.getenv("API_BASE_URL")
        llm = ChatOpenAI(
            model="deepseek-r1-distill-qwen-32b-250120",
            openai_api_base=api_base,
            openai_api_key=api_key,
            temperature=0.1
        )
    
    # Get retrieval configuration from session
    retrieval_config = cl.user_session.get("retrieval_config", {
        "similarity_top_k": 5,
        "rerank_top_n": 3
    })
    
    query_text = message.content
    
    # Create a placeholder message
    msg = cl.Message(content="")
    await msg.send()
    
    try:
        # Define how to get context with source nodes
        def get_context_and_sources(query: str):
            context, source_nodes = retrieveForRag(
                query, 
                index=index, 
                similarity_top_k=retrieval_config.get("similarity_top_k", 5),
                rerank_top_n=retrieval_config.get("rerank_top_n", 3)
            )
            # Store source_nodes in a global variable or return both
            return {"context": context, "source_nodes": source_nodes}
        
        # Define the prompt template for RAG
        template = """Answer the question based ONLY on the following context:
        {context}

        Question: {question}

        Answer:"""
        prompt = ChatPromptTemplate.from_template(template)

        # Define the LCEL chain
        rag_chain = (
            RunnablePassthrough.assign(
                retrieval_output=lambda x: get_context_and_sources(x["question"])
            )
            .assign(
                context=lambda x: x["retrieval_output"]["context"]
            )
            | prompt
            | llm
            | StrOutputParser()
        )
        
        # Invoke the chain
        chain_output = await rag_chain.ainvoke({"question": query_text})
        
        # Get source nodes from the execution
        context_and_sources = get_context_and_sources(query_text)
        source_nodes = context_and_sources["source_nodes"]
        
        # Format sources for display
        source_elements = []
        for i, node in enumerate(source_nodes):
            metadata = node.node.metadata
            text_preview = node.node.get_content()[:350].strip() + "..."
            content = f"**Source {i + 1}** (Score: {node.score:.2f})\n*Section:* {metadata.get('section_title', 'N/A')}\n\n{text_preview}"
            source_elements.append(cl.Text(content=content, name=f"Source_{i + 1}", display="inline"))
        
        # Update the message with the response and sources
        msg.content = chain_output
        msg.elements = source_elements
        await msg.update()

    except Exception as e:
        logger.error(f"RAG chain execution failed: {e}", exc_info=True)
        await msg.update(content=f"Error processing query: {e}")


if __name__ == "__main__":
    cl.run()
