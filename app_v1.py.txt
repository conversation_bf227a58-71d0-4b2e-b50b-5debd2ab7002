import os
import logging
import chainlit as cl
from llama_index.core import Settings
from llama_index.core.schema import Document, QueryBundle
from llama_index.llms.openai_like import OpenAILike
from llama_index.llms.openai import OpenAI
from llama_index.core.postprocessor import SentenceTransformerRerank
from src.embedding import get_embedding_model
from src.indexing import get_vector_index
from src.retrieval import create_pir_retrieval_tool
from src.agent_config import create_pir_agent, LlamaIndexToolWrapper
from src.email_tool import create_email_tool
from src.data_loader import load_pirs_from_confluence, load_html_content, load_confluence_child_pages
from src.node_parser import PIRNodeParser
from langchain_openai import ChatOpenAI
from langchain_core.agents import AgentAction
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)  # Define the logger object


@cl.on_chat_start
async def on_chat_start():
    """Initialize the application, load the persistent index, and set up the LangChain agent."""
    persist_dir = "./storage/pir_chroma_index"

    load_dotenv()

    # Using OpenAI-like embedding model EMBEDDING_MODEL_NAME, API_KEY and API_BASE_URL
    # defined in the environment variable to handle technical nuance found in PIRs.
    api_key = os.getenv("API_KEY")
    api_base = os.getenv("API_BASE_URL")

    # Notify user of initialization
    await cl.Message(content="Initializing application and loading index...").send()

    from llama_index.llms.openai_like import OpenAILike
    llamaindex_llm = OpenAILike(
        model="deepseek-r1-distill-qwen-32b-250120",
        api_base=api_base,
        api_key=api_key,
        max_tokens=4096,
        api_version="250120",
        temperature=0.1,
        is_chat_model=True
    )
    
    try:
        # Instantiate the agent's LLM
        llm = ChatOpenAI(
            model="deepseek-r1-distill-qwen-32b-250120",
            openai_api_base=api_base,
            openai_api_key=api_key,
            temperature=0.1
        )
    except Exception as e:
        await cl.Message(content=f"❌ **Error initializing LLM:** {e}").send()
        return

    # Set up embedding model
    embed_model = get_embedding_model()
    Settings.embed_model = embed_model
    Settings.llm = llamaindex_llm  # LLM for retrieval engine might be needed later

    # Check if mock data should be loaded
    LOAD_MOCK_DATA = os.getenv("LOAD_MOCK_DATA", "true").lower() == "true"
    nodes = []

    if LOAD_MOCK_DATA:
        # Load mock data from a file
        mock_file_path = "./tests/test_data/mock_pir_pages/mock_pir_INC12345678.html"  # Adjust the path as needed
        raw_content = load_html_content(mock_file_path)
        if raw_content:
            raw_doc = Document(text=raw_content)
            parser = PIRNodeParser()
            nodes = parser.get_nodes_from_documents([raw_doc])
            logger.info(f"Loaded and parsed {len(nodes)} nodes from mock data.")
        else:
            logger.error("Failed to load mock data.")
    else:
        # Load from Confluence
        parent_id = os.getenv("CONFLUENCE_PARENT_PAGE_ID")
        if not parent_id:
            logger.error("CONFLUENCE_PARENT_PAGE_ID not set in environment variables.")
            await cl.Message(content="❌ Error: CONFLUENCE_PARENT_PAGE_ID not set.").send()
            return

        await cl.Message(content=f"Loading child pages from Confluence parent ID: {parent_id}...").send()
        confluence_docs = await cl.make_async(load_confluence_child_pages)(parent_id)
        if confluence_docs:
            parser = PIRNodeParser()  # Use custom parser
            nodes = parser.get_nodes_from_documents(confluence_docs)  # Process loaded LlamaIndex docs
            logging.info(f"Processed {len(nodes)} nodes from Confluence.")
            await cl.Message(content=f"✅ Loaded and processed {len(nodes)} pages from Confluence.").send()
        else:
            logging.warning(f"Failed to load from Confluence parent ID {parent_id} or no child documents found.")
            await cl.Message(content=f"⚠️ Failed to load documents from Confluence.").send()

    if not nodes:
        await cl.Message(content="❌ Error: No data loaded (mock or Confluence). Cannot build index.").send()
        return

    try:
        # Attempt to load the index
        index = get_vector_index(nodes=nodes if nodes else None, persist_dir=persist_dir)
    except ValueError as e:
        await cl.Message(content=f"❌ **Error:** Index not found at `{persist_dir}`. Please run `python run_pipeline.py --build` first.").send()
        return
    except Exception as e:
        await cl.Message(content=f"❌ **Error loading index:** {e}").send()
        return

    # Store the index in the session
    cl.user_session.set("vector_index", index)
    # Create the auto-retriever
    from src.retrieval import get_auto_retriever
    retriever = get_auto_retriever(index)

    # Store the retriever in session
    cl.user_session.set("pir_retriever", retriever)

    # Create the email tool
    email_tool = create_email_tool()

    query_tool = create_pir_retrieval_tool(index, similarity_top_k=5, rerank_top_n=1)
    # Extract name and description from the LlamaIndex tool's metadata
    tool_name = query_tool.metadata.name
    tool_description = query_tool.metadata.description
    # Wrap the LlamaIndex FunctionTool, passing name and description explicitly
    langchain_compatible_tool = LlamaIndexToolWrapper(tool=query_tool, name=tool_name, description=tool_description)
    

    # Define the tools list
    tools = [langchain_compatible_tool, email_tool]
    logger.info(f"Tools list: {tools}")

    # Create the agent executor
    agent_executor = create_pir_agent(llm, tools)

    # Store the agent executor in the session
    cl.user_session.set("agent_executor", agent_executor)

    # Notify user that the index and agent are loaded
    await cl.Message(content=f"✅ Index and agent loaded. Ready to answer questions about indexed PIRs and prepare email drafts.").send()


@cl.on_message
async def on_message(message: cl.Message):
    """Handle user messages using the LangChain agent and display sources from intermediate steps."""
    agent_executor = cl.user_session.get("agent_executor")
    if not agent_executor:
        await cl.Message(content="Error: Agent executor not initialized. Please restart the chat.").send()
        return

    # Get the retriever if we need direct retrieval without the agent
    pir_retriever = cl.user_session.get("pir_retriever")

    query_text = message.content
    msg = cl.Message(content="")  # Create placeholder message
    await msg.send()

    try:
        if pir_retriever:
            try:
                # Directly retrieve nodes using the VectorIndexAutoRetriever
                retrieved_nodes = await cl.make_async(pir_retriever.retrieve)(query_text)
                
                if not retrieved_nodes:
                    msg.content = "No relevant information found for your query."
                    await msg.update()
                    return
                
                # Apply reranking to improve relevance
                reranker = SentenceTransformerRerank(
                    top_n=3,
                    model="cross-encoder/ms-marco-MiniLM-L-6-v2"
                )
                # Create a QueryBundle for the reranker
                query_bundle = QueryBundle(query_text)
                # Apply reranking to retrieved nodes
                reranked_nodes = reranker.postprocess_nodes(
                    retrieved_nodes, 
                    query_bundle=query_bundle
                )
                logger.info(f"Retrieved {len(retrieved_nodes)} nodes, reranked to {len(reranked_nodes)} nodes.")
                
                # Get the LLM from the Settings
                llm = Settings.llm if Settings.llm else OpenAILike(
                    model="deepseek-r1-distill-qwen-32b-250120",
                    api_base=os.getenv("API_BASE_URL"),
                    api_key=os.getenv("API_KEY"),
                    max_tokens=4096,
                    api_version="250120",
                    temperature=0.1,
                    is_chat_model=True,
                    chat_completion_path="/api/v3/chat/completions"
                )
                
                # Create context string from reranked nodes
                context_str = "\n\n".join([node.get_content() for node in reranked_nodes])
                
                # Create a simple prompt
                prompt = f"Context:\n{context_str}\n\nQuestion: {query_text}\nAnswer:"
                
                # Call LLM to synthesize response
                response_obj = await cl.make_async(llm.complete)(prompt)
                
                # Extract the text from the CompletionResponse object
                if hasattr(response_obj, 'text'):
                    response_text = response_obj.text
                elif hasattr(response_obj, 'content'):
                    response_text = response_obj.content
                elif hasattr(response_obj, 'message'):
                    response_text = response_obj.message
                elif isinstance(response_obj, str):
                    response_text = response_obj
                else:
                    logger.warning(f"Unexpected response type: {type(response_obj)}")
                    response_text = str(response_obj)
                
                # Format sources from reranked nodes
                source_elements = []
                for i, node in enumerate(reranked_nodes):
                    metadata = node.node.metadata
                    text_preview = node.node.get_content()[:350].strip() + "..."
                    content = f"**Source {i + 1}**\n*Section:* {metadata.get('section_title', 'N/A')}\n\n{text_preview}"
                    source_elements.append(cl.Text(content=content, name=f"Source_{i + 1}", display="inline"))
                
                # Send the synthesized response
                msg.content = response_text
                msg.elements = source_elements
                await msg.update()
            except Exception as inner_e:
                logger.error(f"Direct retrieval failed: {inner_e}", exc_info=True)
                await msg.update(content=f"Error processing query: {inner_e}")
        else:
            await msg.update(content=f"Error processing query: {e}")

    except Exception as e:
        await cl.Message(content=f"Error processing query: {e}").send()


if __name__ == "__main__":
    cl.run()
