import os
import argparse
import shutil
import logging

# ---> CONFIGURE LOGGING FIRST <---
# Set level=logging.DEBUG here to configure the root logger AND its default handler
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - [%(name)s] %(message)s',
    force=True # Add force=True to override any potential default config by libraries
)
# Get the logger for this specific file (app.py)
logger = logging.getLogger(__name__)
logger.debug("Root logger configured for DEBUG level in run_pipeline.py")
# ---------------------------------

from src.data_loader import load_html_content
from src.node_parser import PIRNodeParser
from src.embedding import get_embedding_model
from src.indexing import get_vector_index
# Removed: from src.retrieval import get_base_query_engine
# from src.retrieval import create_pir_query_tool # Added
from src.retrieval import create_pir_retrieval_tool # Added
from src.agent_config import create_pir_agent, LlamaIndexToolWrapper # Added Wrapper
from llama_index.core.schema import Document
from llama_index.core import Settings, StorageContext, load_index_from_storage, VectorStoreIndex
from llama_index.llms.openai_like import OpenAILike
from llama_index.core.extractors import KeywordExtractor # Added
from langchain_openai import ChatOpenAI
from src.enrichment import enrich_nodes_with_llm # Added
from dotenv import load_dotenv # Added
from llama_index.core.llms.llm import LLM
from llama_index.core import Settings
import langchain

# Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main(build_new: bool, query_text: str): # Added query_text argument
    """
    Executes the pipeline for loading, parsing, indexing, and querying using a LangChain agent.

    Args:
        build_new (bool): Whether to build a new index or load an existing one.
        query_text (str): The query to execute via the agent.
    """
    # Load environment variables (needed for API keys)
    load_dotenv()
    mock_file_path = "tests/test_data/mock_pir_pages/mock_pir_INC12345678.md"
    persist_dir = "./storage/pir_chroma_index"
    nodes = None

# Configure global settings BEFORE loading/building index
    logging.info("Configuring global LlamaIndex settings...")
    
    # Configure LLM for LlamaIndex Settings (potentially used by tools/retrievers)
    api_key = os.getenv("API_KEY")
    api_base = os.getenv("API_BASE_URL")

    if not api_key or not api_base:
        logging.error("API_KEY or API_BASE_URL not found in environment variables. Cannot initialize LLM.")
        return
    
    llamaindex_llm = OpenAILike(
        model=os.getenv("MODEL_NAME", "deepseek-r1-distill-qwen-32b-250120"), # Use env var or default
        api_base=api_base,
        api_key=api_key,
        max_tokens=4096,
        api_version=os.getenv("API_VERSION", "250120"), # Use env var or default
        temperature=0.1,
        is_chat_model=True
    )
    logging.info("Getting embedding model...")
    embed_model = get_embedding_model() # Using HuggingFaceEmbedding("all-MiniLM-L6-v2")
    Settings.embed_model = embed_model
    Settings.llm = llamaindex_llm
    logging.info(f"Settings.embed_model set to: {type(Settings.embed_model)}")

    if build_new:
        logging.info(f"Attempting to build index from {mock_file_path}...")
        html_content = load_html_content(mock_file_path)
        if not html_content:
            logging.error("Failed to load mock file")
            return

        # Wrap raw HTML content in a LlamaIndex Document for the parser
        file_basename = os.path.basename(mock_file_path)
        raw_doc = Document(text=html_content, metadata={'file_name': file_basename})

        parser = PIRNodeParser()
        nodes = parser.get_nodes_from_documents([raw_doc])        

        # Instantiate the LLM needed for extraction
        extraction_llm = llamaindex_llm
        # service_context = ServiceContext.from_defaults(embed_model=embed_model, llm=extraction_llm)

        # Define extractors
        extractors = [KeywordExtractor(llm=extraction_llm, keywords=5)]  # Example: extract 5 keywords

        # Call enrich_nodes_with_llm
        nodes = enrich_nodes_with_llm(nodes, extraction_llm, extractors)
        if not nodes:
            logging.error("Node parsing failed")
            return
        logging.info(f"Parsed {len(nodes)} nodes from {file_basename}.")
        
        # Debug node content
        logging.debug("====== NODE CONTENT DEBUG ======")
        for i, node in enumerate(nodes):
            logging.debug(f"Node {i+1}/{len(nodes)} - ID: {node.id_}")
            logging.debug(f"Metadata: {node.metadata}")
            content_preview = node.text[:5000] + "..." if len(node.text) > 5000 else node.text
            logging.debug(f"Content: {content_preview}")
        logging.debug("================================")

        if os.path.exists(persist_dir):
            logging.warning(f"Removing existing index at {persist_dir}")
            shutil.rmtree(persist_dir)
        logging.info(f"Building index and persisting to: {persist_dir}")

    logging.info("Loading or building vector index...")
    try:
        logging.info(f"Attempting to get index (build_new={build_new})...")
        # Pass build_new flag to potentially influence logic inside get_vector_index if needed,
        # although current logic relies on nodes presence.
        index = get_vector_index(nodes=nodes, persist_dir=persist_dir)
        logging.info("Successfully got index.")
    except ValueError as e:
        logging.error(f"Failed to get index: {e}")
        return
    
    # --- Agent Setup ---
    logging.info("Setting up LangChain agent...")

    # 1. Instantiate LangChain LLM for the agent
    try:
        langchain_llm = ChatOpenAI(
            model=os.getenv("MODEL_NAME", "deepseek-r1-distill-qwen-32b-250120"),  # Use same model as Settings
            openai_api_base=api_base,
            openai_api_key=api_key,
            temperature=0.1,
            disable_streaming=True
        )
    except Exception as e:
        logging.error(f"Failed to initialize LangChain LLM: {e}")
        return

    # 2. Create the query tool
    # Note: create_pir_query_tool might internally use Settings.llm if configured for reranking etc.
    query_tool = create_pir_retrieval_tool(index, similarity_top_k=5, rerank_top_n=1)
    # Extract name and description from the LlamaIndex tool's metadata
    tool_name = query_tool.metadata.name
    tool_description = query_tool.metadata.description
    # Wrap the LlamaIndex FunctionTool, passing name and description explicitly
    langchain_compatible_tool = LlamaIndexToolWrapper(tool=query_tool, name=tool_name, description=tool_description)
    tools = [langchain_compatible_tool]
    logging.info(f"Tools created: {[t.name for t in tools]}") # Use .name now

    # 3. Create the agent executor
    agent_executor = create_pir_agent(langchain_llm, tools)
    logging.info("LangChain agent created successfully.")
    # --- End Agent Setup ---
    
    # --- Agent Execution ---
    logging.info(f"Executing agent with query: {query_text}")
    print(f"\nExecuting Agent Query: {query_text}\n")

    try:
        # Invoke the agent
        response = agent_executor.invoke({"input": query_text})

        # Extract the final answer
        agent_answer = response.get('output', 'Agent error: No output generated.')

        print(f"\nAgent Final Answer:\n{agent_answer}")

        # Optionally print intermediate steps for debugging
        intermediate_steps = response.get('intermediate_steps', [])
        if intermediate_steps:
            print("\n--- Intermediate Steps ---")
            for step in intermediate_steps:
                action, observation = step
                print(f"Action: {action.tool}")
                print(f"Action Input: {action.tool_input}")
                # Be careful printing observation, might be large/complex
                obs_preview = str(observation)[:500] + "..." if len(str(observation)) > 500 else str(observation)
                print(f"Observation: {obs_preview}")
                print("--------------------------")

    except Exception as e:
        logging.error(f"Error invoking agent: {e}")
        print(f"\nError during agent execution: {e}")
    # --- End Agent Execution ---


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the PIR indexing and agent querying pipeline.")
    parser.add_argument("--build", action="store_true", help="Build a new index instead of loading an existing one.")
    parser.add_argument("--query", type=str, default="What specific actions were arising from incident INC12345678, as detailed in the Actions Arising section?", help="The query to ask the agent.")
    args = parser.parse_args()
    main(args.build, args.query)
