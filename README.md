# PIR Assistant - AI-Powered Retrieval-Augmented Generation

## Overview

The PIR Assistant is an AI-powered Retrieval-Augmented Generation (RAG) system designed to help incident responders and operations teams quickly access information from Post-Incident Review (PIR) documents. The system uses semantic search and large language models to understand user queries, retrieve relevant information from PIR documents, and generate comprehensive responses.

## Key Features

- **Confluence Integration**: Automatically load PIR documents from Confluence
- **Smart Document Processing**: Process PIR documents into searchable sections and tables
- **Semantic Search**: Use vector-based similarity search to find relevant information
- **AI-Powered Responses**: Generate coherent, context-aware responses using LLM reasoning
- **User-Friendly Interface**: Chainlit-based chat interface with support for SVG diagrams
- **Email Tool**: Ability to send email summaries of PIR information

## Architecture

The system follows a modular architecture with the following components:

1. **Data Loading**: `ConfluenceLoader` fetches PIR documents from Confluence
2. **Document Processing**: `PIRProcessor` parses and structures PIR content
3. **Embedding Creation**: `Embedding<PERSON>and<PERSON>` generates vector embeddings for text 
4. **Vector Storage**: Indexed PIR content is stored in ChromaDB
5. **Query Processing**: `QueryVectorizer` prepares user queries for semantic search
6. **Retrieval**: `SemanticSearchEngine` finds relevant PIR content based on queries
7. **Reasoning**: `CustomReasoningLLM` adds thinking capability to the LLM responses
8. **Agent**: `PIRAgent` coordinates tools and LLM for performing tasks
9. **UI**: `ChainlitApp` provides a chat interface for interacting with the system

## Setup and Installation

### Prerequisites

- Python 3.10 or higher
- API access to a compatible embedding model endpoint
- API access to a compatible LLM/reasoning model endpoint
- ChromaDB for vector storage
- Confluence API credentials (for loading PIR documents)

### Environment Setup

1. Clone this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Create a `.env` file with the following variables:
   ```
   # Confluence API settings
   CONFLUENCE_URL=https://your-company.atlassian.net
   CONFLUENCE_USERNAME=your-username
   CONFLUENCE_API_TOKEN=your-api-token

   # Embedding API settings
   CUSTOM_EMBED_API_ENDPOINT=https://your-embedding-api-endpoint
   CUSTOM_EMBED_API_KEY=your-embedding-api-key

   # Reasoning LLM API settings
   REASONING_ENDPOINT=https://your-reasoning-llm-endpoint
   REASONING_API_KEY=your-reasoning-api-key

   # Vector DB settings
   VECTOR_DB_PATH=./data/vector_db
   ```

## Usage

### Data Indexing

To load and index PIR documents:

```bash
python src/indexer.py
```

This will:
1. Fetch PIR documents from Confluence
2. Process the documents into searchable chunks
3. Generate embeddings for each chunk
4. Store the embeddings in the vector database

### Starting the Chat Interface

To launch the Chainlit chat interface:

```bash
chainlit run src/chainlit_app.py
```

This will start a web server (typically on http://localhost:8000) where you can interact with the PIR Assistant.

### Interacting with the PIR Assistant

Within the chat interface, you can:
- Ask questions about past incidents
- Request specific information from PIR documents
- View reasoning steps behind responses
- Request email summaries of PIR information

## Development and Testing

### Running Tests

To run the test suite:

```bash
pytest tests/
```

### Project Structure

- `src/` - Core application code
  - `confluence_loader.py` - Retrieves PIR documents from Confluence
  - `pir_processor.py` - Processes PIR documents into structured format
  - `embedding_handler.py` - Manages vector embeddings
  - `indexer.py` - Orchestrates document indexing
  - `query_vectorizer.py` - Prepares queries for vector search
  - `semantic_search.py` - Performs semantic search over indexed content
  - `custom_reasoning_llm.py` - Enhances LLM with reasoning capabilities
  - `pir_agent.py` - Implements ReAct agent with tools
  - `chainlit_app.py` - Provides the user interface
- `tests/` - Test suite
  - `test_e2e.py` - End-to-end and unit tests
- `data/` - (Created during operation) Stores vector database
- `.env` - Environment variables and configuration

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues.

When contributing, please:
1. Follow the existing code style and documentation patterns
2. Add tests for new functionality
3. Update documentation to reflect changes
4. Ensure all tests pass before submitting

## License

[MIT License](LICENSE)
