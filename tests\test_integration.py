import pytest
import pytest_asyncio
from unittest.mock import AsyncMock

from llama_index.core import Document, VectorStoreIndex, ServiceContext
from llama_index.core.query_engine import BaseQueryEngine
from llama_index.core.tools import BaseTool
from langchain.agents import AgentExecutor

from src.node_parser import PIRNodeParser
from src.indexing import get_vector_index
from src.retrieval import get_advanced_query_engine
from src.email_tool import create_email_tool
from src.agent_config import create_pir_agent
from src.data_loader import load_html_content
from src.retrieval import create_pir_query_tool


@pytest.mark.asyncio
async def test_data_pipeline_integration(tmp_path):
    """Test data pipeline integration: Loader -> Parser -> Nodes -> Index -> QueryEngine."""
    # 1. Load mock HTML
    html_content = "<h1>Mock HTML Content</h1><p>This is a test document.</p><p>Key information: Incident PIR RAG Implementation Plan.</p>"
    documents = [Document(text=html_content)]

    # 2. Parse using PIRNodeParser
    node_parser = PIRNodeParser()
    nodes = node_parser.get_nodes_from_documents(documents)
    print(f"Number of nodes: {len(nodes)}")

    # 3. Create ServiceContext (mock embedding model)
    mock_embedding_model = AsyncMock()
    mock_embedding_model.aget_text_embedding.return_value = [0.1, 0.2, 0.3]  # Mock embedding vector
    from llama_index.core import Settings
    from llama_index.core.embeddings import MockEmbedding

    class MyMockEmbedding(MockEmbedding):
        def embed(self, text):
            return mock_embedding_model.aget_text_embedding(text)

    Settings.embed_model = MyMockEmbedding(embed_dim=3)

    # 4. Build/Load index
    index = await get_vector_index(nodes.copy(), persist_dir=str(tmp_path))

    # 5. Create advanced query engine
    query_engine = get_advanced_query_engine(index)

    # 6. Run query and assert response
    query = "What is this document about?"
    response = await query_engine.aquery(query)
    assert "Incident PIR RAG Implementation Plan" in str(response)
    assert len(response.source_nodes) > 0
    assert "Mock HTML Content" in response.source_nodes[0].node.get_content()


@pytest.mark.asyncio
async def test_agent_tool_integration(tmp_path):
    """Test agent-tool integration: Agent -> Tool -> Index."""
    # 1. Setup mock index (or use index from previous test)
    html_content = "<h1>Mock HTML Content</h1><p>This is a test document.</p><p>Key information: Incident PIR RAG Implementation Plan.</p>"
    documents = [Document(text=html_content)]
    node_parser = PIRNodeParser()
    nodes = node_parser.get_nodes_from_documents(documents)
    print(f"Number of nodes: {len(nodes)}")
    mock_embedding_model = AsyncMock()
    mock_embedding_model.aget_text_embedding.return_value = [0.1, 0.2, 0.3]  # Mock embedding vector
    from llama_index.core import Settings
    from llama_index.core.embeddings import MockEmbedding

    class MyMockEmbedding(MockEmbedding):
        def embed(self, text):
            return mock_embedding_model.aget_text_embedding(text)

    Settings.embed_model = MyMockEmbedding(embed_dim=3)
    index = await get_vector_index(nodes.copy(), persist_dir=str(tmp_path))

    # 2. Create tools
    query_tool = create_dynamic_pir_query_tool(index)
    email_tool = create_email_tool()

    # 3. Get mock LLM (can simulate tool calls/responses)
    mock_llm = AsyncMock()
    # Simulate tool call for query tool
    mock_llm.return_value.choices[0].message.content = "I should use the query tool to answer this."
    mock_llm.return_value.choices[0].message.tool_calls = [AsyncMock()]

    # 4. Create agent
    agent_executor = create_pir_agent(tools=[query_tool, email_tool], llm=mock_llm)

    # 5. Invoke agent with query requiring query tool
    query = "What is this document about?"
    response = await agent_executor.ainvoke({"input": query})
    assert "Incident PIR RAG Implementation Plan" in str(response)
    # TODO: Assert intermediate steps show structured observation from tool

    # 6. Invoke agent with query requiring email tool
    query = "Send an <NAME_EMAIL> about the incident."
    response = await agent_executor.ainvoke({"input": query})
    assert "email" in str(response).lower()  # Check for email action
    # TODO: Assert intermediate steps show email tool call