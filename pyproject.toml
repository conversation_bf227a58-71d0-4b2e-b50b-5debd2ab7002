[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["my-assistant-v3"]

[project]
name = "my-assistant-v3"
version = "0.1.0"
description = "RAG Application for searching Incident Post-Implementation Review documents in Confluence."
authors = [
    { name = "tomboy40", email = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.11,<3.12" # Specify exact minor version compatibility
license = { text = "MIT" } # Or choose your preferred license

dependencies = [
    # Core Application Dependencies (mirroring requirements.txt)
    "langchain>=0.1.0,<0.2.0",
    "langchain-community>=0.0.20",
    "langchain-openai>=0.1.0",
    "llama-index>=0.12.0,<0.13.0",
    "llama-index-readers-confluence",
    "llama-index-vector-stores-chroma",
    "chainlit>=1.0.0",
    "chromadb>=0.4.22",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "python-dotenv>=1.0.0",
    "numpy>=1.26.0",
    "pydantic>=1.10,<3.0.0",
    "tenacity>=8.2.0",
    "email-validator>=2.2.0",
    "lxml>=5.3.1",
    "llama-index-llms-openai>=0.1.31",
    "llama-index-embeddings-openai>=0.1.11",
    "sentence-transformers>=4.0.2",
    "llama-index-llms-openai-like==0.3.4",
]

[project.optional-dependencies]
# Development and Testing Dependencies
dev = [    
    "pytest>=8.3.5",
    "pytest-mock>=3.14.0",
    # Add other dev tools like linters (ruff, mypy), formatters (black) if desired    
    "ruff>=0.11.3",
    "mypy>=1.15.0",
    "black>=25.1.0",
]

[project.urls]
Homepage = "https://github.com/tomboy40/incident-pir-rag-app" # Optional: Link to your repository
Repository = "https://github.com/tomboy40/incident-pir-rag-app" # Optional

[dependency-groups]
dev = [
    "pytest-asyncio>=0.26.0",
]

# Tool Configurations (Optional but recommended)

# Example: Ruff linter configuration (if you add ruff to dev dependencies)
[tool.ruff]
line-length = 88
select = ["E", "F", "W", "I"] # Example selection: pycodestyle errors/warnings, pyflakes, isort

# Example: Mypy configuration (if you add mypy to dev dependencies)
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true # Can be helpful initially

# Example: Black formatter configuration (if you add black to dev dependencies)
[tool.black]
line-length = 88
target-version = ['py311']
