from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.base.embeddings.base import BaseEmbedding
import os
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)

def get_embedding_model() -> BaseEmbedding:
    """
    Initializes and returns an embedding model using OpenAI-like integration.

    Returns:
        BaseEmbedding: An instance of the embedding model.

    Raises:
        ValueError: If the API key is missing.
        Exception: If the embedding model fails to initialize.
    """
    load_dotenv()

    # Using OpenAI-like embedding model EMBEDDING_MODEL_NAME, API_KEY and API_BASE_URL
    # defined in the environment variable to handle technical nuance found in PIRs.
    api_key = os.getenv("API_KEY")
    api_base = os.getenv("API_BASE_URL")

    if not api_key:
        logger.error("API_KEY not found in environment variables.")
        raise ValueError("Missing OpenAI API Key")

    try:
        model = OpenAIEmbedding(
            api_base=api_base,
            api_key=api_key,
            model_name="doubao-embedding-text-240515"  # Specify the embedding model
        )
    except Exception as e:
        logger.error(f"Failed to initialize OpenAIEmbedding: {e}")
        raise

    logger.info(f"Initialized embedding model: {model.model_name}")
    return model
