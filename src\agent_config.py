import logging
from typing import List

# Import necessary LangChain components
from langchain.agents import Agent<PERSON><PERSON>cut<PERSON>, create_react_agent
from langchain_core.prompts import PromptTemplate
import asyncio
from typing import Any # Added for type hinting
from langchain_core.tools import BaseTool, ToolException
from langchain_openai import Chat<PERSON>penAI
from llama_index.core.tools import FunctionTool # Import FunctionTool for type hinting
# from langchain import hub # Not strictly needed if defining prompt inline, but good practice

logger = logging.getLogger(__name__) # Use logger configured in main script


from pydantic.v1 import BaseModel, Field, root_validator # Import pydantic v1 components

# Wrapper class to make LlamaIndex tools compatible with Langchain's expected interface
class LlamaIndexToolWrapper(BaseTool):
    """Wraps a LlamaIndex FunctionTool to be compatible with Langchain BaseTool interface."""
    tool: FunctionTool # The LlamaIndex tool to wrap
    # name and description are now passed directly during initialization

    def _run(self, tool_input: str, **kwargs: Any) -> Any:
        """Use this tool."""
        # LlamaIndex FunctionTool expects the first arg as input usually
        # Ensure the input is a string if the underlying tool expects it
        if not isinstance(tool_input, str):
             tool_input = str(tool_input)
        try:
            # Execute the wrapped tool's function
            return self.tool.fn(tool_input)
        except Exception as e:
            raise ToolException(f"Error in wrapped LlamaIndex tool '{self.name}': {e}") from e

    async def _arun(self, tool_input: str, **kwargs: Any) -> Any:
        """Use this tool asynchronously."""
        # LlamaIndex FunctionTool expects the first arg as input usually
        # Ensure the input is a string if the underlying tool expects it
        if not isinstance(tool_input, str):
             tool_input = str(tool_input)
        try:
            # Execute the wrapped tool's async function if available, otherwise run sync in executor
            if asyncio.iscoroutinefunction(self.tool.async_fn):
                 return await self.tool.async_fn(tool_input)
            else:
                 # Fallback to sync execution if no async version
                 return await asyncio.get_event_loop().run_in_executor(None, self.tool.fn, tool_input)
        except Exception as e:
            raise ToolException(f"Error in wrapped LlamaIndex tool '{self.name}' (async): {e}") from e
    # Removed Config class as name/description are passed directly now


# Define the strict ReAct prompt template for PIR document interaction
REACT_PIR_PROMPT_TEMPLATE = """
**Role:** Assistant specialized in answering questions using ONLY information from Post-Incident Review (PIR) documents via available tools.
**Constraint:** Base your final answer *strictly* on the information provided in the 'Observation' from the tools. Do NOT use prior knowledge.
**Tool Usage:** Tools available: {tools}
**Tool Descriptions:** {tool_descriptions}
The 'PIR_Retriever_Tool' provides relevant text context from PIR documents.

**Response Format:**
Question: the input question you must answer
Thought: Your reasoning to use a tool.
Action: One of [{tool_names}]
Action Input: The input for the action.
Observation: The text context retrieved by the Action.
Thought: Based *only* on the previous Observation, I can now answer the question or state the information is unavailable.
Final Answer: Your synthesized answer based *only* on the Observation. If the Observation indicates no relevant information or an error, state that clearly (e.g., "Based on the available PIR documents, the requested information was not found." or "An error occurred during retrieval."). Do not mention 'sources' or metadata in the final answer, as they are handled separately.

Begin!

Question: {input}
Thought:{agent_scratchpad}
"""

def create_pir_agent(llm: ChatOpenAI, tools: List[BaseTool]) -> AgentExecutor:
    """
    Creates a LangChain ReAct agent specifically configured for querying PIR documents.
    This agent is designed to work with the simplified PIR_Retriever_Tool, which now returns
    a direct string context rather than a dictionary. This simplifies observation handling
    as the agent can directly use the text without needing to extract from a structured response.

    Args:
        llm (ChatOpenAI): The language model to use for the agent's reasoning.
        tools (List[BaseTool]): The list of tools (e.g., retrieval tools) available to the agent.

    Returns:
        AgentExecutor: The configured agent executor ready for execution.
    """
    logger.info("Creating PIR ReAct agent...")

    # 1. Create the prompt from the template
    prompt = PromptTemplate.from_template(REACT_PIR_PROMPT_TEMPLATE)
    logger.debug(f"Base prompt template created: {prompt}")

    # 2. Partially format the prompt with the names of the available tools.
    # This is crucial because the prompt template itself references {tool_names}.
    # Now use the .name and .description attributes directly from the (wrapped) tools
    tool_names = ", ".join([t.name for t in tools])
    tool_descriptions = "\n".join([f"{t.name}: {t.description}" for t in tools]) # Use newline for better readability in prompt
    prompt = prompt.partial(tools=tool_names, tool_descriptions=tool_descriptions, tool_names=tool_names)
    logger.info(f"Prompt partially formatted with tool names: {tool_descriptions}")
    logger.debug(f"Final prompt template after partial formatting: {prompt}")

    # 3. Create the ReAct agent
    # This binds the LLM, tools, and the specific prompt together.
    # No need to extract names separately here, create_react_agent handles it
    agent = create_react_agent(llm, tools, prompt)
    logger.info("ReAct agent created successfully.")

    # 4. Create the AgentExecutor
    # This wraps the agent and handles the execution loop, including intermediate steps and error handling.
    agent_executor = AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,  # Log internal agent steps for debugging
        handle_parsing_errors=True,  # Use default parsing error handling
        max_iterations=7,  # Limit the number of steps to prevent infinite loops
        return_intermediate_steps=True  # Crucial for observing the agent's reasoning and for source extraction in app.py
    )
    logger.info("AgentExecutor created with robust error handling and intermediate steps enabled.")

    return agent_executor
