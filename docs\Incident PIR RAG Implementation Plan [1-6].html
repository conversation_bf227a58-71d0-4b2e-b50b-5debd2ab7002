<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incident PIR RAG Implementation Plan</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 0.5em;
            color: #2563eb;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            color: #1e40af;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.3em;
        }
        h3 {
            font-size: 1.4em;
            margin-top: 1.2em;
            margin-bottom: 0.5em;
            color: #1e3a8a;
        }
        p {
            margin-bottom: 1em;
        }
        ul, ol {
            margin-bottom: 1em;
            padding-left: 2em;
        }
        li {
            margin-bottom: 0.5em;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
            padding: 0.2em 0.4em;
            background-color: #f1f5f9;
            border-radius: 3px;
        }
        pre {
            background-color: #f1f5f9;
            border-radius: 5px;
            padding: 1em;
            overflow-x: auto;
            margin-bottom: 1em;
        }
        .prompt-block {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            padding: 1.5em;
            margin-bottom: 2em;
        }
        .step-number {
            background-color: #2563eb;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        blockquote {
            border-left: 4px solid #cbd5e1;
            padding-left: 1em;
            margin-left: 0;
            margin-bottom: 1em;
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="container mx-auto">
        <h1 class="text-center">Incident PIR RAG Application Implementation Plan</h1>
        <p class="text-center mb-8">A step-by-step guide with AI IDE-compatible prompts for building a Retrieval-Augmented Generation application for Incident Post-Implementation Review documents</p>

        <div class="bg-blue-50 p-6 rounded-lg mb-8">
            <h2 class="text-xl font-bold text-blue-800 mt-0">Overview</h2>
            <p>This document provides a comprehensive implementation plan for developing an Incident PIR RAG application using Chainlit, LangChain ReAct agent, and LlamaIndex. The application will index Confluence PIR pages, retrieve relevant information based on user queries, and enable a ReAct agent to orchestrate actions.</p>
            <p>Each section includes:</p>
            <ul class="list-disc pl-6">
                <li>Step description</li>
                <li>AI IDE-compatible prompts</li>
                <li>Expected inputs and outputs</li>
                <li>Code examples and guidance</li>
            </ul>
            <p>The plan follows an incremental development approach, allowing for testing and validation at each step.</p>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">1</div>
                <h2 class="text-2xl font-bold m-0">Confluence Data Loading and Indexing</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement Confluence PIR Data Loader and Indexer

Create a Python module for loading and indexing Confluence PIR pages using the custom retrieval strategy described earlier. The implementation should:

1. Connect to Confluence API
2. Load PIR pages from a specified space and home page ID
3. Process HTML structure using the specialized approach for PIR documents
4. Generate embeddings using a custom OpenAI-compatible embedding model
5. Store embeddings and processed documents in a vector database
6. Implement error handling, logging, and performance optimizations

## Requirements:
- Use Python 3.9+
- Handle rate limiting for Confluence API
- Process HTML structure to extract metadata and sections
- Support batched processing for performance optimization
- Support incremental indexing (only process new/changed pages)
- Log progress and errors

## Expected Inputs:
- Confluence API credentials
- Space key and PIR home page ID
- Custom embedding model endpoint
- Vector database connection details

## Expected Outputs:
- Indexed PIR documents with structured metadata
- Vector representations stored in the database
- Progress logs and error reports

## Implementation Guidelines:
1. Create a `confluence_loader.py` module first
2. Implement a `pir_processor.py` module for HTML structure processing
3. Create an `embedding_handler.py` for vectorization
4. Implement a main `indexer.py` that orchestrates the process

Begin by implementing the `confluence_loader.py` module that connects to Confluence and retrieves PIR pages. Focus on pagination handling and error retries.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">confluence_loader.py</h4>
                    <pre><code class="language-python">
import logging
import time
from typing import Dict, List, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class ConfluenceLoader:
    """
    Loads Confluence PIR pages from a specified space and home page.
    Handles pagination, rate limiting, and connection retries.
    """
    
    def __init__(
        self,
        base_url: str,
        username: str,
        api_token: str,
        max_retries: int = 3,
        retry_backoff: float = 1.0,
        batch_size: int = 25
    ):
        """
        Initialize the Confluence loader with API credentials.
        
        Args:
            base_url: Confluence instance URL (e.g., https://yourcompany.atlassian.net/wiki)
            username: Confluence username (usually email)
            api_token: Confluence API token
            max_retries: Maximum number of retries for failed requests
            retry_backoff: Backoff factor for retries
            batch_size: Number of pages to fetch in each batch
        """
        self.base_url = base_url.rstrip('/')
        self.auth = (username, api_token)
        self.batch_size = batch_size
        self.logger = logging.getLogger(__name__)
        
        # Set up session with retry logic
        self.session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=retry_backoff,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        self.session.auth = self.auth
    
    def get_children_pages(self, space_key: str, parent_id: str) -> List[Dict[str, Any]]:
        """
        Get all child pages of a specific parent page in a space.
        
        Args:
            space_key: Confluence space key
            parent_id: ID of the parent page
            
        Returns:
            List of page objects
        """
        self.logger.info(f"Fetching children pages for parent ID {parent_id} in space {space_key}")
        
        all_pages = []
        start = 0
        
        while True:
            url = f"{self.base_url}/rest/api/content"
            params = {
                "spaceKey": space_key,
                "expand": "version,body.storage",
                "start": start,
                "limit": self.batch_size,
                "status": "current",
                "ancestorId": parent_id
            }
            
            try:
                response = self.session.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                pages = data.get("results", [])
                all_pages.extend(pages)
                
                # Check if we need to paginate
                if len(pages) < self.batch_size:
                    break
                    
                start += self.batch_size
                
                # Respect rate limits
                time.sleep(0.1)
                
            except requests.exceptions.RequestException as e:
                self.logger.error(f"Error fetching children pages: {e}")
                raise
        
        self.logger.info(f"Found {len(all_pages)} children pages")
        return all_pages
    
    def get_page_content(self, page_id: str) -> Dict[str, Any]:
        """
        Get detailed content of a specific page.
        
        Args:
            page_id: ID of the page to fetch
            
        Returns:
            Page object with content
        """
        self.logger.info(f"Fetching content for page ID {page_id}")
        
        url = f"{self.base_url}/rest/api/content/{page_id}"
        params = {
            "expand": "body.storage,history,version,ancestors,children.page,metadata.labels"
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching page content: {e}")
            raise
    
    def load_pir_pages(
        self, 
        space_key: str, 
        home_page_id: str,
        recursive: bool = True,
        filter_func: Optional[callable] = None
    ) -> List[Dict[str, Any]]:
        """
        Load all PIR pages from the specified space and home page.
        
        Args:
            space_key: Confluence space key
            home_page_id: ID of the PIR home page
            recursive: Whether to recursively fetch child pages
            filter_func: Optional function to filter pages
            
        Returns:
            List of page objects with content
        """
        self.logger.info(f"Loading PIR pages from space {space_key}, home page {home_page_id}")
        
        # Get all children pages
        children = self.get_children_pages(space_key, home_page_id)
        
        # Apply filter if provided
        if filter_func:
            children = [page for page in children if filter_func(page)]
        
        # Get detailed content for each page
        pir_pages = []
        
        for page in children:
            page_id = page["id"]
            try:
                page_with_content = self.get_page_content(page_id)
                pir_pages.append(page_with_content)
                
                # Recursively get children if requested
                if recursive:
                    child_pages = self.load_pir_pages(
                        space_key, 
                        page_id,
                        recursive=True,
                        filter_func=filter_func
                    )
                    pir_pages.extend(child_pages)
                
                # Respect rate limits
                time.sleep(0.2)
                
            except Exception as e:
                self.logger.error(f"Error processing page {page_id}: {e}")
        
        return pir_pages

def is_pir_page(page: Dict[str, Any]) -> bool:
    """
    Check if a page is a PIR page based on its title or labels.
    
    Args:
        page: Page object from Confluence API
        
    Returns:
        True if the page is a PIR page, False otherwise
    """
    title = page.get("title", "").lower()
    
    # Check title patterns
    title_indicators = ["incident", "pir", "post-incident", "post incident", "outage"]
    if any(indicator in title for indicator in title_indicators):
        return True
    
    # Check labels if available
    try:
        labels = page.get("metadata", {}).get("labels", {}).get("results", [])
        label_names = [label.get("name", "").lower() for label in labels]
        
        label_indicators = ["incident", "pir", "postincident", "outage"]
        if any(indicator in label for label in label_names):
            return True
    except (KeyError, AttributeError):
        pass
    
    return False
                    </code></pre>

                    <h4 class="font-medium">pir_processor.py</h4>
                    <pre><code class="language-python">
import logging
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
from dataclasses import dataclass

@dataclass
class PIRSection:
    """Represents a section in a PIR document."""
    title: str
    content: str
    section_type: str  # "header", "table", "text"
    metadata: Dict[str, Any]

@dataclass
class PIRDocument:
    """Represents a processed PIR document."""
    id: str
    title: str
    url: str
    space_key: str
    created_date: str
    last_modified_date: str
    sections: List[PIRSection]
    metadata: Dict[str, Any]

class PIRProcessor:
    """
    Processes Confluence PIR pages, extracting structured information
    based on the PIR format conventions.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_date_from_title(self, title: str) -> Optional[str]:
        """Extract date in YYYYMMDD format from title if present."""
        date_pattern = r'\b(\d{8})\b'  # Match 8 digits
        match = re.search(date_pattern, title)
        if match:
            return match.group(1)
        return None
    
    def extract_incident_id(self, title: str) -> Optional[str]:
        """Extract incident ID (IN000000 format) from title if present."""
        id_pattern = r'\b(IN\d{6,})\b'  # Match IN followed by 6+ digits
        match = re.search(id_pattern, title)
        if match:
            return match.group(1)
        return None
    
    def extract_team_name(self, title: str) -> Optional[str]:
        """
        Attempt to extract team name from title, assuming format:
        YYYYMMDD - IN000000 - Team Name - Incident Subject
        """
        parts = title.split(' - ')
        if len(parts) >= 3:
            return parts[2]
        return None
    
    def extract_table_data(self, table) -> List[Dict[str, str]]:
        """Extract structured data from an HTML table."""
        rows = table.find_all('tr')
        if not rows:
            return []
        
        data = []
        headers = []
        
        # Try to get headers from first row
        first_row = rows[0]
        header_cells = first_row.find_all(['th', 'td'])
        
        if header_cells:
            headers = [cell.get_text(strip=True) for cell in header_cells]
        
        # Process data rows
        for row in rows[1:] if headers else rows:
            cells = row.find_all('td')
            if len(cells) == 0:
                continue
                
            if not headers:
                # Handle key-value pair tables (2 columns)
                if len(cells) == 2:
                    key = cells[0].get_text(strip=True)
                    value = cells[1].get_text(strip=True)
                    row_data = {key: value}
                else:
                    # Create numeric keys for tables without headers
                    row_data = {f"col_{i}": cell.get_text(strip=True) 
                               for i, cell in enumerate(cells)}
            else:
                # Use headers as keys
                row_data = {}
                for i, cell in enumerate(cells):
                    if i < len(headers):
                        row_data[headers[i]] = cell.get_text(strip=True)
                    else:
                        row_data[f"col_{i}"] = cell.get_text(strip=True)
            
            data.append(row_data)
        
        return data
    
    def process_page(self, page: Dict[str, Any]) -> PIRDocument:
        """
        Process a Confluence page into a structured PIR document.
        
        Args:
            page: Page object from Confluence API
            
        Returns:
            Processed PIRDocument
        """
        self.logger.info(f"Processing PIR page: {page.get('title', 'Unknown')}")
        
        page_id = page.get("id")
        title = page.get("title", "")
        
        # Extract base metadata
        metadata = {
            "incident_date": self.extract_date_from_title(title),
            "incident_id": self.extract_incident_id(title),
            "team_name": self.extract_team_name(title),
        }
        
        # Get HTML content
        html_content = page.get("body", {}).get("storage", {}).get("value", "")
        if not html_content:
            self.logger.warning(f"No content found for page {page_id}")
            html_content = ""
        
        # Parse HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Extract sections
        sections = []
        
        # Find all headers (assuming h1, h2, h3 are used for sections)
        headers = soup.find_all(['h1', 'h2', 'h3'])
        
        for i, header in enumerate(headers):
            section_title = header.get_text(strip=True)
            section_level = header.name  # h1, h2, or h3
            
            # Get all content until the next header of same or higher level
            section_content = []
            current = header.next_sibling
            
            while current and not (
                current.name in ['h1', 'h2', 'h3'] and 
                (current.name <= section_level or i == len(headers) - 1)
            ):
                if current.name:  # Skip NavigableString objects
                    section_content.append(str(current))
                current = current.next_sibling
            
            section_html = "".join(section_content)
            section_soup = BeautifulSoup(section_html, 'html.parser')
            
            # Process tables within this section
            tables = section_soup.find_all('table')
            for table in tables:
                table_data = self.extract_table_data(table)
                
                # Create a section for this table
                table_section = PIRSection(
                    title=f"{section_title} - Table",
                    content=str(table_data),
                    section_type="table",
                    metadata={
                        "parent_section": section_title,
                        "section_level": section_level,
                        "table_data": table_data
                    }
                )
                sections.append(table_section)
                
                # Remove the table from section content to avoid duplication
                table.decompose()
            
            # Add text content as a separate section
            text_content = section_soup.get_text(strip=True)
            if text_content:
                text_section = PIRSection(
                    title=section_title,
                    content=text_content,
                    section_type="text",
                    metadata={
                        "section_level": section_level
                    }
                )
                sections.append(text_section)
        
        # Extract additional metadata from first table if it exists
        first_table = soup.find('table')
        if first_table:
            table_data = self.extract_table_data(first_table)
            for row in table_data:
                for key, value in row.items():
                    # Convert keys to snake_case for consistency
                    meta_key = key.lower().replace(' ', '_').replace('(', '').replace(')', '')
                    if value:  # Only add non-empty values
                        metadata[meta_key] = value
        
        # Create space and URL information
        space_key = page.get("space", {}).get("key", "")
        base_url = "https://confluence.example.com"  # This should be configured
        url = f"{base_url}/display/{space_key}/{page_id}"
        
        # Extract version information
        version_info = page.get("version", {})
        created_date = page.get("history", {}).get("createdDate", "")
        last_modified_date = version_info.get("when", "")
        
        return PIRDocument(
            id=page_id,
            title=title,
            url=url,
            space_key=space_key,
            created_date=created_date,
            last_modified_date=last_modified_date,
            sections=sections,
            metadata=metadata
        )
                    </code></pre>

                    <h4 class="font-medium">embedding_handler.py</h4>
                    <pre><code class="language-python">
import logging
import time
from typing import Dict, List, Any, Union
import requests
import numpy as np
from dataclasses import dataclass

@dataclass
class EmbeddingChunk:
    """Represents a chunk of text with its embedding."""
    id: str
    document_id: str
    text: str
    embedding: List[float]
    metadata: Dict[str, Any]

class EmbeddingHandler:
    """
    Handles the generation of embeddings using a custom OpenAI-compatible
    embedding model.
    """
    
    def __init__(
        self, 
        embedding_endpoint: str,
        api_key: str,
        model_name: str = "custom-embedding-model",
        batch_size: int = 10,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        dimensions: int = 1536
    ):
        """
        Initialize the embedding handler.
        
        Args:
            embedding_endpoint: URL of the embedding API endpoint
            api_key: API key for the embedding service
            model_name: Name of the embedding model
            batch_size: Number of texts to embed in a single API call
            max_retries: Maximum number of retries for failed API calls
            retry_delay: Delay between retries
            dimensions: Expected dimensions of the embeddings
        """
        self.embedding_endpoint = embedding_endpoint
        self.api_key = api_key
        self.model_name = model_name
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.dimensions = dimensions
        self.logger = logging.getLogger(__name__)
    
    def create_embeddings(
        self, 
        texts: List[str], 
        metadata_list: List[Dict[str, Any]]
    ) -> List[EmbeddingChunk]:
        """
        Create embeddings for a list of texts with associated metadata.
        
        Args:
            texts: List of text chunks to embed
            metadata_list: List of metadata dictionaries for each text chunk
            
        Returns:
            List of EmbeddingChunk objects with text, embedding, and metadata
        """
        self.logger.info(f"Creating embeddings for {len(texts)} text chunks")
        
        if len(texts) != len(metadata_list):
            raise ValueError("Length of texts and metadata_list must match")
        
        # Process in batches
        embeddings_chunks = []
        
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i+self.batch_size]
            batch_metadata = metadata_list[i:i+self.batch_size]
            
            self.logger.debug(f"Processing batch {i//self.batch_size + 1}")
            
            # Get embeddings for this batch
            batch_embeddings = self._get_embeddings_with_retry(batch_texts)
            
            # Create embedding chunks
            for j, (text, embedding, metadata) in enumerate(
                zip(batch_texts, batch_embeddings, batch_metadata)
            ):
                # Create unique ID for this chunk
                chunk_id = f"{metadata.get('document_id', 'doc')}_{i+j}"
                
                chunk = EmbeddingChunk(
                    id=chunk_id,
                    document_id=metadata.get('document_id', ''),
                    text=text,
                    embedding=embedding,
                    metadata=metadata
                )
                
                embeddings_chunks.append(chunk)
            
            # Avoid rate limiting
            if i + self.batch_size < len(texts):
                time.sleep(0.5)
        
        self.logger.info(f"Created {len(embeddings_chunks)} embedding chunks")
        return embeddings_chunks
    
    def _get_embeddings_with_retry(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for a batch of texts with retry logic.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        for attempt in range(self.max_retries):
            try:
                return self._call_embedding_api(texts)
            except Exception as e:
                self.logger.warning(f"Embedding attempt {attempt+1} failed: {e}")
                
                if attempt < self.max_retries - 1:
                    # Exponential backoff
                    delay = self.retry_delay * (2 ** attempt)
                    time.sleep(delay)
                else:
                    self.logger.error(f"Failed to create embeddings after {self.max_retries} attempts")
                    raise
    
    def _call_embedding_api(self, texts: List[str]) -> List[List[float]]:
        """
        Call the OpenAI-compatible embedding API.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "model": self.model_name,
            "input": texts
        }
        
        response = requests.post(
            self.embedding_endpoint,
            headers=headers,
            json=payload
        )
        
        if response.status_code != 200:
            self.logger.error(f"API error: {response.status_code}, {response.text}")
            response.raise_for_status()
        
        data = response.json()
        
        # Check response format (OpenAI compatible)
        if "data" not in data:
            self.logger.error(f"Unexpected API response format: {data}")
            raise ValueError("API response missing 'data' field")
        
        # Extract embeddings
        embeddings = [item["embedding"] for item in data["data"]]
        
        # Validate dimensions
        for i, emb in enumerate(embeddings):
            if len(emb) != self.dimensions:
                self.logger.warning(
                    f"Embedding {i} has {len(emb)} dimensions, expected {self.dimensions}"
                )
        
        return embeddings
                    </code></pre>

                    <h4 class="font-medium">indexer.py</h4>
                    <pre><code class="language-python">
import os
import logging
import argparse
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import hashlib
from pathlib import Path

from confluence_loader import ConfluenceLoader, is_pir_page
from pir_processor import PIRProcessor, PIRDocument, PIRSection
from embedding_handler import EmbeddingHandler, EmbeddingChunk

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("indexer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PIRIndexer:
    """
    Main class for indexing PIR documents from Confluence.
    Orchestrates the loading, processing, and embedding of documents.
    """
    
    def __init__(
        self,
        confluence_url: str,
        confluence_username: str,
        confluence_token: str,
        embedding_endpoint: str,
        embedding_api_key: str,
        vector_db_path: str,
        cache_dir: str = "./cache"
    ):
        """
        Initialize the PIR indexer.
        
        Args:
            confluence_url: URL of the Confluence instance
            confluence_username: Confluence username
            confluence_token: Confluence API token
            embedding_endpoint: URL of the embedding API endpoint
            embedding_api_key: API key for the embedding service
            vector_db_path: Path to the vector database
            cache_dir: Directory for caching loaded documents
        """
        self.confluence_loader = ConfluenceLoader(
            base_url=confluence_url,
            username=confluence_username,
            api_token=confluence_token
        )
        
        self.pir_processor = PIRProcessor()
        
        self.embedding_handler = EmbeddingHandler(
            embedding_endpoint=embedding_endpoint,
            api_key=embedding_api_key
        )
        
        self.vector_db_path = vector_db_path
        self.cache_dir = cache_dir
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # Load index metadata if it exists
        self.index_metadata_path = os.path.join(cache_dir, "index_metadata.json")
        self.index_metadata = self._load_index_metadata()
        
        # Initialize vector database
        self.vector_db = None  # Will be initialized in init_vector_db
    
    def _load_index_metadata(self) -> Dict[str, Any]:
        """Load index metadata from disk."""
        if os.path.exists(self.index_metadata_path):
            try:
                with open(self.index_metadata_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading index metadata: {e}")
        
        # Return default metadata if file doesn't exist or error occurred
        return {
            "last_indexed": None,
            "document_count": 0,
            "document_hashes": {},
            "embedding_count": 0
        }
    
    def _save_index_metadata(self):
        """Save index metadata to disk."""
        with open(self.index_metadata_path, 'w') as f:
            json.dump(self.index_metadata, f, indent=2)
    
    def init_vector_db(self):
        """Initialize the vector database."""
        # This is a placeholder for actual vector DB implementation
        # In a real implementation, this would connect to or create
        # a vector database like Chroma, FAISS, or Milvus
        
        # For now, we'll just log that we would initialize the DB
        logger.info(f"Initializing vector database at {self.vector_db_path}")
        
        # In real implementation, replace with:
        # from langchain.vectorstores import Chroma
        # from langchain.embeddings import OpenAIEmbeddings
        #
        # embeddings = OpenAIEmbeddings(
        #    openai_api_key=self.embedding_api_key,
        #    openai_api_base=self.embedding_endpoint
        # )
        #
        # self.vector_db = Chroma(
        #     persist_directory=self.vector_db_path,
        #     embedding_function=embeddings
        # )
    
    def _compute_document_hash(self, pir_doc: PIRDocument) -> str:
        """
        Compute a hash of the document content for change detection.
        
        Args:
            pir_doc: Processed PIR document
            
        Returns:
            Hash string of the document content
        """
        # Combine all section contents
        content = pir_doc.title + "".join([s.content for s in pir_doc.sections])
        return hashlib.md5(content.encode()).hexdigest()
    
    def _should_index_document(self, pir_doc: PIRDocument) -> bool:
        """
        Determine if a document should be indexed based on its hash.
        
        Args:
            pir_doc: Processed PIR document
            
        Returns:
            True if document should be indexed, False otherwise
        """
        doc_id = pir_doc.id
        doc_hash = self._compute_document_hash(pir_doc)
        
        # Get the stored hash for this document
        stored_hash = self.index_metadata["document_hashes"].get(doc_id)
        
        # Index if document is new or changed
        if stored_hash is None or stored_hash != doc_hash:
            self.index_metadata["document_hashes"][doc_id] = doc_hash
            return True
        
        return False
    
    def _prepare_chunks(self, pir_doc: PIRDocument) -> List[Dict[str, Any]]:
        """
        Prepare text chunks and metadata for embedding.
        
        Args:
            pir_doc: Processed PIR document
            
        Returns:
            List of dictionaries with text and metadata
        """
        chunks = []
        
        # Add document-level chunk
        doc_chunk = {
            "text": f"Title: {pir_doc.title}\n\nSummary: " + 
                   "\n".join([s.content[:200] for s in pir_doc.sections if s.section_type == "text"][:2]),
            "metadata": {
                "document_id": pir_doc.id,
                "title": pir_doc.title,
                "url": pir_doc.url,
                "chunk_type": "document",
                **pir_doc.metadata
            }
        }
        chunks.append(doc_chunk)
        
        # Add section-level chunks
        for i, section in enumerate(pir_doc.sections):
            # Skip empty sections
            if not section.content.strip():
                continue
            
            section_chunk = {
                "text": f"{section.title}:\n{section.content}",
                "metadata": {
                    "document_id": pir_doc.id,
                    "title": pir_doc.title,
                    "url": pir_doc.url,
                    "section_title": section.title,
                    "section_type": section.section_type,
                    "chunk_type": "section",
                    "section_index": i,
                    **pir_doc.metadata,
                    **section.metadata
                }
            }
            chunks.append(section_chunk)
            
            # For tables, add row-level chunks if it's a data-rich table
            if section.section_type == "table" and "table_data" in section.metadata:
                table_data = section.metadata["table_data"]
                
                # Only create row chunks for tables with multiple rows
                if len(table_data) > 1:
                    for j, row in enumerate(table_data):
                        # Convert row to text
                        row_text = ", ".join([f"{k}: {v}" for k, v in row.items()])
                        
                        row_chunk = {
                            "text": f"{section.title} (Row {j+1}):\n{row_text}",
                            "metadata": {
                                "document_id": pir_doc.id,
                                "title": pir_doc.title,
                                "url": pir_doc.url,
                                "section_title": section.title,
                                "section_type": "table_row",
                                "chunk_type": "table_row",
                                "section_index": i,
                                "row_index": j,
                                "table_data": row,
                                **pir_doc.metadata
                            }
                        }
                        chunks.append(row_chunk)
        
        return chunks
    
    def _store_embeddings(self, embedding_chunks: List[EmbeddingChunk]):
        """
        Store embeddings in vector database.
        
        Args:
            embedding_chunks: List of embedding chunks to store
        """
        # This is a placeholder for actual vector DB implementation
        # In a real implementation, this would store the embeddings in
        # a vector database like Chroma, FAISS, or Milvus
        
        # For now, we'll just log that we would store the embeddings
        logger.info(f"Storing {len(embedding_chunks)} embedding chunks in vector database")
        
        # In real implementation, replace with:
        # texts = [chunk.text for chunk in embedding_chunks]
        # embeddings = [chunk.embedding for chunk in embedding_chunks]
        # metadatas = [chunk.metadata for chunk in embedding_chunks]
        # ids = [chunk.id for chunk in embedding_chunks]
        #
        # self.vector_db.add_texts(
        #     texts=texts,
        #     embeddings=embeddings,
        #     metadatas=metadatas,
        #     ids=ids
        # )
    
    def index_pir_pages(
        self,
        space_key: str,
        home_page_id: str,
        recursive: bool = True,
        force_reindex: bool = False
    ):
        """
        Index PIR pages from Confluence.
        
        Args:
            space_key: Confluence space key
            home_page_id: ID of the PIR home page
            recursive: Whether to recursively index child pages
            force_reindex: Whether to force reindexing of all documents
        """
        logger.info(f"Starting indexing of PIR pages from space {space_key}, home page {home_page_id}")
        
        # Initialize vector database
        self.init_vector_db()
        
        # Load PIR pages
        pages = self.confluence_loader.load_pir_pages(
            space_key=space_key,
            home_page_id=home_page_id,
            recursive=recursive,
            filter_func=is_pir_page
        )
        
        logger.info(f"Loaded {len(pages)} PIR pages")
        
        # Process and index each page
        indexed_count = 0
        skipped_count = 0
        
        for page in pages:
            page_id = page.get("id")
            title = page.get("title", "Unknown")
            
            try:
                # Process the page
                pir_doc = self.pir_processor.process_page(page)
                
                # Check if we should index this document
                if force_reindex or self._should_index_document(pir_doc):
                    logger.info(f"Indexing document: {title} ({page_id})")
                    
                    # Prepare chunks for embedding
                    chunks = self._prepare_chunks(pir_doc)
                    
                    # Create embeddings
                    texts = [chunk["text"] for chunk in chunks]
                    metadatas = [chunk["metadata"] for chunk in chunks]
                    
                    embedding_chunks = self.embedding_handler.create_embeddings(
                        texts=texts,
                        metadata_list=metadatas
                    )
                    
                    # Store embeddings
                    self._store_embeddings(embedding_chunks)
                    
                    indexed_count += 1
                    self.index_metadata["embedding_count"] += len(embedding_chunks)
                else:
                    logger.info(f"Skipping unchanged document: {title} ({page_id})")
                    skipped_count += 1
            
            except Exception as e:
                logger.error(f"Error processing page {page_id}: {e}", exc_info=True)
        
        # Update index metadata
        self.index_metadata["last_indexed"] = datetime.now().isoformat()
        self.index_metadata["document_count"] = len(self.index_metadata["document_hashes"])
        self._save_index_metadata()
        
        logger.info(f"Indexing complete. Indexed: {indexed_count}, Skipped: {skipped_count}")
        
        # In real implementation, ensure vector DB is persisted
        # self.vector_db.persist()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Index PIR pages from Confluence")
    
    parser.add_argument("--confluence-url", required=True, help="Confluence URL")
    parser.add_argument("--confluence-username", required=True, help="Confluence username")
    parser.add_argument("--confluence-token", required=True, help="Confluence API token")
    parser.add_argument("--embedding-endpoint", required=True, help="Embedding API endpoint")
    parser.add_argument("--embedding-api-key", required=True, help="Embedding API key")
    parser.add_argument("--vector-db-path", default="./vector_db", help="Vector database path")
    parser.add_argument("--cache-dir", default="./cache", help="Cache directory")
    parser.add_argument("--space-key", required=True, help="Confluence space key")
    parser.add_argument("--home-page-id", required=True, help="PIR home page ID")
    parser.add_argument("--recursive", action="store_true", help="Recursively index child pages")
    parser.add_argument("--force-reindex", action="store_true", help="Force reindexing of all documents")
    
    args = parser.parse_args()
    
    indexer = PIRIndexer(
        confluence_url=args.confluence_url,
        confluence_username=args.confluence_username,
        confluence_token=args.confluence_token,
        embedding_endpoint=args.embedding_endpoint,
        embedding_api_key=args.embedding_api_key,
        vector_db_path=args.vector_db_path,
        cache_dir=args.cache_dir
    )
    
    indexer.index_pir_pages(
        space_key=args.space_key,
        home_page_id=args.home_page_id,
        recursive=args.recursive,
        force_reindex=args.force_reindex
    )
                    </code></pre>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">2</div>
                <h2 class="text-2xl font-bold m-0">Query Vectorization</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement Query Vectorization Module

Create a Python module for converting user queries into vector representations using the same custom embedding model used for indexing. The implementation should:

1. Take a user query as input
2. Preprocess the query for optimal retrieval
3. Generate embeddings using the custom OpenAI-compatible model
4. Return the vector representation for similarity search

## Requirements:
- Use the same embedding model as document indexing for consistency
- Support query expansion and preprocessing for better retrieval
- Handle different query types (e.g., factual questions, timeline questions, root cause questions)
- Support caching of query embeddings for performance
- Implement error handling and fallbacks

## Expected Inputs:
- User query string
- Query type (optional)
- Custom embedding model endpoint and API key

## Expected Outputs:
- Vector representation of the query
- Preprocessed query text
- Metadata about the vectorization process

## Implementation Guidelines:
1. Create a `query_vectorizer.py` module
2. Reuse the embedding function from the previous step
3. Implement query preprocessing and expansion functions
4. Add caching mechanism for frequently asked queries

Begin by implementing the main `QueryVectorizer` class that handles the core vectorization logic.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">query_vectorizer.py</h4>
                    <pre><code class="language-python">
import logging
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
from functools import lru_cache
import re

from embedding_handler import EmbeddingHandler

class QueryVectorizer:
    """
    Converts user queries into vector representations using a custom
    embedding model, with preprocessing to optimize retrieval.
    """
    
    def __init__(
        self,
        embedding_endpoint: str,
        api_key: str,
        model_name: str = "custom-embedding-model",
        cache_size: int = 100
    ):
        """
        Initialize the query vectorizer.
        
        Args:
            embedding_endpoint: URL of the embedding API endpoint
            api_key: API key for the embedding service
            model_name: Name of the embedding model
            cache_size: Size of the query cache
        """
        self.embedding_handler = EmbeddingHandler(
            embedding_endpoint=embedding_endpoint,
            api_key=api_key,
            model_name=model_name
        )
        
        self.logger = logging.getLogger(__name__)
        self.cache_size = cache_size
        
        # Maintain a simple in-memory cache
        self.query_cache = {}
    
    def _preprocess_query(self, query: str, query_type: Optional[str] = None) -> str:
        """
        Preprocess the query for better retrieval.
        
        Args:
            query: User query string
            query_type: Type of query (e.g., "factual", "timeline", "root_cause")
            
        Returns:
            Preprocessed query string
        """
        # Convert to lowercase and strip extra whitespace
        processed_query = query.lower().strip()
        
        # Remove extra spaces
        processed_query = re.sub(r'\s+', ' ', processed_query)
        
        # Expand common acronyms
        acronym_map = {
            "pir": "post implementation review incident",
            "rca": "root cause analysis",
            "sev": "severity",
            "p1": "priority 1",
            "p2": "priority 2",
            "sla": "service level agreement",
            "tta": "time to acknowledge",
            "ttd": "time to detect",
            "ttf": "time to fix",
            "ttr": "time to resolve"
        }
        
        # Check for whole word acronyms and expand them
        for acronym, expansion in acronym_map.items():
            processed_query = re.sub(
                r'\b' + acronym + r'\b', 
                expansion, 
                processed_query
            )
        
        # Add type-specific context if provided
        if query_type:
            type_context = {
                "factual": "facts about incident",
                "timeline": "timeline and timestamps of incident",
                "root_cause": "root cause analysis of incident",
                "impact": "business impact of incident",
                "actions": "actions and remediation for incident"
            }
            
            context = type_context.get(query_type, "")
            if context:
                processed_query = f"{processed_query} {context}"
        
        # Extract and normalize incident IDs
        id_pattern = r'in\s*(\d{6,})'  # Match "in" followed by 6+ digits
        id_matches = re.findall(id_pattern, processed_query)
        
        for id_match in id_matches:
            # Replace with properly formatted ID
            processed_query = processed_query.replace(
                f"in{id_match}", 
                f"IN{id_match}"
            )
        
        return processed_query
    
    def _expand_query(self, query: str) -> List[str]:
        """
        Generate expanded versions of the query for better retrieval.
        
        Args:
            query: Preprocessed query string
            
        Returns:
            List of expanded query strings
        """
        expanded_queries = [query]
        
        # Extract incident ID if present
        id_pattern = r'(IN\d{6,})'  # Match IN followed by 6+ digits
        id_match = re.search(id_pattern, query)
        
        if id_match:
            incident_id = id_match.group(1)
            
            # Add a version focused just on the incident ID
            expanded_queries.append(f"Find information about {incident_id}")
            
            # If query mentions specific aspects, add focused versions
            aspects = {
                "root cause": f"What was the root cause of {incident_id}",
                "impact": f"What was the impact of {incident_id}",
                "timeline": f"What was the timeline of {incident_id}",
                "detection": f"How was {incident_id} detected",
                "resolution": f"How was {incident_id} resolved",
                "actions": f"What actions were taken for {incident_id}"
            }
            
            for keyword, expanded in aspects.items():
                if keyword in query:
                    expanded_queries.append(expanded)
        
        # Handle specific question types
        question_patterns = [
            (r'what happened', "incident summary"),
            (r'when was.*detected', "detection time"),
            (r'how long did it take', "time to resolve"),
            (r'who was', "teams involved"),
            (r'why did', "root cause analysis")
        ]
        
        for pattern, expansion in question_patterns:
            if re.search(pattern, query):
                expanded_queries.append(f"{query} {expansion}")
        
        # Remove duplicates while preserving order
        return list(dict.fromkeys(expanded_queries))
    
    def _compute_query_hash(self, query: str) -> str:
        """
        Compute a hash of the query for caching.
        
        Args:
            query: Query string
            
        Returns:
            Hash string of the query
        """
        return hashlib.md5(query.encode()).hexdigest()
    
    def vectorize_query(
        self, 
        query: str, 
        query_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Convert a user query into a vector representation.
        
        Args:
            query: User query string
            query_type: Type of query (optional)
            
        Returns:
            Dictionary with query vector and metadata
        """
        # Check cache first
        query_hash = self._compute_query_hash(query + (query_type or ""))
        
        if query_hash in self.query_cache:
            self.logger.debug(f"Cache hit for query: {query}")
            return self.query_cache[query_hash]
        
        # Preprocess the query
        processed_query = self._preprocess_query(query, query_type)
        
        # Generate expanded queries
        expanded_queries = self._expand_query(processed_query)
        
        try:
            # Generate embeddings for all expanded queries
            embedding_chunks = self.embedding_handler.create_embeddings(
                texts=expanded_queries,
                metadata_list=[{"query": q} for q in expanded_queries]
            )
            
            # Use the first (original) query's embedding as primary
            primary_embedding = embedding_chunks[0].embedding
            
            # Create additional embeddings for alternatives
            alternative_embeddings = [
                chunk.embedding for chunk in embedding_chunks[1:]
            ] if len(embedding_chunks) > 1 else []
            
            result = {
                "query": query,
                "processed_query": processed_query,
                "expanded_queries": expanded_queries,
                "embedding": primary_embedding,
                "alternative_embeddings": alternative_embeddings,
                "timestamp": time.time()
            }
            
            # Update cache (simple LRU implementation)
            if len(self.query_cache) >= self.cache_size:
                # Remove oldest entry
                oldest_key = min(
                    self.query_cache.keys(),
                    key=lambda k: self.query_cache[k]["timestamp"]
                )
                del self.query_cache[oldest_key]
            
            self.query_cache[query_hash] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error vectorizing query: {e}")
            
            # Return a minimal result with error information
            return {
                "query": query,
                "processed_query": processed_query,
                "error": str(e),
                "embedding": None,
                "timestamp": time.time()
            }
    
    @lru_cache(maxsize=100)
    def cached_vectorize_query(
        self, 
        query: str, 
        query_type: Optional[str] = None
    ) -> np.ndarray:
        """
        Cached version of query vectorization that returns just the embedding.
        
        Args:
            query: User query string
            query_type: Type of query (optional)
            
        Returns:
            Numpy array of the query embedding
        """
        result = self.vectorize_query(query, query_type)
        
        if result["embedding"] is None:
            self.logger.error("Failed to generate query embedding")
            # Return a zero vector as fallback
            return np.zeros(1536)  # Assuming 1536-dimensional embeddings
        
        return np.array(result["embedding"])
                    </code></pre>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">3</div>
                <h2 class="text-2xl font-bold m-0">Semantic Similarity Search</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement Semantic Similarity Search Module

Create a Python module that implements the advanced retrieval strategy for incident PIR documents as described earlier. This module should:

1. Take a user query and its vector representation
2. Apply the specialized retrieval techniques (Parent Document Retriever, Self-Query Retriever, etc.)
3. Implement query routing based on query type
4. Retrieve relevant document snippets using semantic similarity
5. Apply reranking to ensure the most relevant snippets are returned
6. Return formatted results with source information

## Requirements:
- Implement the multi-faceted retrieval architecture discussed earlier
- Support different retrieval strategies based on query type
- Handle table structures specially for data-centric queries
- Include metadata filtering for targeted retrieval
- Apply post-retrieval reranking to improve relevance
- Support batched and parallel retrieval for performance

## Expected Inputs:
- User query string
- Query vector representation
- Optional filters and parameters

## Expected Outputs:
- List of relevant document snippets with source information
- Relevance scores and reasoning
- Structured data extractions for table-based information

## Implementation Guidelines:
1. Create a `semantic_search.py` module
2. Implement the core retrieval logic
3. Add specialized handling for different query types
4. Implement reranking for improved relevance

Begin by implementing the main `SemanticSearchEngine` class that orchestrates the retrieval process.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">semantic_search.py</h4>
                    <pre><code class="language-python">
import logging
import re
from typing import Dict, List, Any, Optional, Union, Tuple
import numpy as np
from dataclasses import dataclass
import concurrent.futures
from enum import Enum
import json

# Placeholder imports - in a real implementation, these would be actual imports
# from langchain.vectorstores import VectorStore
# from langchain.retrievers import ParentDocumentRetriever, MultiVectorRetriever
# from langchain.retrievers import SelfQueryRetriever, ContextualCompressionRetriever

class QueryType(Enum):
    """Enum for different types of queries."""
    GENERAL = "general"
    METADATA = "metadata"
    TIMELINE = "timeline"
    ROOT_CAUSE = "root_cause"
    IMPACT = "impact"
    ACTION = "action"
    TABLE_DATA = "table_data"

@dataclass
class SearchResult:
    """Represents a single search result."""
    id: str
    document_id: str
    text: str
    metadata: Dict[str, Any]
    score: float
    source_url: Optional[str] = None
    section_title: Optional[str] = None

class PIRQueryClassifier:
    """
    Classifies PIR queries into specific types for specialized retrieval.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Define patterns for each query type
        self.patterns = {
            QueryType.METADATA: [
                r'incident (number|id)',
                r'impacted (application|app)',
                r'affected region',
                r'impact level',
                r'when (was|did) the incident',
                r'which team',
                r'who was responsible'
            ],
            QueryType.TIMELINE: [
                r'timeline',
                r'when (was|did)',
                r'how long did it take',
                r'(start|begin|detect|fix|resolve|recover) time',
                r'time to (detect|fix|resolve|recover)',
                r'duration of',
                r'how (quickly|soon)'
            ],
            QueryType.ROOT_CAUSE: [
                r'root cause',
                r'why did',
                r'what caused',
                r'reason for',
                r'trigger',
                r'underlying (issue|problem)'
            ],
            QueryType.IMPACT: [
                r'impact',
                r'affected',
                r'outage',
                r'service disruption',
                r'business (impact|consequence)',
                r'customer impact',
                r'how (many|much)',
                r'severity'
            ],
            QueryType.ACTION: [
                r'action',
                r'remediation',
                r'mitigation',
                r'prevent',
                r'fix',
                r'lesson',
                r'what (was|were) done',
                r'how (was|were) (it|the incident) (fixed|resolved)',
                r'resolution'
            ],
            QueryType.TABLE_DATA: [
                r'table',
                r'metrics',
                r'data',
                r'number[s]? of',
                r'specific (time|date|value)',
                r'statistic'
            ]
        }
    
    def classify_query(self, query: str) -> QueryType:
        """
        Classify a query into a specific type.
        
        Args:
            query: The user query
            
        Returns:
            The query type
        """
        query = query.lower()
        
        # Check each pattern type
        for query_type, patterns in self.patterns.items():
            for pattern in patterns:
                if re.search(pattern, query):
                    self.logger.debug(f"Query '{query}' classified as {query_type.value}")
                    return query_type
        
        # Default to general if no patterns match
        self.logger.debug(f"Query '{query}' classified as general")
        return QueryType.GENERAL

class SemanticSearchEngine:
    """
    Semantic search engine for PIR documents implementing the specialized
    retrieval strategy for incident reports.
    """
    
    def __init__(
        self,
        vector_db_path: str,
        embedding_endpoint: str,
        embedding_api_key: str,
        reranking_endpoint: Optional[str] = None,
        reranking_api_key: Optional[str] = None,
        max_results: int = 10
    ):
        """
        Initialize the semantic search engine.
        
        Args:
            vector_db_path: Path to the vector database
            embedding_endpoint: URL of the embedding API endpoint
            embedding_api_key: API key for the embedding service
            reranking_endpoint: URL of the reranking API endpoint (optional)
            reranking_api_key: API key for the reranking service (optional)
            max_results: Maximum number of results to return
        """
        self.vector_db_path = vector_db_path
        self.embedding_endpoint = embedding_endpoint
        self.embedding_api_key = embedding_api_key
        self.reranking_endpoint = reranking_endpoint
        self.reranking_api_key = reranking_api_key
        self.max_results = max_results
        self.logger = logging.getLogger(__name__)
        
        # Initialize query classifier
        self.query_classifier = PIRQueryClassifier()
        
        # Initialize vector database and retrievers
        self._init_retrievers()
    
    def _init_retrievers(self):
        """Initialize the different retrieval components."""
        # This is a placeholder for actual retriever initialization
        # In a real implementation, this would initialize the various
        # retrieval components like ParentDocumentRetriever, etc.
        
        self.logger.info("Initializing retrieval components")
        
        # Vector database - placeholder for actual implementation
        # self.vector_db = Chroma(
        #     persist_directory=self.vector_db_path,
        #     embedding_function=self._get_embedding_function()
        # )
        
        # Retrievers - placeholders for actual implementations
        # self.base_retriever = self.vector_db.as_retriever(search_kwargs={"k": self.max_results * 2})
        # self.parent_retriever = self._create_parent_retriever()
        # self.multi_vector_retriever = self._create_multi_vector_retriever()
        # self.self_query_retriever = self._create_self_query_retriever()
        # self.table_retriever = self._create_table_retriever()
        # self.ensemble_retriever = self._create_ensemble_retriever()
        # self.reranked_retriever = self._create_reranked_retriever()
    
    def _detect_query_constraints(self, query: str) -> Dict[str, Any]:
        """
        Detect constraints from the query for filtering.
        
        Args:
            query: User query
            
        Returns:
            Dictionary of constraints
        """
        constraints = {}
        
        # Extract incident ID if present
        id_pattern = r'(IN\d{6,})'  # Match IN followed by 6+ digits
        id_match = re.search(id_pattern, query)
        if id_match:
            constraints["incident_id"] = id_match.group(1)
        
        # Extract date if present
        date_pattern = r'(\d{8})'  # Match 8 digits (YYYYMMDD)
        date_match = re.search(date_pattern, query)
        if date_match:
            constraints["date"] = date_match.group(1)
        
        # Extract team if mentioned
        team_pattern = r'team\s+([A-Za-z]+)'
        team_match = re.search(team_pattern, query)
        if team_match:
            constraints["team"] = team_match.group(1)
        
        # Extract impact level if mentioned
        impact_pattern = r'(high|medium|low)\s+impact'
        impact_match = re.search(impact_pattern, query, re.IGNORECASE)
        if impact_match:
            constraints["impact"] = impact_match.group(1).capitalize()
        
        return constraints
    
    def _get_retriever_for_query(self, query_type: QueryType):
        """
        Get the appropriate retriever for the query type.
        
        Args:
            query_type: Type of the query
            
        Returns:
            The appropriate retriever
        """
        # This is a placeholder for actual implementation
        # In a real implementation, this would return the appropriate
        # retriever based on the query type
        
        # Placeholder mapping
        retriever_map = {
            QueryType.METADATA: "self_query_retriever",
            QueryType.TIMELINE: "parent_retriever",
            QueryType.ROOT_CAUSE: "parent_retriever",
            QueryType.IMPACT: "parent_retriever",
            QueryType.ACTION: "parent_retriever",
            QueryType.TABLE_DATA: "table_retriever",
            QueryType.GENERAL: "ensemble_retriever"
        }
        
        retriever_name = retriever_map.get(query_type, "ensemble_retriever")
        self.logger.debug(f"Using {retriever_name} for query type {query_type.value}")
        
        # For now, just return the name as a placeholder
        return retriever_name
    
    def _apply_reranking(
        self,
        query: str,
        results: List[Dict[str, Any]],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """
        Apply reranking to the search results.
        
        Args:
            query: User query
            results: Initial search results
            top_k: Number of results to return after reranking
            
        Returns:
            Reranked results
        """
        if not self.reranking_endpoint or len(results) <= 1:
            # Skip reranking if no endpoint or only one result
            return results[:top_k]
        
        try:
            # This is a placeholder for actual reranking implementation
            # In a real implementation, this would call a reranking API
            
            self.logger.debug(f"Reranking {len(results)} results for query: {query}")
            
            # Placeholder for actual reranking logic
            # For now, just sort by score and return top_k
            reranked_results = sorted(
                results, 
                key=lambda x: x.get("score", 0),
                reverse=True
            )[:top_k]
            
            return reranked_results
            
        except Exception as e:
            self.logger.error(f"Error in reranking: {e}")
            # Fall back to original ranking
            return results[:top_k]
    
    def _format_results(
        self,
        raw_results: List[Dict[str, Any]],
        query: str
    ) -> List[SearchResult]:
        """
        Format raw search results into structured SearchResult objects.
        
        Args:
            raw_results: Raw search results
            query: User query
            
        Returns:
            List of SearchResult objects
        """
        formatted_results = []
        
        for result in raw_results:
            # Extract required fields
            result_id = result.get("id", "unknown")
            document_id = result.get("metadata", {}).get("document_id", "unknown")
            text = result.get("text", "")
            metadata = result.get("metadata", {})
            score = result.get("score", 0.0)
            
            # Extract optional fields
            source_url = metadata.get("url")
            section_title = metadata.get("section_title")
            
            # Create SearchResult object
            search_result = SearchResult(
                id=result_id,
                document_id=document_id,
                text=text,
                metadata=metadata,
                score=score,
                source_url=source_url,
                section_title=section_title
            )
            
            formatted_results.append(search_result)
        
        return formatted_results
    
    def _extract_table_data(self, results: List[SearchResult]) -> List[Dict[str, Any]]:
        """
        Extract structured data from table results.
        
        Args:
            results: Search results
            
        Returns:
            List of structured data extractions
        """
        table_data = []
        
        for result in results:
            if result.metadata.get("section_type") in ["table", "table_row"]:
                # Extract table data if available
                if "table_data" in result.metadata:
                    table_data.append({
                        "id": result.id,
                        "document_id": result.document_id,
                        "section_title": result.section_title,
                        "data": result.metadata["table_data"]
                    })
                elif "row_index" in result.metadata:
                    # For table row results
                    table_data.append({
                        "id": result.id,
                        "document_id": result.document_id,
                        "section_title": result.section_title,
                        "row_index": result.metadata.get("row_index"),
                        "data": result.metadata.get("table_data", {})
                    })
        
        return table_data
    
    def search(
        self,
        query: str,
        query_embedding: Optional[List[float]] = None,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 10
    ) -> Dict[str, Any]:
        """
        Perform semantic search for the given query.
        
        Args:
            query: User query
            query_embedding: Vector representation of the query (optional)
            filters: Additional filters to apply (optional)
            top_k: Number of results to return
            
        Returns:
            Dictionary with search results and metadata
        """
        self.logger.info(f"Searching for: {query}")
        
        # Detect query type
        query_type = self.query_classifier.classify_query(query)
        
        # Detect constraints from query
        query_constraints = self._detect_query_constraints(query)
        
        # Combine with explicit filters
        if filters:
            query_constraints.update(filters)
        
        # Get appropriate retriever for this query type
        retriever_name = self._get_retriever_for_query(query_type)
        
        # PLACEHOLDER: In a real implementation, this would actually use
        # the retrievers to get results. For now, we'll simulate results.
        
        # Simulate retrieval results
        raw_results = self._simulate_retrieval(query, query_type, query_constraints)
        
        # Apply reranking
        reranked_results = self._apply_reranking(query, raw_results, top_k)
        
        # Format results
        formatted_results = self._format_results(reranked_results, query)
        
        # Extract table data if applicable
        table_data = self._extract_table_data(formatted_results) if query_type == QueryType.TABLE_DATA else []
        
        # Prepare response
        response = {
            "query": query,
            "query_type": query_type.value,
            "constraints": query_constraints,
            "results": formatted_results,
            "table_data": table_data,
            "total_results": len(raw_results),
            "retriever_used": retriever_name
        }
        
        return response
    
    def _simulate_retrieval(
        self, 
        query: str, 
        query_type: QueryType,
        constraints: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Simulate retrieval results for demonstration purposes.
        
        Args:
            query: User query
            query_type: Type of query
            constraints: Query constraints
            
        Returns:
            List of simulated results
        """
        # This is a placeholder that simulates retrieval results
        # In a real implementation, this would be replaced with actual
        # retrieval from the vector database
        
        self.logger.info(f"Simulating retrieval for query type: {query_type.value}")
        
        # Create some dummy results
        results = []
        
        # Add constraints to metadata for filtering
        metadata = {"query_type": query_type.value}
        metadata.update(constraints)
        
        # Generate dummy results based on query type
        for i in range(15):  # Generate more than needed for reranking
            score = 0.95 - (i * 0.05)  # Decreasing scores
            
            result = {
                "id": f"result_{i}",
                "text": f"Simulated result {i} for query: {query}",
                "metadata": {
                    "document_id": f"doc_{i}",
                    "section_title": f"Section for {query_type.value}",
                    "section_type": "text" if i % 3 != 0 else "table",
                    "url": f"https://confluence.example.com/display/PIR/doc_{i}",
                    **metadata
                },
                "score": score
            }
            
            # Add table data for table results
            if result["metadata"]["section_type"] == "table":
                if query_type == QueryType.TIMELINE:
                    result["metadata"]["table_data"] = {
                        "Time": f"2023-01-01 {10+i}:00:00",
                        "Event": f"Event {i} occurred",
                        "Details": f"Details about event {i}"
                    }
                elif query_type == QueryType.ROOT_CAUSE:
                    result["metadata"]["table_data"] = {
                        "Root Cause": f"Cause {i}",
                        "Description": f"Description of cause {i}",
                        "Status": "Completed" if i % 2 == 0 else "In Progress"
                    }
            
            results.append(result)
        
        return results

class PIRRetrievalService:
    """
    Service class that combines query vectorization and semantic search.
    """
    
    def __init__(
        self,
        vector_db_path: str,
        embedding_endpoint: str,
        embedding_api_key: str,
        reranking_endpoint: Optional[str] = None,
        reranking_api_key: Optional[str] = None
    ):
        """
        Initialize the PIR retrieval service.
        
        Args:
            vector_db_path: Path to the vector database
            embedding_endpoint: URL of the embedding API endpoint
            embedding_api_key: API key for the embedding service
            reranking_endpoint: URL of the reranking API endpoint (optional)
            reranking_api_key: API key for the reranking service (optional)
        """
        from query_vectorizer import QueryVectorizer
        
        self.query_vectorizer = QueryVectorizer(
            embedding_endpoint=embedding_endpoint,
            api_key=embedding_api_key
        )
        
        self.search_engine = SemanticSearchEngine(
            vector_db_path=vector_db_path,
            embedding_endpoint=embedding_endpoint,
            embedding_api_key=embedding_api_key,
            reranking_endpoint=reranking_endpoint,
            reranking_api_key=reranking_api_key
        )
        
        self.logger = logging.getLogger(__name__)
    
    def retrieve(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        top_k: int = 10
    ) -> Dict[str, Any]:
        """
        Retrieve relevant information for a query.
        
        Args:
            query: User query
            filters: Additional filters to apply (optional)
            top_k: Number of results to return
            
        Returns:
            Dictionary with search results and metadata
        """
        self.logger.info(f"Retrieving information for query: {query}")
        
        try:
            # Vectorize the query
            query_data = self.query_vectorizer.vectorize_query(query)
            
            # Check if vectorization was successful
            if query_data.get("embedding") is None:
                raise ValueError(f"Failed to vectorize query: {query_data.get('error')}")
            
            # Perform semantic search
            search_results = self.search_engine.search(
                query=query,
                query_embedding=query_data["embedding"],
                filters=filters,
                top_k=top_k
            )
            
            # Add query processing info to results
            search_results["processed_query"] = query_data["processed_query"]
            search_results["expanded_queries"] = query_data.get("expanded_queries", [])
            
            return search_results
            
        except Exception as e:
            self.logger.error(f"Error in retrieval: {e}", exc_info=True)
            
            # Return error response
            return {
                "query": query,
                "error": str(e),
                "results": [],
                "table_data": [],
                "total_results": 0
            }
                    </code></pre>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">4</div>
                <h2 class="text-2xl font-bold m-0">ReAct Agent Configuration (LangChain)</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement LangChain ReAct Agent for PIR Retrieval

Create a Python module that sets up a LangChain ReAct agent with access to PIR retrieval capabilities and a simple email tool. The agent should:

1. Process user queries about incident reports
2. Use the PIR retrieval service to find relevant information
3. Have access to an email tool for sending information
4. Implement the ReAct (Reasoning and Acting) framework for step-by-step problem solving
5. Include a thoughtful prompt template that guides the agent's behavior

## Requirements:
- Use LangChain's ReAct agent framework
- Define appropriate tools for the agent (PIR retrieval, email sending)
- Create a comprehensive system prompt that guides the agent
- Implement proper error handling and fallbacks
- Structure agent output for easy integration with the UI

## Expected Inputs:
- User query
- Configuration for tools and the LLM
- Optional history/memory for conversation context

## Expected Outputs:
- Agent response with reasoning steps
- Results from any tools used
- Actions taken (e.g., sending an email)

## Implementation Guidelines:
1. Create a `pir_agent.py` module
2. Define the PIR retrieval tool
3. Define the email sending tool (stub implementation)
4. Create the agent prompt template
5. Set up the ReAct agent with the tools

Begin by implementing the tool definitions and then the agent configuration.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">pir_agent.py</h4>
                    <pre><code class="language-python">
import logging
import json
import re
from typing import Dict, List, Any, Optional, Union, Callable
from pydantic import BaseModel, Field

# LangChain imports
from langchain.agents import AgentType, AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.tools import BaseTool, StructuredTool, Tool
from langchain.schema import AgentAction, AgentFinish
from langchain.chains.llm import LLMChain
from langchain.schema.language_model import BaseLanguageModel

# Local imports
from semantic_search import PIRRetrievalService

class PIRSearchInput(BaseModel):
    """Input schema for PIR search tool."""
    query: str = Field(description="The search query about the incident")
    filters: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Optional filters like incident_id, date, team"
    )

class SendEmailInput(BaseModel):
    """Input schema for email sending tool."""
    to: str = Field(description="Email recipient address")
    subject: str = Field(description="Email subject line")
    body: str = Field(description="Email body content")

class PIRRetrievalTool(BaseTool):
    """Tool for retrieving information from PIR documents."""
    
    name = "pir_search"
    description = """
    Search for information in incident PIR (Post-Implementation Review) documents.
    Use this tool to find details about incidents, their timelines, root causes, impacts, and actions taken.
    Provide a specific query about the incident information you're looking for.
    """
    args_schema = PIRSearchInput
    
    def __init__(self, retrieval_service: PIRRetrievalService):
        """
        Initialize the PIR retrieval tool.
        
        Args:
            retrieval_service: The PIR retrieval service
        """
        super().__init__()
        self.retrieval_service = retrieval_service
        self.logger = logging.getLogger(__name__)
    
    def _run(self, query: str, filters: Optional[Dict[str, Any]] = None) -> str:
        """
        Run the tool to retrieve information from PIR documents.
        
        Args:
            query: The search query
            filters: Optional filters
            
        Returns:
            Formatted results as a string
        """
        self.logger.info(f"Searching PIR documents for: {query}")
        
        try:
            # Call the retrieval service
            results = self.retrieval_service.retrieve(
                query=query,
                filters=filters,
                top_k=5  # Limit to 5 results for readability
            )
            
            # Check for errors
            if "error" in results:
                return f"Error searching PIR documents: {results['error']}"
            
            # Format the results
            formatted_results = self._format_search_results(results)
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"Error in PIR search: {e}", exc_info=True)
            return f"Error searching PIR documents: {str(e)}"
    
    def _format_search_results(self, results: Dict[str, Any]) -> str:
        """
        Format search results into a readable string.
        
        Args:
            results: The search results
            
        Returns:
            Formatted results as a string
        """
        if not results.get("results"):
            return "No relevant information found in PIR documents."
        
        formatted = []
        formatted.append(f"Found {len(results['results'])} relevant items:")
        
        # Add each result
        for i, result in enumerate(results["results"], 1):
            formatted.append(f"\n--- Result {i} ---")
            
            # Add section title if available
            if result.section_title:
                formatted.append(f"Section: {result.section_title}")
            
            # Add document ID and incident info
            doc_id = result.document_id
            incident_id = result.metadata.get("incident_id", "Unknown")
            formatted.append(f"Document: {doc_id} (Incident: {incident_id})")
            
            # Add content
            formatted.append(f"Content: {result.text[:500]}..." if len(result.text) > 500 else f"Content: {result.text}")
            
            # Add source URL if available
            if result.source_url:
                formatted.append(f"Source: {result.source_url}")
        
        # Add table data if available
        if results.get("table_data"):
            formatted.append("\n--- Table Data ---")
            for table in results["table_data"][:3]:  # Limit to 3 tables
                formatted.append(f"\nTable: {table.get('section_title', 'Unknown')}")
                
                # Format table data
                data = table.get("data", {})
                if isinstance(data, dict):
                    for key, value in data.items():
                        formatted.append(f"{key}: {value}")
                elif isinstance(data, list):
                    for row in data:
                        formatted.append(str(row))
        
        return "\n".join(formatted)

class SendEmailTool(BaseTool):
    """Tool for sending emails (stub implementation)."""
    
    name = "send_email"
    description = """
    Send an email with information about an incident.
    Use this tool when you need to share incident information with someone.
    Provide the recipient email address, subject line, and email body.
    This is a critical action, so only use it when explicitly asked to send information.
    """
    args_schema = SendEmailInput
    
    def __init__(self):
        """Initialize the email sending tool."""
        super().__init__()
        self.logger = logging.getLogger(__name__)
    
    def _run(self, to: str, subject: str, body: str) -> str:
        """
        Run the tool to send an email (stub implementation).
        
        Args:
            to: Recipient email address
            subject: Email subject line
            body: Email body content
            
        Returns:
            Confirmation message
        """
        self.logger.info(f"Would send email to: {to}, Subject: {subject}")
        
        # This is a stub implementation that just logs the email details
        email_details = {
            "to": to,
            "subject": subject,
            "body": body,
            "timestamp": "2023-06-15T12:34:56Z"  # Placeholder timestamp
        }
        
        # Log the email details
        self.logger.info(f"Email details: {json.dumps(email_details, indent=2)}")
        
        # In a real implementation, this would send an actual email
        return f"Email sent to {to} with subject: {subject}"

class PIRAgent:
    """
    ReAct agent for PIR document retrieval and actions.
    """
    
    def __init__(
        self,
        llm: BaseLanguageModel,
        retrieval_service: PIRRetrievalService,
        verbose: bool = False
    ):
        """
        Initialize the PIR agent.
        
        Args:
            llm: Language model for the agent
            retrieval_service: PIR retrieval service
            verbose: Whether to enable verbose output
        """
        self.llm = llm
        self.retrieval_service = retrieval_service
        self.verbose = verbose
        self.logger = logging.getLogger(__name__)
        
        # Create tools
        self.tools = self._create_tools()
        
        # Create agent
        self.agent_executor = self._create_agent()
    
    def _create_tools(self) -> List[BaseTool]:
        """
        Create the tools for the agent.
        
        Returns:
            List of tools
        """
        # Create PIR retrieval tool
        pir_tool = PIRRetrievalTool(self.retrieval_service)
        
        # Create email sending tool
        email_tool = SendEmailTool()
        
        return [pir_tool, email_tool]
    
    def _create_agent(self) -> AgentExecutor:
        """
        Create the ReAct agent.
        
        Returns:
            Agent executor
        """
        # Define the prompt template
        template = """
        You are an Incident Management Assistant specialized in helping users find information in Post-Implementation Review (PIR) documents. 
        Your goal is to answer questions about incidents, their timelines, root causes, impacts, and actions taken.

        When answering questions:
        1. First, understand what specific incident information the user is looking for.
        2. Use the pir_search tool to find relevant information in PIR documents.
        3. Analyze the search results to find the most relevant information.
        4. Present the information in a clear and structured manner.
        5. If asked to send an email, use the send_email tool, but only when explicitly requested.
        6. Always cite your sources by including document IDs and section titles.
        7. If the information is not found, acknowledge this and suggest refinements to the search.

        {format_instructions}

        Human: {input}
        {agent_scratchpad}
        """
        
        # Create the prompt
        prompt = PromptTemplate(
            template=template,
            input_variables=["input", "agent_scratchpad"],
            partial_variables={"format_instructions": ""}
        )
        
        # Create the agent
        agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        # Create the agent executor
        agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=self.tools,
            verbose=self.verbose,
            handle_parsing_errors=True,
            max_iterations=10,
            early_stopping_method="generate"
        )
        
        return agent_executor
    
    def run(self, query: str) -> Dict[str, Any]:
        """
        Run the agent on a query.
        
        Args:
            query: User query
            
        Returns:
            Agent response
        """
        self.logger.info(f"Running PIR agent for query: {query}")
        
        try:
            # Run the agent
            response = self.agent_executor.run(query)
            
            # Extract tool calls and intermediate steps
            intermediate_steps = self.agent_executor.agent.tools
            
            # Format the response
            formatted_response = {
                "query": query,
                "response": response,
                "tool_calls": [
                    {
                        "tool": step.tool,
                        "tool_input": step.tool_input,
                        "tool_output": step.tool_output
                    }
                    for step in intermediate_steps
                ] if hasattr(self.agent_executor, "intermediate_steps") else []
            }
            
            return formatted_response
            
        except Exception as e:
            self.logger.error(f"Error running PIR agent: {e}", exc_info=True)
            
            # Return error response
            return {
                "query": query,
                "error": str(e),
                "response": f"I encountered an error while processing your query: {str(e)}. Please try again or rephrase your question."
            }
                    </code></pre>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">5</div>
                <h2 class="text-2xl font-bold m-0">Reasoning LLM Integration (Custom Model)</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement Custom Reasoning LLM Integration

Create a Python module that integrates a custom OpenAI-compatible reasoning model into the ReAct agent workflow. The implementation should:

1. Initialize and configure the custom reasoning model client
2. Format the input with retrieved snippets for the reasoning model
3. Handle the model's output, including parsing the thinking process in the <think></think> tags
4. Integrate with the LangChain ReAct agent framework
5. Implement error handling and fallback mechanisms

## Requirements:
- Use a custom OpenAI-compatible API for the reasoning model
- Properly format inputs with retrieved context
- Parse model outputs, including the thinking process
- Implement retry logic and error handling
- Handle non-streaming output from the reasoning model

## Expected Inputs:
- Model API endpoint
- API key
- Input text with context
- Optional model parameters

## Expected Outputs:
- Structured model response with reasoning steps
- Parsed thinking process from <think></think> tags
- Error information if the model call fails

## Implementation Guidelines:
1. Create a `custom_reasoning_llm.py` module
2. Implement a custom LLM class compatible with LangChain
3. Add input formatting and output parsing functions
4. Implement retry logic and error handling

Begin by implementing the core LLM wrapper class that integrates with LangChain.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">custom_reasoning_llm.py</h4>
                    <pre><code class="language-python">
import logging
import json
import re
import time
from typing import Dict, List, Any, Optional, Union, Mapping, Callable
import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# LangChain imports
from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.schema import Generation, LLMResult

class CustomReasoningLLM(LLM):
    """
    Custom LLM class for the reasoning model with OpenAI-compatible API.
    """
    
    def __init__(
        self,
        api_base: str,
        api_key: str,
        model_name: str = "reasoning-model",
        max_tokens: int = 2000,
        temperature: float = 0.2,
        max_retries: int = 3,
        timeout: int = 120,
        **kwargs
    ):
        """
        Initialize the custom reasoning LLM.
        
        Args:
            api_base: Base URL for the API endpoint
            api_key: API key for authentication
            model_name: Name of the model to use
            max_tokens: Maximum tokens to generate
            temperature: Temperature for sampling
            max_retries: Maximum number of retries
            timeout: Timeout for API calls in seconds
        """
        super().__init__(**kwargs)
        self.api_base = api_base.rstrip("/")
        self.api_key = api_key
        self.model_name = model_name
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.max_retries = max_retries
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
    
    @property
    def _llm_type(self) -> str:
        """Return the type of LLM."""
        return "custom_reasoning_llm"
    
    def _format_context(self, context: List[Dict[str, Any]]) -> str:
        """
        Format context information for the model input.
        
        Args:
            context: List of context items
            
        Returns:
            Formatted context string
        """
        if not context:
            return "No additional context provided."
        
        formatted = ["Here is relevant information from incident reports:"]
        
        for i, item in enumerate(context, 1):
            formatted.append(f"\n--- Document {i} ---")
            
            # Add metadata if available
            if "metadata" in item:
                metadata = item["metadata"]
                incident_id = metadata.get("incident_id", "Unknown")
                doc_id = metadata.get("document_id", "Unknown")
                section = metadata.get("section_title", "Unknown section")
                
                formatted.append(f"Incident: {incident_id}")
                formatted.append(f"Document: {doc_id}")
                formatted.append(f"Section: {section}")
            
            # Add content
            if "text" in item:
                formatted.append(f"Content: {item['text']}")
            elif "content" in item:
                formatted.append(f"Content: {item['content']}")
        
        return "\n".join(formatted)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((requests.RequestException, TimeoutError))
    )
    def _call_api(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        Call the reasoning model API with retry logic.
        
        Args:
            prompt: Input prompt
            **kwargs: Additional arguments for the API call
            
        Returns:
            API response as a dictionary
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "model": self.model_name,
            "prompt": prompt,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            **kwargs
        }
        
        try:
            response = requests.post(
                f"{self.api_base}/v1/completions",
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            self.logger.error(f"API call failed: {e}")
            raise
    
    def _extract_thinking(self, text: str) -> Tuple[str, str]:
        """
        Extract thinking process from <think></think> tags.
        
        Args:
            text: Model output text
            
        Returns:
            Tuple of (cleaned_response, thinking_content)
        """
        # Extract thinking content
        think_pattern = r'<think>(.*?)</think>'
        think_match = re.search(think_pattern, text, re.DOTALL)
        
        if think_match:
            thinking = think_match.group(1).strip()
            # Remove the thinking tags from the response
            cleaned_response = re.sub(think_pattern, '', text, flags=re.DOTALL).strip()
            return cleaned_response, thinking
        else:
            # No thinking tags found
            return text, ""
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs
    ) -> str:
        """
        Call the reasoning model.
        
        Args:
            prompt: Input prompt
            stop: List of stop sequences
            run_manager: Callback manager
            **kwargs: Additional arguments
            
        Returns:
            Model output text
        """
        self.logger.debug(f"Calling reasoning model with prompt: {prompt[:100]}...")
        
        # Process additional kwargs
        context = kwargs.pop("context", [])
        
        # Format context if provided
        if context:
            formatted_context = self._format_context(context)
            prompt = f"{prompt}\n\nContext:\n{formatted_context}"
        
        # Add stop sequences if provided
        if stop:
            kwargs["stop"] = stop
        
        try:
            # Call the API
            response = self._call_api(prompt, **kwargs)
            
            # Extract the text from the response
            if "choices" in response and response["choices"]:
                text = response["choices"][0].get("text", "")
            else:
                text = ""
                self.logger.warning("No text found in model response")
            
            # Extract thinking process
            cleaned_response, thinking = self._extract_thinking(text)
            
            # Store thinking for later retrieval
            self._last_thinking = thinking
            
            return cleaned_response
            
        except Exception as e:
            self.logger.error(f"Error in model call: {e}", exc_info=True)
            return f"Error: {str(e)}"
    
    def get_last_thinking(self) -> str:
        """
        Get the thinking process from the last model call.
        
        Returns:
            Thinking content as a string
        """
        return getattr(self, "_last_thinking", "")

class ReasoningLLMWithContext(CustomReasoningLLM):
    """
    Extension of CustomReasoningLLM that automatically integrates
    retrieved context with the query.
    """
    
    def __init__(
        self,
        api_base: str,
        api_key: str,
        retrieval_service,
        max_context_items: int = 5,
        **kwargs
    ):
        """
        Initialize the reasoning LLM with context.
        
        Args:
            api_base: Base URL for the API endpoint
            api_key: API key for authentication
            retrieval_service: Service for retrieving context
            max_context_items: Maximum number of context items to include
            **kwargs: Additional arguments for CustomReasoningLLM
        """
        super().__init__(api_base=api_base, api_key=api_key, **kwargs)
        self.retrieval_service = retrieval_service
        self.max_context_items = max_context_items
    
    def _get_context_for_query(self, query: str) -> List[Dict[str, Any]]:
        """
        Retrieve context for a query.
        
        Args:
            query: User query
            
        Returns:
            List of context items
        """
        try:
            # Call the retrieval service
            results = self.retrieval_service.retrieve(
                query=query,
                top_k=self.max_context_items
            )
            
            # Extract context items
            context_items = []
            
            if "results" in results:
                for result in results["results"]:
                    context_items.append({
                        "text": result.text,
                        "metadata": result.metadata
                    })
            
            return context_items
            
        except Exception as e:
            self.logger.error(f"Error retrieving context: {e}", exc_info=True)
            return []
    
    def generate_with_context(
        self,
        query: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response for a query with automatically retrieved context.
        
        Args:
            query: User query
            system_prompt: Optional system prompt
            **kwargs: Additional arguments for the model call
            
        Returns:
            Dictionary with response, thinking, and context
        """
        self.logger.info(f"Generating response with context for query: {query}")
        
        # Retrieve context
        context = self._get_context_for_query(query)
        
        # Prepare the full prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\nQuestion: {query}"
        else:
            full_prompt = f"Question: {query}"
        
        # Generate response
        response = self._call(full_prompt, context=context, **kwargs)
        
        # Get thinking process
        thinking = self.get_last_thinking()
        
        return {
            "query": query,
            "response": response,
            "thinking": thinking,
            "context": context
        }

def create_reasoning_chain(
    api_base: str,
    api_key: str,
    retrieval_service,
    system_prompt: Optional[str] = None
) -> Callable:
    """
    Create a reasoning chain function that processes queries with context.
    
    Args:
        api_base: Base URL for the API endpoint
        api_key: API key for authentication
        retrieval_service: Service for retrieving context
        system_prompt: Optional system prompt
        
    Returns:
        Function that processes queries and returns responses with reasoning
    """
    llm = ReasoningLLMWithContext(
        api_base=api_base,
        api_key=api_key,
        retrieval_service=retrieval_service
    )
    
    def process_query(query: str, **kwargs) -> Dict[str, Any]:
        """
        Process a query with the reasoning chain.
        
        Args:
            query: User query
            **kwargs: Additional arguments
            
        Returns:
            Response with reasoning
        """
        return llm.generate_with_context(query, system_prompt, **kwargs)
    
    return process_query
                    </code></pre>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">6</div>
                <h2 class="text-2xl font-bold m-0">Chainlit UI Integration</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement Chainlit UI for PIR RAG Application

Create a Python module that implements a user interface using Chainlit for the PIR RAG application. The UI should:

1. Allow users to input queries about incidents
2. Display the ReAct agent's response in a user-friendly format
3. Show reasoning details in a collapsible block
4. Properly render any SVG content from the reasoning LLM
5. Display retrieved document snippets with source information
6. Implement the "ask user" functionality for confirming email actions
7. Provide a clean and intuitive user experience

## Requirements:
- Use Chainlit for the UI implementation
- Show reasoning details in a collapsible element
- Properly render SVG content from the model
- Implement "ask user" for confirming email actions
- Display source information for retrieved snippets
- Show progress indicators during processing
- Handle errors gracefully with user-friendly messages

## Expected Inputs:
- Configuration for the application (model endpoints, API keys, etc.)
- User queries through the Chainlit interface

## Expected Outputs:
- Interactive chat interface with responses from the agent
- Collapsible reasoning details
- Rendered SVG content when present
- Confirmations for critical actions like sending emails

## Implementation Guidelines:
1. Create a `chainlit_app.py` module
2. Implement the main app with proper initialization
3. Create message formatting functions for different content types
4. Implement collapsible elements for reasoning details
5. Add the "ask user" functionality for email confirmations

Begin by implementing the main Chainlit application structure.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">chainlit_app.py</h4>
                    <pre><code class="language-python">
import os
import re
import sys
import logging
import json
from typing import Dict, List, Any, Optional, Union

# Chainlit imports
import chainlit as cl
from chainlit.playground.config import add_llm_provider
from chainlit.message import Message

# Local imports
from pir_agent import PIRAgent
from semantic_search import PIRRetrievalService
from custom_reasoning_llm import CustomReasoningLLM, ReasoningLLMWithContext

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chainlit_app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize services and components
retrieval_service = None
reasoning_llm = None
pir_agent = None

@cl.on_settings_update
def update_settings(settings):
    """Update settings when changed in the UI."""
    logger.info(f"Settings updated: {settings}")

@cl.password_auth_callback
def auth_callback(username: str, password: str) -> Optional[Dict[str, Any]]:
    """Simple password authentication for the app."""
    # This is a very basic authentication, in production use a proper auth system
    if username == "admin" and password == "password":
        return {"username": username, "role": "admin"}
    return None

def extract_svg(text: str) -> List[Dict[str, str]]:
    """
    Extract SVG content from text.
    
    Args:
        text: Text that may contain SVG content
        
    Returns:
        List of dictionaries with text and svg content
    """
    # Pattern to match SVG content
    svg_pattern = r'<svg.*?</svg>'
    
    # Find all SVG matches
    svg_matches = re.finditer(svg_pattern, text, re.DOTALL)
    
    result = []
    last_end = 0
    
    for match in svg_matches:
        start, end = match.span()
        
        # Add text before SVG
        if start > last_end:
            result.append({"type": "text", "content": text[last_end:start]})
        
        # Add SVG content
        result.append({"type": "svg", "content": match.group()})
        last_end = end
    
    # Add remaining text
    if last_end < len(text):
        result.append({"type": "text", "content": text[last_end:]})
    
    return result if result else [{"type": "text", "content": text}]

async def process_thinking(thinking: str) -> cl.Message:
    """
    Process and display thinking details in a collapsible element.
    
    Args:
        thinking: Thinking content from the model
        
    Returns:
        Chainlit message with thinking details
    """
    if not thinking:
        return None
    
    # Create a collapsible element for thinking
    thinking_elements = []
    
    # Process thinking content for any SVG
    thinking_parts = extract_svg(thinking)
    
    for part in thinking_parts:
        if part["type"] == "svg":
            # Add SVG as an element
            thinking_elements.append(
                cl.Image(
                    content=part["content"].encode(),
                    name="reasoning_diagram",
                    display="inline",
                    size="medium"
                )
            )
        else:
            # Add text
            thinking_elements.append(
                cl.Text(
                    content=part["content"],
                    name="reasoning_text"
                )
            )
    
    # Create collapsible element
    thinking_element = cl.Collapse(
        title="Reasoning Details",
        content=thinking_elements
    )
    
    # Create and send the message
    message = cl.Message(
        author="Assistant",
        content="",
        elements=[thinking_element]
    )
    
    return message

async def format_search_results(results: List[Dict[str, Any]]) -> List[cl.Element]:
    """
    Format search results as Chainlit elements.
    
    Args:
        results: List of search results
        
    Returns:
        List of Chainlit elements
    """
    elements = []
    
    # Create accordion for search results
    items = []
    
    for i, result in enumerate(results, 1):
        # Extract metadata
        metadata = result.get("metadata", {})
        incident_id = metadata.get("incident_id", "Unknown")
        doc_id = metadata.get("document_id", "Unknown")
        section = metadata.get("section_title", "Unknown section")
        source_url = metadata.get("source_url", "#")
        
        # Create item content
        content = f"""
### {section}

**Incident:** {incident_id}  
**Document:** {doc_id}

{result.get("text", "")}

[Source]({source_url})
        """
        
        items.append(cl.AccordionItem(
            title=f"Result {i}: {section} - {incident_id}",
            content=content
        ))
    
    if items:
        elements.append(cl.Accordion(items=items, name="search_results"))
    
    return elements

async def process_email_confirmation(to: str, subject: str, body: str) -> bool:
    """
    Ask the user for confirmation before sending an email.
    
    Args:
        to: Recipient email address
        subject: Email subject
        body: Email body
        
    Returns:
        True if user confirms, False otherwise
    """
    # Create a message to show email details
    email_content = f"""
### Email Details

**To:** {to}
**Subject:** {subject}

**Body:**
{body}
    """
    
    # Ask for user confirmation
    res = await cl.AskUserMessage(
        content=email_content,
        author="Assistant",
        actions=[
            cl.Action(name="confirm", value="yes", label="✅ Send Email"),
            cl.Action(name="cancel", value="no", label="❌ Cancel")
        ]
    ).send()
    
    return res and res.get("value") == "yes"

@cl.on_chat_start
async def on_chat_start():
    """Initialize the chat session."""
    # Display welcome message
    await cl.Message(
        content="👋 Welcome to the Incident PIR Assistant! I can help you find information about incident reports and their details. What would you like to know?",
        author="Assistant"
    ).send()
    
    try:
        # Initialize global components
        global retrieval_service, reasoning_llm, pir_agent
        
        # Load configuration from environment or use defaults
        vector_db_path = os.environ.get("VECTOR_DB_PATH", "./vector_db")
        embedding_endpoint = os.environ.get("EMBEDDING_ENDPOINT", "https://api.example.com/embeddings")
        embedding_api_key = os.environ.get("EMBEDDING_API_KEY", "dummy_key")
        reasoning_endpoint = os.environ.get("REASONING_ENDPOINT", "https://api.example.com/reasoning")
        reasoning_api_key = os.environ.get("REASONING_API_KEY", "dummy_key")
        
        # Initialize retrieval service
        cl.user_session.set("status", "Initializing retrieval service...")
        
        retrieval_service = PIRRetrievalService(
            vector_db_path=vector_db_path,
            embedding_endpoint=embedding_endpoint,
            embedding_api_key=embedding_api_key
        )
        
        # Initialize reasoning LLM
        cl.user_session.set("status", "Initializing reasoning model...")
        
        reasoning_llm = ReasoningLLMWithContext(
            api_base=reasoning_endpoint,
            api_key=reasoning_api_key,
            retrieval_service=retrieval_service
        )
        
        # Initialize PIR agent
        cl.user_session.set("status", "Initializing PIR agent...")
        
        pir_agent = PIRAgent(
            llm=reasoning_llm,
            retrieval_service=retrieval_service,
            verbose=True
        )
        
        # Store components in session
        cl.user_session.set("retrieval_service", retrieval_service)
        cl.user_session.set("reasoning_llm", reasoning_llm)
        cl.user_session.set("pir_agent", pir_agent)
        
        cl.user_session.set("status", "Ready")
        
        logger.info("Chat session initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing chat session: {e}", exc_info=True)
        
        await cl.Message(
            content=f"⚠️ Error initializing the application: {str(e)}. Please contact support.",
            author="System"
        ).send()

@cl.on_message
async def on_message(message: cl.Message):
    """Process user messages."""
    query = message.content
    
    try:
        # Get components from session
        pir_agent = cl.user_session.get("pir_agent")
        reasoning_llm = cl.user_session.get("reasoning_llm")
        
        if not pir_agent or not reasoning_llm:
            await cl.Message(
                content="⚠️ Application not properly initialized. Please refresh the page and try again.",
                author="System"
            ).send()
            return
        
        # Show thinking indicator
        thinking_msg = cl.Message(content="Thinking...", author="Assistant")
        await thinking_msg.send()
        
        # Process the query with the reasoning LLM
        result = reasoning_llm.generate_with_context(
            query=query,
            system_prompt="You are an Incident Management Assistant specialized in helping users find information in Post-Implementation Review (PIR) documents."
        )
        
        # Extract response and thinking
        response = result.get("response", "")
        thinking = result.get("thinking", "")
        context = result.get("context", [])
        
        # Process any SVG content in the response
        response_parts = extract_svg(response)
        
        # Update thinking message with the response
        response_content = "".join(
            part["content"] if part["type"] == "text" else ""
            for part in response_parts
        )
        
        await thinking_msg.update(content=response_content)
        
        # Add SVG elements if any
        svg_elements = []
        for part in response_parts:
            if part["type"] == "svg":
                svg_elements.append(
                    cl.Image(
                        content=part["content"].encode(),
                        name="response_diagram",
                        display="inline",
                        size="medium"
                    )
                )
        
        if svg_elements:
            await thinking_msg.elements.extend(svg_elements)
            await thinking_msg.update()
        
        # Add search result elements if available
        if context:
            search_elements = await format_search_results(context)
            if search_elements:
                await thinking_msg.elements.extend(search_elements)
                await thinking_msg.update()
        
        # Process and display thinking details if available
        if thinking:
            thinking_msg = await process_thinking(thinking)
            if thinking_msg:
                await thinking_msg.send()
        
        # Check for email sending intent
        email_pattern = r'send\s+email\s+to\s+([^\s,]+@[^\s,]+)'
        email_match = re.search(email_pattern, query.lower())
        
        if email_match:
            # Extract potential recipient
            potential_recipient = email_match.group(1)
            
            # Generate email content based on the query and results
            email_subject = f"Information about incident mentioned in query: {query[:50]}..."
            email_body = f"""
Hello,

Here is the information you requested about the incident:

{response}

This email was generated automatically based on a request through the PIR Assistant.
            """
            
            # Ask for confirmation
            confirmed = await process_email_confirmation(
                to=potential_recipient,
                subject=email_subject,
                body=email_body
            )
            
            if confirmed:
                # Use the email tool from the agent
                for tool in pir_agent.tools:
                    if tool.name == "send_email":
                        email_result = tool._run(
                            to=potential_recipient,
                            subject=email_subject,
                            body=email_body
                        )
                        
                        await cl.Message(
                            content=f"✅ {email_result}",
                            author="Assistant"
                        ).send()
                        break
            else:
                await cl.Message(
                    content="❌ Email sending cancelled.",
                    author="Assistant"
                ).send()
        
    except Exception as e:
        logger.error(f"Error processing message: {e}", exc_info=True)
        
        await cl.Message(
            content=f"⚠️ I encountered an error while processing your query: {str(e)}. Please try again or rephrase your question.",
            author="Assistant"
        ).send()

if __name__ == "__main__":
    # The application will be started by running `chainlit run chainlit_app.py`
    logger.info("Chainlit app initialized, waiting for chainlit to run it")
                    </code></pre>

                    <h4 class="font-medium">config.toml</h4>
                    <pre><code class="language-toml">
[project]
# Project settings
name = "Incident PIR Assistant"
description = "A Retrieval-Augmented Generation application for Incident Post-Implementation Review documents"

[UI]
# UI customization
name = "PIR Assistant"
description = "Ask questions about incident reports and get detailed answers"
theme = "light"

[features]
# Enable/disable features
prompt_playground = true
prompt_history = true
multi_modal = true
speech_to_text = true

[playground]
# Playground settings
Prompt = true
                    </code></pre>
                </div>
            </div>
        </div>

        <div class="mb-12">
            <div class="flex items-center mb-4">
                <div class="step-number">7</div>
                <h2 class="text-2xl font-bold m-0">Email Tool Implementation (Stub)</h2>
            </div>
            
            <div class="prompt-block">
                <h3 class="text-xl font-semibold text-blue-700">AI IDE Prompt</h3>
                <div class="bg-gray-50 p-4 rounded-md border border-gray-200 whitespace-pre-wrap">
# Implement Email Tool Stub

Create a Python module that implements a stub version of the email sending tool for the PIR RAG application. This tool should:

1. Mimic the interface of a real email sending tool
2. Log email details instead of actually sending emails
3. Integrate with Chainlit's "ask user" functionality for approval
4. Provide detailed feedback about what would happen in a real implementation
5. Include proper error handling and validation

## Requirements:
- Implement a realistic email tool interface
- Log email details (recipient, subject, body) instead of sending real emails
- Validate email addresses and input formats
- Use Chainlit's "ask user" functionality for approval
- Provide clear feedback about the simulated email sending process

## Expected Inputs:
- Recipient email address
- Subject line
- Email body content
- Optional CC/BCC recipients
- Optional attachments (references to files)

## Expected Outputs:
- Success or error message
- Log entry with email details
- Simulated email ID for reference

## Implementation Guidelines:
1. Create an `email_tool.py` module
2. Implement the EmailTool class with appropriate methods
3. Add validation and error handling
4. Include logging of all operations
5. Integrate with Chainlit's user confirmation

Begin by implementing the core EmailTool class with the necessary methods.
                </div>
            </div>

            <div class="mt-8">
                <h3 class="text-xl font-semibold text-blue-700">Implementation Details</h3>
                <div class="space-y-4">
                    <h4 class="font-medium">email_tool.py</h4>
                    <pre><code class="language-python">
import logging
import re
import json
import uuid
import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr, validator

import chainlit as cl
from langchain.tools import BaseTool

class EmailRecipient(BaseModel):
    """Model for email recipient."""
    email: str = Field(..., description="Recipient email address")
    name: Optional