<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incident PIR RAG Application - PRD Creation Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9fafb;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #1a202c;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        h1 { font-size: 2.25rem; }
        h2 { font-size: 1.875rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }
        p, ul, ol {
            margin-bottom: 1rem;
        }
        ul, ol {
            padding-left: 1.5rem;
        }
        ul { list-style-type: disc; }
        ol { list-style-type: decimal; }
        code {
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            background-color: #edf2f7;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }
        pre {
            background-color: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1.5rem;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            color: inherit;
            font-size: 0.875rem;
        }
        blockquote {
            border-left: 4px solid #4a5568;
            padding-left: 1rem;
            color: #4a5568;
            font-style: italic;
            margin: 1.5rem 0;
        }
        a {
            color: #3182ce;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .prompt-box {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            background-color: white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        .implementation-box {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            background-color: white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        .tip-box {
            background-color: #ebf8ff;
            border-left: 4px solid #4299e1;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 0.25rem;
        }
        .warning-box {
            background-color: #fffaf0;
            border-left: 4px solid #ed8936;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 0.25rem;
        }
        .highlight {
            background-color: #faf5ff;
            border-left: 4px solid #805ad5;
            padding: 1rem;
            margin: 1.5rem 0;
            border-radius: 0.25rem;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
        }
        table, th, td {
            border: 1px solid #e2e8f0;
        }
        th, td {
            padding: 0.75rem;
            text-align: left;
        }
        th {
            background-color: #f7fafc;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f7fafc;
        }
    </style>
</head>
<body>
    <div class="container mx-auto">
        <h1 class="text-4xl font-bold mb-8 text-center">Incident PIR RAG Application - Project Requirements Document</h1>
        
        <div class="bg-blue-50 p-4 rounded-lg mb-8 border-l-4 border-blue-500">
            <h3 class="text-xl font-semibold text-blue-700 mb-2">
                <i class="fas fa-info-circle mr-2"></i>Overview
            </h3>
            <p>This guide provides detailed instructions for creating a comprehensive Project Requirements Document (PRD) for the Incident PIR RAG Application. The PRD will serve as the central reference document for the project, outlining all requirements, features, technical specifications, and implementation details with a specific focus on leveraging LangChain and LlamaIndex frameworks.</p>
        </div>

        <h2 class="text-2xl font-bold mt-8 mb-4 border-b-2 border-gray-200 pb-2">9. Project Requirements Document - AI IDE Prompts</h2>
        
        <div class="prompt-box">
            <h3 class="text-xl font-semibold mb-4 text-indigo-700">
                <i class="fas fa-terminal mr-2"></i>Prompt for Cursor AI
            </h3>
            <div class="bg-gray-50 p-4 rounded">
                <p class="font-mono text-sm">Create a comprehensive Project Requirements Document (PRD) for an Incident PIR RAG Application that leverages LangChain and LlamaIndex frameworks. The application will index Confluence incident reports, retrieve relevant information based on user queries, and use a ReAct agent to orchestrate actions.</p>
                <p class="font-mono text-sm mt-2">The PRD should include:</p>
                <ul class="font-mono text-sm list-disc pl-8 mt-2">
                    <li>Project overview and objectives</li>
                    <li>Core features and functional requirements</li>
                    <li>Technical architecture, specifically addressing how LangChain and LlamaIndex will be utilized</li>
                    <li>Data flow diagrams</li>
                    <li>API specifications</li>
                    <li>Component descriptions with specific emphasis on retrieval strategies leveraging LlamaIndex's structured data handling and LangChain's agent capabilities</li>
                    <li>UI/UX design guidelines for the Chainlit interface</li>
                    <li>Performance requirements and optimization techniques</li>
                    <li>Testing methodology</li>
                    <li>Deployment strategy</li>
                    <li>Future enhancement roadmap</li>
                </ul>
                <p class="font-mono text-sm mt-2">Format this as a professional software requirements specification document with proper sections, tables, and diagrams. Use markdown for text formatting and include placeholders for diagrams.</p>
            </div>
        </div>

        <div class="prompt-box">
            <h3 class="text-xl font-semibold mb-4 text-green-700">
                <i class="fas fa-terminal mr-2"></i>Prompt for Roo Cline AI
            </h3>
            <div class="bg-gray-50 p-4 rounded">
                <p class="font-mono text-sm">Generate a Project Requirements Document for an Incident PIR RAG application built with LangChain and LlamaIndex. The application should meet these objectives:</p>
                <ul class="font-mono text-sm list-disc pl-8 mt-2">
                    <li>Index and retrieve information from Confluence incident reports</li>
                    <li>Use LlamaIndex's structured data handling for HTML parsing and sectioned retrieval</li>
                    <li>Implement LangChain's ReAct agent for orchestrating multi-step reasoning and actions</li>
                    <li>Provide a Chainlit UI for user interaction</li>
                    <li>Include an email notification tool with user approval flow</li>
                </ul>
                <p class="font-mono text-sm mt-2">Focus on technical details including:</p>
                <ul class="font-mono text-sm list-disc pl-8 mt-2">
                    <li>How the application will leverage LlamaIndex's RecursiveRetriever for section-aware retrieval</li>
                    <li>Implementation of LangChain's ReAct agent with custom tools</li>
                    <li>Specific retrieval strategies for extracting information from structured incident reports</li>
                    <li>Integration points with Confluence APIs</li>
                    <li>Vector database selection and configuration</li>
                    <li>Custom embedding and reasoning model integration</li>
                </ul>
                <p class="font-mono text-sm mt-2">Document should include sequence diagrams for key workflows, component diagrams showing LangChain and LlamaIndex integration, and API specifications for all components.</p>
            </div>
        </div>

        <div class="prompt-box">
            <h3 class="text-xl font-semibold mb-4 text-red-700">
                <i class="fas fa-terminal mr-2"></i>Prompt for Windsurf AI
            </h3>
            <div class="bg-gray-50 p-4 rounded">
                <p class="font-mono text-sm">Write a detailed Project Requirements Document (PRD) for an Incident PIR RAG application that uses LangChain and LlamaIndex to provide advanced retrieval and reasoning capabilities.</p>
                <p class="font-mono text-sm mt-2">Structure the PRD with these main sections:</p>
                <ol class="font-mono text-sm list-decimal pl-8 mt-2">
                    <li><strong>Executive Summary</strong>: High-level overview of the project's purpose, goals, and expected outcomes</li>
                    <li><strong>Project Scope</strong>: Detailed description of what's included and excluded</li>
                    <li><strong>User Requirements</strong>: User stories, personas, and use cases</li>
                    <li><strong>Functional Requirements</strong>: Feature specifications for each component</li>
                    <li><strong>Technical Architecture</strong>: Comprehensive description of how LangChain and LlamaIndex will be integrated, including:
                        <ul class="list-disc pl-8">
                            <li>LlamaIndex document processors and retrievers for structured data</li>
                            <li>LangChain ReAct agent implementation and tool integration</li>
                            <li>Custom embedding and LLM integration</li>
                            <li>Storage solutions and data persistence</li>
                        </ul>
                    </li>
                    <li><strong>Implementation Plan</strong>: Timeline, milestones, and resource requirements</li>
                    <li><strong>Testing Strategy</strong>: Testing methodologies including unit, integration, and end-to-end testing</li>
                    <li><strong>Deployment Plan</strong>: Environment specifications and deployment procedures</li>
                </ol>
                <p class="font-mono text-sm mt-2">Pay special attention to explaining the specific advantages of LangChain and LlamaIndex in this application, and how their capabilities will be leveraged for optimal performance and accuracy.</p>
            </div>
        </div>

        <h2 class="text-2xl font-bold mt-12 mb-4 border-b-2 border-gray-200 pb-2">9. Project Requirements Document - Implementation Details</h2>
        
        <div class="implementation-box">
            <h3 class="text-xl font-semibold mb-4 text-purple-700">
                <i class="fas fa-file-alt mr-2"></i>PRD Structure and Content Guide
            </h3>
            
            <p>Below is a detailed guide for creating a comprehensive Project Requirements Document (PRD) for the Incident PIR RAG Application. The PRD should be structured to clearly communicate all aspects of the project while emphasizing how LangChain and LlamaIndex frameworks will be leveraged.</p>
            
            <h4 class="text-lg font-semibold mt-6 mb-2">1. Executive Summary</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Provide a concise overview (1-2 paragraphs) of the Incident PIR RAG Application, including:</p>
                <ul class="list-disc pl-8">
                    <li>Primary purpose: To enable efficient retrieval and analysis of incident Post-Implementation Review (PIR) reports stored in Confluence</li>
                    <li>Key capabilities: Structure-aware retrieval, reasoning over retrieved content, and action orchestration</li>
                    <li>Primary technologies: LangChain, LlamaIndex, Chainlit, Custom Embedding and Reasoning LLMs</li>
                    <li>Expected business impact: Improved incident knowledge management, faster resolution of recurring issues, and enhanced team learning</li>
                </ul>
                
                <div class="bg-yellow-50 p-3 rounded mt-3 border-l-4 border-yellow-400">
                    <p><strong>Example Content:</strong></p>
                    <p class="italic">The Incident PIR RAG Application is designed to transform how teams access and utilize knowledge from past incident reports. By leveraging advanced retrieval and reasoning capabilities provided by LlamaIndex and LangChain, the application enables users to query incident data using natural language and receive highly relevant information from structured PIR reports stored in Confluence. The system further extends functionality by enabling action orchestration through a ReAct agent, allowing users to trigger workflows such as sending notification emails based on retrieved information.</p>
                </div>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">2. Project Scope</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Define the boundaries of the project by clearly stating what is included and excluded:</p>
                
                <p class="font-semibold mt-3">In Scope:</p>
                <ul class="list-disc pl-8">
                    <li>Confluence integration for loading incident PIR reports</li>
                    <li>Structure-aware parsing of HTML-formatted incident reports</li>
                    <li>Custom embedding generation using OpenAI-compatible API</li>
                    <li>Vector storage and retrieval system</li>
                    <li>Implementation of specialized retrieval strategies for incident reports</li>
                    <li>LangChain ReAct agent for reasoning and action orchestration</li>
                    <li>Email notification tool (stub implementation)</li>
                    <li>Chainlit user interface</li>
                    <li>Basic authentication and access control</li>
                </ul>
                
                <p class="font-semibold mt-3">Out of Scope:</p>
                <ul class="list-disc pl-8">
                    <li>Creation or modification of incident reports in Confluence</li>
                    <li>Integration with incident management systems beyond Confluence</li>
                    <li>Production deployment infrastructure</li>
                    <li>Full implementation of email notification service (only stub provided)</li>
                    <li>User management system</li>
                    <li>Mobile application</li>
                </ul>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">3. User Requirements</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Define user personas and their needs through user stories:</p>
                
                <p class="font-semibold mt-3">Key User Personas:</p>
                <ol class="list-decimal pl-8">
                    <li><strong>Incident Responder</strong>: Team members actively responding to ongoing incidents</li>
                    <li><strong>Service Owner</strong>: Manager responsible for service reliability</li>
                    <li><strong>Technical Lead</strong>: Engineer leading technical incident resolution</li>
                    <li><strong>Knowledge Manager</strong>: Person responsible for organizing and maintaining incident knowledge</li>
                </ol>
                
                <p class="font-semibold mt-3">User Stories:</p>
                <div class="ml-4">
                    <p class="font-semibold">Incident Responder:</p>
                    <ul class="list-disc pl-8">
                        <li>As an incident responder, I want to quickly find similar past incidents so that I can apply previous resolution steps.</li>
                        <li>As an incident responder, I want to query for specific parts of incident reports (e.g., root cause, timeline) so that I can focus on relevant information.</li>
                        <li>As an incident responder, I want to notify stakeholders about my findings by sending emails directly from the application.</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Service Owner:</p>
                    <ul class="list-disc pl-8">
                        <li>As a service owner, I want to understand recurring patterns in incidents so that I can prioritize preventative measures.</li>
                        <li>As a service owner, I want to see actionable insights from past incidents so that I can improve service reliability.</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Technical Lead:</p>
                    <ul class="list-disc pl-8">
                        <li>As a technical lead, I want to retrieve detailed technical information from past incidents so that I can ensure thorough problem resolution.</li>
                        <li>As a technical lead, I want to understand the root causes of similar incidents so that I can develop comprehensive fixes.</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Knowledge Manager:</p>
                    <ul class="list-disc pl-8">
                        <li>As a knowledge manager, I want to ensure incident knowledge is accessible through natural language queries so that team members can find information efficiently.</li>
                        <li>As a knowledge manager, I want to see which incident reports are most frequently accessed so that I can improve their content quality.</li>
                    </ul>
                </div>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">4. Functional Requirements</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Detail the specific functional requirements by system component:</p>
                
                <p class="font-semibold mt-3">4.1 Confluence Integration</p>
                <ul class="list-disc pl-8">
                    <li>System shall connect to Confluence using API token authentication</li>
                    <li>System shall retrieve incident PIR pages based on space key and parent page ID</li>
                    <li>System shall support incremental indexing of new or updated PIR reports</li>
                    <li>System shall parse HTML-formatted incident reports into structured data</li>
                    <li>System shall extract metadata including incident ID, date, team, impact level, etc.</li>
                </ul>
                
                <p class="font-semibold mt-3">4.2 Vectorization and Embedding</p>
                <ul class="list-disc pl-8">
                    <li>System shall generate embeddings for document chunks using custom embedding model</li>
                    <li>System shall create section-aware document chunks using LlamaIndex's HTML parsing capabilities</li>
                    <li>System shall store embeddings in a vector database with associated metadata</li>
                    <li>System shall optimize embedding storage for efficient retrieval</li>
                    <li>System shall support re-embedding when embedding model is updated</li>
                </ul>
                
                <p class="font-semibold mt-3">4.3 Retrieval System</p>
                <ul class="list-disc pl-8">
                    <li>System shall implement specialized retrieval strategies using LlamaIndex:
                        <ul class="list-disc pl-8">
                            <li>HTML structure-aware retrieval for targeting specific sections</li>
                            <li>Table-specific retrieval for extracting tabular data</li>
                            <li>Metadata-filtered retrieval for narrowing results by incident properties</li>
                            <li>Parent-child retrieval for maintaining document context</li>
                        </ul>
                    </li>
                    <li>System shall convert user queries to vector representations</li>
                    <li>System shall perform semantic similarity search to find relevant document chunks</li>
                    <li>System shall re-rank results based on relevance to user query</li>
                    <li>System shall return properly formatted snippets to the user</li>
                </ul>
                
                <p class="font-semibold mt-3">4.4 ReAct Agent</p>
                <ul class="list-disc pl-8">
                    <li>System shall implement a LangChain ReAct agent with the following capabilities:
                        <ul class="list-disc pl-8">
                            <li>Tool-based architecture for modularity</li>
                            <li>Multi-step reasoning about incident information</li>
                            <li>Action planning based on retrieved information</li>
                            <li>Tool selection based on user intent</li>
                        </ul>
                    </li>
                    <li>System shall integrate custom reasoning LLM with OpenAI-compatible API</li>
                    <li>System shall track agent reasoning steps and provide explainable results</li>
                    <li>System shall handle edge cases and error conditions gracefully</li>
                </ul>
                
                <p class="font-semibold mt-3">4.5 Email Tool</p>
                <ul class="list-disc pl-8">
                    <li>System shall provide a stub implementation of email notification tool</li>
                    <li>System shall request user approval via Chainlit before "sending" emails</li>
                    <li>System shall log email details for verification</li>
                    <li>System shall support email templates based on incident types</li>
                    <li>System shall allow customization of email content before sending</li>
                </ul>
                
                <p class="font-semibold mt-3">4.6 Chainlit User Interface</p>
                <ul class="list-disc pl-8">
                    <li>System shall provide a conversational interface for user queries</li>
                    <li>System shall display agent reasoning in collapsible sections</li>
                    <li>System shall support SVG rendering for diagrams</li>
                    <li>System shall provide interactive approval for actions (e.g., sending emails)</li>
                    <li>System shall allow users to modify queries and refine searches</li>
                    <li>System shall provide session history and persistence</li>
                </ul>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">5. Technical Architecture</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Provide a detailed description of the technical architecture with specific focus on LangChain and LlamaIndex integration:</p>
                
                <div class="bg-gray-100 p-4 rounded-lg my-4 border border-gray-300">
                    <p class="font-semibold">Architecture Diagram Placeholder</p>
                    <p class="italic text-sm">Include a detailed architecture diagram showing all system components, data flows, and integration points between LangChain and LlamaIndex.</p>
                </div>
                
                <p class="font-semibold mt-3">5.1 LlamaIndex Components</p>
                <div class="ml-4">
                    <p>The application will leverage the following LlamaIndex components:</p>
                    
                    <p class="font-semibold mt-2">Document Processing:</p>
                    <ul class="list-disc pl-8">
                        <li><code>HTMLNodeParser</code>: For parsing HTML structure of incident reports</li>
                        <li><code>SimpleNodeParser</code>: For basic text chunking</li>
                        <li>Custom node parser for PIR-specific structure</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Retrievers:</p>
                    <ul class="list-disc pl-8">
                        <li><code>VectorIndexRetriever</code>: Base retriever for embedding-based search</li>
                        <li><code>RecursiveRetriever</code>: For section-aware retrieval using document structure</li>
                        <li><code>KeywordNodeRetriever</code>: For targeting specific fields like incident IDs</li>
                        <li>Custom PIR-specific retriever class extending <code>BaseRetriever</code></li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Indices:</p>
                    <ul class="list-disc pl-8">
                        <li><code>VectorStoreIndex</code>: Primary index for similarity search</li>
                        <li><code>KnowledgeGraphIndex</code>: For relationship mapping between incidents</li>
                        <li><code>StructStoreIndex</code>: For structured data from tables</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Response Synthesis:</p>
                    <ul class="list-disc pl-8">
                        <li><code>CompactAndRefine</code>: For handling structured data in responses</li>
                        <li><code>TreeSummarize</code>: For summarizing multiple PIR reports</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Query Engines:</p>
                    <ul class="list-disc pl-8">
                        <li><code>RetrieverQueryEngine</code>: For basic retrieval-based query answering</li>
                        <li><code>SubQuestionQueryEngine</code>: For complex queries requiring multiple retrievals</li>
                    </ul>
                    
                    <div class="bg-blue-50 p-3 rounded-lg my-3 border-l-4 border-blue-500">
                        <p><strong>LlamaIndex Integration Point:</strong> The application will use LlamaIndex's node-based document representation to create a structured view of incident reports that preserves the hierarchical nature of the HTML content and enables precise section and table targeting during retrieval.</p>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">5.2 LangChain Components</p>
                <div class="ml-4">
                    <p>The application will leverage the following LangChain components:</p>
                    
                    <p class="font-semibold mt-2">Agents:</p>
                    <ul class="list-disc pl-8">
                        <li><code>ReActChain</code>: Core agent implementation with reasoning and action capabilities</li>
                        <li><code>AgentExecutor</code>: For executing agent workflows</li>
                        <li>Custom agent type for PIR analysis</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Tools:</p>
                    <ul class="list-disc pl-8">
                        <li>Custom retrieval tool wrapping LlamaIndex retrievers</li>
                        <li>Email notification tool with Chainlit integration</li>
                        <li>Incident analysis tools for specialized reasoning tasks</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Memory:</p>
                    <ul class="list-disc pl-8">
                        <li><code>ConversationBufferMemory</code>: For maintaining conversation context</li>
                        <li><code>VectorStoreRetrieverMemory</code>: For retrieving past interactions</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Embeddings:</p>
                    <ul class="list-disc pl-8">
                        <li><code>OpenAIEmbeddings</code>-compatible wrapper for custom embedding model</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Document Loaders:</p>
                    <ul class="list-disc pl-8">
                        <li><code>ConfluenceLoader</code>: For loading content from Confluence</li>
                        <li><code>HTMLLoader</code>: For processing HTML content</li>
                    </ul>
                    
                    <p class="font-semibold mt-2">Text Splitters:</p>
                    <ul class="list-disc pl-8">
                        <li><code>HTMLHeaderTextSplitter</code>: For splitting by HTML headers</li>
                        <li><code>RecursiveCharacterTextSplitter</code>: For controlled chunking</li>
                    </ul>
                    
                    <div class="bg-blue-50 p-3 rounded-lg my-3 border-l-4 border-blue-500">
                        <p><strong>LangChain Integration Point:</strong> The application will use LangChain's agent framework to orchestrate the retrieval, reasoning, and action processes, allowing for a structured workflow that can handle complex multi-step interactions with the indexed incident data.</p>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">5.3 Integration Between LangChain and LlamaIndex</p>
                <div class="ml-4">
                    <p>The application will integrate LangChain and LlamaIndex in the following ways:</p>
                    
                    <ol class="list-decimal pl-8">
                        <li><strong>Tool Wrapping</strong>: LlamaIndex retrievers will be wrapped as LangChain tools for use by the ReAct agent</li>
                        <li><strong>Query Engine Integration</strong>: LlamaIndex query engines will be integrated with LangChain chains for seamless reasoning over retrieved content</li>
                        <li><strong>Common LLM Interface</strong>: Both frameworks will use the same underlying LLM interfaces for consistent behavior</li>
                        <li><strong>Node Postprocessors</strong>: LlamaIndex node postprocessors will be used to enhance retrieval results before they're processed by LangChain components</li>
                        <li><strong>Response Synthesizers</strong>: LlamaIndex response synthesizers will be used to format and structure agent outputs</li>
                    </ol>
                    
                    <div class="bg-yellow-50 p-3 rounded-lg my-3 border-l-4 border-yellow-400">
                        <p><strong>Implementation Note:</strong> The code will use the latest LangChain and LlamaIndex integration patterns, particularly the <code>LlamaIndexToolSpec</code> for creating LangChain-compatible tools from LlamaIndex components.</p>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">5.4 Data Flow</p>
                <div class="ml-4">
                    <ol class="list-decimal pl-8">
                        <li><strong>Data Ingestion</strong>: LangChain document loaders fetch PIR reports from Confluence</li>
                        <li><strong>Document Processing</strong>: LlamaIndex document processors parse and chunk HTML content</li>
                        <li><strong>Embedding Generation</strong>: Custom embedding model generates vector representations</li>
                        <li><strong>Indexing</strong>: LlamaIndex creates various indices from processed documents</li>
                        <li><strong>Query Processing</strong>: User queries processed by LangChain agent</li>
                        <li><strong>Retrieval</strong>: LlamaIndex retrievers fetch relevant document chunks</li>
                        <li><strong>Reasoning</strong>: LangChain ReAct agent reasons over retrieved content</li>
                        <li><strong>Action Execution</strong>: Agent uses tools (e.g., email) based on reasoning</li>
                        <li><strong>Response Generation</strong>: Final response synthesized and presented to user</li>
                    </ol>
                </div>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">6. Non-Functional Requirements</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p class="font-semibold mt-3">6.1 Performance</p>
                <ul class="list-disc pl-8">
                    <li>System shall return query results within 5 seconds for 90% of queries</li>
                    <li>System shall support indexing of up to 10,000 incident reports</li>
                    <li>System shall handle up to 50 concurrent users</li>
                    <li>System shall efficiently chunk and embed documents to optimize retrieval speed</li>
                    <li>System shall implement caching for frequently accessed queries</li>
                </ul>
                
                <p class="font-semibold mt-3">6.2 Scalability</p>
                <ul class="list-disc pl-8">
                    <li>System shall support horizontal scaling of vector database</li>
                    <li>System shall implement batch processing for large document sets</li>
                    <li>System shall support distributed deployment if needed</li>
                </ul>
                
                <p class="font-semibold mt-3">6.3 Security</p>
                <ul class="list-disc pl-8">
                    <li>System shall enforce authentication for all API access</li>
                    <li>System shall securely store credentials and API keys</li>
                    <li>System shall log all access and actions for audit purposes</li>
                    <li>System shall implement appropriate data encryption</li>
                </ul>
                
                <p class="font-semibold mt-3">6.4 Reliability</p>
                <ul class="list-disc pl-8">
                    <li>System shall maintain 99% uptime</li>
                    <li>System shall implement error handling and recovery mechanisms</li>
                    <li>System shall provide monitoring and alerting capabilities</li>
                </ul>
                
                <p class="font-semibold mt-3">6.5 Maintainability</p>
                <ul class="list-disc pl-8">
                    <li>System shall follow modular design principles</li>
                    <li>System shall include comprehensive documentation</li>
                    <li>System shall implement logging for troubleshooting</li>
                    <li>System shall support version tracking of models and indices</li>
                </ul>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">7. Retrieval Strategy Implementation</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Detailed description of the specialized retrieval strategy implementation using LlamaIndex and LangChain:</p>
                
                <p class="font-semibold mt-3">7.1 Document Processing Pipeline</p>
                <div class="ml-4">
                    <p>The system will implement a specialized document processing pipeline for incident PIR reports:</p>
                    
                    <div class="bg-gray-100 p-3 rounded-lg my-3 border border-gray-300">
                        <pre><code>from llama_index import SimpleDirectoryReader
from llama_index.node_parser import HTMLNodeParser
from langchain.document_loaders import ConfluenceLoader

# Custom PIR HTML parser
pir_parser = HTMLNodeParser(
    tags=["h2", "p", "table", "tr", "td"],
    chunk_level=["h2", "table"],
    fields_to_metadata={
        "id": "incident_id",
        "date": "incident_date",
        "team": "team_name"
    }
)

# Confluence loader
confluence_loader = ConfluenceLoader(
    url="https://your-confluence-url",
    username="your-username",
    api_key="your-api-key"
)

# Load PIR pages from Confluence
pages = confluence_loader.load(
    space_key="INCIDENTS",
    parent_id="1234567890",
    include_attachments=False,
    limit=100
)

# Process documents with custom parser
nodes = pir_parser.get_nodes_from_documents(pages)

# Add additional metadata
for node in nodes:
    # Extract incident metadata from content
    if "Incident Number" in node.text:
        # Extract and add incident number to metadata
        incident_number = extract_incident_number(node.text)
        node.metadata["incident_number"] = incident_number</code></pre>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">7.2 Multi-Index Strategy</p>
                <div class="ml-4">
                    <p>The system will implement multiple specialized indices for different query types:</p>
                    
                    <div class="bg-gray-100 p-3 rounded-lg my-3 border border-gray-300">
                        <pre><code>from llama_index import VectorStoreIndex, KnowledgeGraphIndex, StructStoreIndex
from llama_index.ingestion import IngestionPipeline

# Create vector index for semantic search
vector_index = VectorStoreIndex(nodes)

# Create knowledge graph for relationship queries
kg_index = KnowledgeGraphIndex(
    nodes,
    include_metadata=True,
    max_triplets_per_chunk=10
)

# Create structured index for tabular data
struct_index = StructStoreIndex.from_documents(
    pages,
    sql_database=sql_database
)

# Combined ingestion pipeline
ingestion_pipeline = IngestionPipeline(
    transformations=[
        node_parser,
        vector_index.storage_context.docstore,
        vector_index.storage_context.index_store,
    ]
)</code></pre>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">7.3 Custom PIR Retriever</p>
                <div class="ml-4">
                    <p>Implementation of a custom retriever specialized for incident PIR reports:</p>
                    
                    <div class="bg-gray-100 p-3 rounded-lg my-3 border border-gray-300">
                        <pre><code>from llama_index.retrievers import BaseRetriever
from llama_index.schema import NodeWithScore
from typing import List

class PIRRetriever(BaseRetriever):
    """Custom retriever for PIR documents that handles section and table structure"""
    
    def __init__(
        self, 
        vector_retriever, 
        kg_retriever, 
        struct_retriever, 
        node_dict
    ):
        self.vector_retriever = vector_retriever
        self.kg_retriever = kg_retriever
        self.struct_retriever = struct_retriever
        self.node_dict = node_dict
        
    def _get_relevant_sections(self, query):
        """Identify which sections are most relevant to the query"""
        section_keywords = {
            "summary": ["summary", "overview", "description"],
            "timeline": ["timeline", "time", "detected", "started", "fixed"],
            "impact": ["impact", "affected", "outage", "business", "service"],
            "change": ["change", "related", "implementation"],
            "root_cause": ["root", "cause", "analysis", "reason", "why"],
            "actions": ["action", "remediation", "prevent", "fix", "lesson"]
        }
        
        relevant_sections = []
        for section, keywords in section_keywords.items():
            if any(keyword in query.lower() for keyword in keywords):
                relevant_sections.append(section)
                
        return relevant_sections or ["summary"]  # Default to summary
    
    def _retrieve(self, query_str):
        """Override retrieve to include section awareness"""
        # First identify relevant sections
        relevant_sections = self._get_relevant_sections(query_str)
        
        # Check if query is asking for tabular data
        if any(kw in query_str.lower() for kw in ["table", "row", "column", "cell"]):
            # Use structured retriever for table queries
            return self.struct_retriever.retrieve(query_str)
        
        # Get nodes from vector retriever
        vector_nodes = self.vector_retriever.retrieve(query_str)
        
        # Filter for nodes from relevant sections
        filtered_nodes = []
        for node in vector_nodes:
            section = node.metadata.get("section", "")
            if any(rel_section in section.lower() for rel_section in relevant_sections):
                filtered_nodes.append(node)
        
        # If we filtered too aggressively, fall back to original results
        if len(filtered_nodes) < 2:
            return vector_nodes
            
        return filtered_nodes</code></pre>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">7.4 LangChain Integration with LlamaIndex Retrieval</p>
                <div class="ml-4">
                    <p>Integration of the custom retriever with LangChain's ReAct agent:</p>
                    
                    <div class="bg-gray-100 p-3 rounded-lg my-3 border border-gray-300">
                        <pre><code>from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.tools import BaseTool, StructuredTool, Tool
from llama_index.core.tools import QueryEngineTool, ToolMetadata

# Create LlamaIndex query engine
query_engine = pir_retriever.as_query_engine()

# Create LlamaIndex tool spec
retrieval_tool_spec = QueryEngineTool.from_defaults(
    query_engine=query_engine,
    metadata=ToolMetadata(
        name="incident_retrieval",
        description="Useful for retrieving information from incident PIR reports"
    )
)

# Convert to LangChain tool
retrieval_tool = retrieval_tool_spec.to_langchain_tool()

# Email tool
def send_email(recipient: str, subject: str, body: str) -> str:
    """Send an email with the provided details."""
    # This is a stub implementation
    print(f"Would send email to: {recipient}")
    print(f"Subject: {subject}")
    print(f"Body: {body}")
    return f"Email to {recipient} would be sent"

email_tool = StructuredTool.from_function(
    func=send_email,
    name="send_email",
    description="Send an email to a recipient"
)

# Create ReAct agent
tools = [retrieval_tool, email_tool]

prompt = PromptTemplate.from_template("""
You are an Incident Analysis Assistant that helps users find information in PIR reports.
You have access to the following tools:

{tools}

Use the following format:
Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: I need to analyze this question...
""")

llm = CustomOpenAILike(model="your-reasoning-model") # Custom reasoning LLM

agent = create_react_agent(llm, tools, prompt)

agent_executor = AgentExecutor(
    agent=agent,
    tools=tools,
    verbose=True,
    handle_parsing_errors=True
)</code></pre>
                    </div>
                </div>
                
                <p class="font-semibold mt-3">7.5 Advanced Retrieval Features</p>
                <div class="ml-4">
                    <ul class="list-disc pl-8">
                        <li><strong>Hybrid Search</strong>: Combine semantic (vector) and keyword (BM25) search for better results</li>
                        <li><strong>Contextual Compression</strong>: Extract only the most relevant parts of retrieved documents</li>
                        <li><strong>Query Routing</strong>: Direct different types of queries to specialized retrievers</li>
                        <li><strong>Metadata Filtering</strong>: Use metadata to narrow search results by date, team, incident type</li>
                        <li><strong>Reranking</strong>: Apply advanced reranking to improve retrieval precision</li>
                    </ul>
                </div>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">8. Chainlit UI Implementation</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Specification for the Chainlit UI implementation:</p>
                
                <div class="bg-gray-100 p-3 rounded-lg my-3 border border-gray-300">
                    <pre><code>import chainlit as cl
from chainlit.element import Element
from chainlit.input_widget import Select, Slider, Switch
import re

# Initialize tools and agent
tools = [retrieval_tool, email_tool]
agent_executor = setup_agent_executor(tools)

# Helper function to extract and process reasoning
def extract_thinking(text):
    thinking = ""
    pattern = r"<think>(.*?)</think>"
    match = re.search(pattern, text, re.DOTALL)
    if match:
        thinking = match.group(1).strip()
        # Remove the thinking from original text
        text = re.sub(pattern, "", text, flags=re.DOTALL).strip()
    return text, thinking

@cl.on_chat_start
async def start():
    # Set up agent session
    cl.user_session.set("agent", agent_executor)
    
    # Welcome message
    await cl.Message(
        content="Welcome to the Incident PIR Assistant! How can I help you analyze incident reports today?",
        author="Incident Assistant"
    ).send()
    
    # Add settings panel
    settings = [
        Select(
            id="retrieval_depth",
            label="Retrieval Depth",
            values=["basic", "detailed", "comprehensive"],
            initial_value="detailed"
        ),
        Slider(
            id="max_results",
            label="Maximum Results",
            initial=5,
            min=1,
            max=20,
            step=1
        ),
        Switch(
            id="show_reasoning",
            label="Always Show Reasoning",
            initial=False
        )
    ]
    await cl.ChatSettings(settings).send()

@cl.on_message
async def on_message(message: cl.Message):
    agent = cl.user_session.get("agent")
    show_reasoning = cl.user_session.get("show_reasoning", False)
    
    # Start a response message
    msg = cl.Message(author="Incident Assistant", content="")
    await msg.send()
    
    # Run the agent with the user's message
    try:
        result = await cl.make_async(agent.invoke)(
            {"input": message.content}
        )
        
        # Extract thinking if present
        response, thinking = extract_thinking(result["output"])
        
        # Update the message with the response
        await msg.update(content=response)
        
        # Add thinking in a collapsible element if present
        if thinking:
            thinking_element = cl.Text(
                name="Reasoning Process",
                content=thinking,
                display="inline",
                language="markdown"
            )
            # If show_reasoning is True, expand by default
            await msg.elements.append(thinking_element)
            
        # Handle any SVG content
        svg_pattern = r'<svg.*?</svg>'
        svg_matches = re.finditer(svg_pattern, response, re.DOTALL)
        for i, match in enumerate(svg_matches):
            svg_content = match.group(0)
            svg_element = Element(
                name=f"diagram_{i}",
                type="svg",
                content=svg_content
            )
            await msg.elements.append(svg_element)
            
    except Exception as e:
        await msg.update(content=f"Error: {str(e)}")

# Handle user approval for email
@cl.on_tool_call
async def on_tool_call(tool_call):
    if tool_call.tool_name == "send_email":
        # Extract email details
        args = tool_call.tool_kwargs
        recipient = args.get("recipient", "")
        subject = args.get("subject", "")
        body = args.get("body", "")
        
        # Ask for user approval
        res = await cl.AskActionMessage(
            content=f"Do you want to send an email to {recipient}?\n\nSubject: {subject}\n\nBody: {body}",
            actions=[
                cl.Action(name="approve", value="approve", label="✅ Approve"),
                cl.Action(name="reject", value="reject", label="❌ Reject")
            ]
        ).send()
        
        if res.get("value") == "approve":
            # Return success to continue the agent
            return "Email approved and sent successfully"
        else:
            # Return rejection
            return "Email sending was cancelled by the user"</code></pre>
                </div>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">9. Testing Strategy</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Comprehensive testing strategy for the Incident PIR RAG Application:</p>
                
                <p class="font-semibold mt-3">9.1 Unit Testing</p>
                <ul class="list-disc pl-8">
                    <li>Test individual components in isolation:
                        <ul class="list-disc pl-8">
                            <li>Document loading and parsing</li>
                            <li>Embedding generation</li>
                            <li>Retrieval functions</li>
                            <li>Agent tool implementations</li>
                        </ul>
                    </li>
                    <li>Use pytest for unit test implementation</li>
                    <li>Implement mocks for external dependencies (Confluence, LLM APIs)</li>
                    <li>Test edge cases and error handling</li>
                </ul>
                
                <p class="font-semibold mt-3">9.2 Integration Testing</p>
                <ul class="list-disc pl-8">
                    <li>Test interactions between components:
                        <ul class="list-disc pl-8">
                            <li>Document loading → Processing → Indexing pipeline</li>
                            <li>Query → Retrieval → Agent reasoning workflow</li>
                            <li>Agent → Tool usage sequence</li>
                            <li>UI → Backend integration</li>
                        </ul>
                    </li>
                    <li>Test with realistic sample PIR data</li>
                    <li>Validate LangChain and LlamaIndex integration points</li>
                </ul>
                
                <p class="font-semibold mt-3">9.3 End-to-End Testing</p>
                <ul class="list-disc pl-8">
                    <li>Complete workflow testing from document loading to UI response</li>
                    <li>Testing different query types and scenarios</li>
                    <li>Performance testing with large document sets</li>
                    <li>User acceptance testing with realistic usage patterns</li>
                </ul>
                
                <p class="font-semibold mt-3">9.4 Testing Tools and Frameworks</p>
                <ul class="list-disc pl-8">
                    <li><code>pytest</code> for unit and integration tests</li>
                    <li><code>pytest-asyncio</code> for testing async components</li>
                    <li><code>pytest-mock</code> for mocking dependencies</li>
                    <li>Custom test fixtures for LangChain and LlamaIndex components</li>
                    <li>Chainlit testing utilities</li>
                </ul>
                
                <div class="bg-yellow-50 p-3 rounded-lg my-3 border-l-4 border-yellow-400">
                    <p><strong>Sample Test Case:</strong></p>
                    <pre><code>import pytest
from unittest.mock import MagicMock, patch
from your_app.retrievers import PIRRetriever
from llama_index.schema import NodeWithScore, TextNode

@pytest.fixture
def sample_nodes():
    """Create sample nodes for testing"""
    return [
        TextNode(
            text="This is the incident summary section",
            metadata={"section": "Incident Summary"}
        ),
        TextNode(
            text="Root cause was identified as network failure",
            metadata={"section": "Root Cause"}
        ),
        TextNode(
            text="Timeline: Detected at 10:00, Fixed at 11:30",
            metadata={"section": "Incident Timeline"}
        )
    ]

@pytest.fixture
def mock_retrievers(sample_nodes):
    """Create mock retrievers"""
    vector_retriever = MagicMock()
    vector_retriever.retrieve.return_value = [
        NodeWithScore(node=node, score=0.8) for node in sample_nodes
    ]
    
    kg_retriever = MagicMock()
    struct_retriever = MagicMock()
    
    return {
        "vector": vector_retriever,
        "kg": kg_retriever,
        "struct": struct_retriever
    }

def test_pir_retriever_section_filtering(mock_retrievers, sample_nodes):
    """Test that PIR retriever correctly filters by section"""
    # Create retriever
    retriever = PIRRetriever(
        vector_retriever=mock_retrievers["vector"],
        kg_retriever=mock_retrievers["kg"],
        struct_retriever=mock_retrievers["struct"],
        node_dict={node.id_: node for node in sample_nodes}
    )
    
    # Test root cause query
    root_cause_query = "What was the root cause of the incident?"
    result = retriever.retrieve(root_cause_query)
    
    # Should return only root cause section
    assert len(result) == 1
    assert "Root Cause" in result[0].metadata.get("section")</code></pre>
                </div>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">10. Deployment Strategy</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Outline of the deployment strategy for the application:</p>
                
                <p class="font-semibold mt-3">10.1 Development Environment</p>
                <ul class="list-disc pl-8">
                    <li>Local development using Docker containers</li>
                    <li>Mock implementations of external services</li>
                    <li>Version control with Git</li>
                    <li>CI/CD pipeline for automated testing</li>
                </ul>
                
                <p class="font-semibold mt-3">10.2 Staging Environment</p>
                <ul class="list-disc pl-8">
                    <li>Deployed on cloud infrastructure (AWS/Azure/GCP)</li>
                    <li>Integration with test Confluence instance</li>
                    <li>Vectorstore using managed services</li>
                    <li>Performance testing and monitoring</li>
                </ul>
                
                <p class="font-semibold mt-3">10.3 Production Environment</p>
                <ul class="list-disc pl-8">
                    <li>Deployed on enterprise infrastructure</li>
                    <li>Integration with production Confluence</li>
                    <li>Scaled vectorstore for production workloads</li>
                    <li>Monitoring and alerting</li>
                    <li>Backup and recovery procedures</li>
                </ul>
                
                <p class="font-semibold mt-3">10.4 Infrastructure Requirements</p>
                <ul class="list-disc pl-8">
                    <li>Compute resources for application servers</li>
                    <li>Vector database (e.g., Pinecone, Weaviate, or self-hosted)</li>
                    <li>Storage for document cache</li>
                    <li>API gateway for service access</li>
                    <li>Authentication and authorization services</li>
                </ul>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">11. Future Enhancements</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p>Potential future enhancements for the application:</p>
                
                <ol class="list-decimal pl-8">
                    <li><strong>Advanced Analytics</strong>: 
                        <ul class="list-disc pl-8">
                            <li>Trend analysis across incidents</li>
                            <li>Automatic identification of related incidents</li>
                            <li>Root cause categorization and clustering</li>
                        </ul>
                    </li>
                    <li><strong>Integration Enhancements</strong>:
                        <ul class="list-disc pl-8">
                            <li>Integration with incident management systems</li>
                            <li>Automatic email notifications for stakeholders</li>
                            <li>Calendar integration for incident review meetings</li>
                        </ul>
                    </li>
                    <li><strong>Enhanced UI</strong>:
                        <ul class="list-disc pl-8">
                            <li>Dashboard for incident metrics</li>
                            <li>Visualization of incident relationships</li>
                            <li>Timeline views of related incidents</li>
                        </ul>
                    </li>
                    <li><strong>Advanced Retrieval</strong>:
                        <ul class="list-disc pl-8">
                            <li>Multi-modal search including screenshots and diagrams</li>
                            <li>Cross-lingual search capabilities</li>
                            <li>Automatic knowledge distillation from multiple incidents</li>
                        </ul>
                    </li>
                    <li><strong>Automated Insights</strong>:
                        <ul class="list-disc pl-8">
                            <li>Proactive surfacing of relevant past incidents</li>
                            <li>Automatic identification of recurring issues</li>
                            <li>Predictive analytics for incident prevention</li>
                        </ul>
                    </li>
                </ol>
            </div>

            <h4 class="text-lg font-semibold mt-6 mb-2">12. Appendices</h4>
            <div class="bg-gray-50 p-4 rounded mb-4">
                <p class="font-semibold mt-3">12.1 Glossary</p>
                <div class="ml-4">
                    <dl>
                        <dt><strong>PIR</strong></dt>
                        <dd>Post-Implementation Review - A document created after an incident to analyze what happened, why it happened, and how to prevent it in the future.</dd>
                        
                        <dt><strong>RAG</strong></dt>
                        <dd>Retrieval-Augmented Generation - A technique that combines retrieval of relevant documents with generative AI to produce accurate, grounded responses.</dd>
                        
                        <dt><strong>ReAct</strong></dt>
                        <dd>Reasoning and Acting - An agent framework that alternates between reasoning about a situation and taking actions based on that reasoning.</dd>
                        
                        <dt><strong>LangChain</strong></dt>
                        <dd>A framework for developing applications powered by language models, focusing on composition and orchestration of LLM-powered applications.</dd>
                        
                        <dt><strong>LlamaIndex</strong></dt>
                        <dd>A data framework for LLM applications to ingest, structure, and access private or domain-specific data.</dd>
                        
                        <dt><strong>Chainlit</strong></dt>
                        <dd>A framework for creating conversational AI user interfaces with rich features like element display and user interaction.</dd>
                    </dl>
                </div>
                
                <p class="font-semibold mt-3">12.2 References</p>
                <div class="ml-4">
                    <ol>
                        <li>LangChain Documentation: <a href="https://langchain.readthedocs.io/">https://langchain.readthedocs.io/</a></li>
                        <li>LlamaIndex Documentation: <a href="https://gpt-index.readthedocs.io/">https://gpt-index.readthedocs.io/</a></li>
                        <li>Chainlit Documentation: <a href="https://docs.chainlit.io/">https://docs.chainlit.io/</a></li>
                        <li>ReAct Pattern: <a href="https://arxiv.org/abs/2210.03629">https://arxiv.org/abs/2210.03629</a></li>
                        <li>RAG Architecture: <a href="https://arxiv.org/abs/2005.11401">https://arxiv.org/abs/2005.11401</a></li>
                    </ol>
                </div>
                
                <p class="font-semibold mt-3">12.3 Sample Code Repository Structure</p>
                <div class="ml-4">
                    <pre><code>incident-pir-rag/
├── README.md
├── requirements.txt
├── Dockerfile
├── docker-compose.yml
├── .env.example
├── src/
│   ├── __init__.py
│   ├── main.py
│   ├── config.py
│   ├── confluence/
│   │   ├── __init__.py
│   │   ├── loader.py
│   │   └── parser.py
│   ├── indexing/
│   │   ├── __init__.py
│   │   ├── embedding.py
│   │   ├── processors.py
│   │   └── storage.py
│   ├── retrieval/
│   │   ├── __init__.py
│   │   ├── pir_retriever.py
│   │   ├── query_engine.py
│   │   └── reranking.py
│   ├── agent/
│   │   ├── __init__.py
│   │   ├── react_agent.py
│   │   ├── tools.py
│   │   └── prompts.py
│   ├── tools/
│   │   ├── __init__.py
│   │   └── email_tool.py
│   └── ui/
│       ├── __init__.py
│       └── chainlit_app.py
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_confluence.py
│   ├── test_indexing.py
│   ├── test_retrieval.py
│   ├── test_agent.py
│   ├── test_tools.py
│   └── test_e2e.py
└── docs/
    ├── architecture.md
    ├── api_reference.md
    ├── deployment.md
    └── user_guide.md</code></pre>
                </div>
            </div>
            
            <div class="bg-indigo-50 p-4 rounded-lg mt-8 border-l-4 border-indigo-500">
                <h3 class="text-xl font-semibold text-indigo-700 mb-2">
                    <i class="fas fa-lightbulb mr-2"></i>PRD Creation Tips
                </h3>
                <ol class="list-decimal pl-8">
                    <li>Begin with a clear executive summary that sets the context and objectives</li>
                    <li>Focus on specific integration points between LangChain and LlamaIndex</li>
                    <li>Include realistic code examples that demonstrate key technical approaches</li>
                    <li>Define clear interfaces between components for maintainable architecture</li>
                    <li>Specify testing strategies for each component and integration point</li>
                    <li>Use diagrams to illustrate architecture, data flow, and component relationships</li>
                    <li>Include examples of expected user interactions and system responses</li>
                    <li>Define performance metrics and evaluation criteria</li>
                    <li>Include a detailed glossary to ensure common understanding of technical terms</li>
                    <li>Provide references to relevant documentation and research papers</li>
                </ol>
            </div>
        </div>
    </div>
    
    <div class="text-center py-6 text-gray-600 text-sm">
        <p>Incident PIR RAG Application - Project Requirements Document Template</p>
    </div>
</body>
</html>