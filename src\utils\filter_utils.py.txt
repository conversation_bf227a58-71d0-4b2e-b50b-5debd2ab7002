import re
from typing import Dict, Optional

def detect_pir_filters(query_text: str) -> Dict[str, str]:
    """
    Detects PIR-related filters from the query text using regex.

    Args:
        query_text (str): The user's query text.

    Returns:
        Dict[str, str]: Dictionary of detected filters.
    """
    detected = {}
    
    # Detect incident ID
    incident_match = re.search(r"incident\s+(INC\d{8,})", query_text, re.IGNORECASE)
    if incident_match:
        detected['incident_id'] = incident_match.group(1).upper()
    
    # Detect severity
    severity_match = re.search(r"(high|medium|low)\s+severity", query_text, re.IGNORECASE)
    if severity_match:
        detected['severity'] = severity_match.group(1).capitalize()
    
    # Add regex for other potential filter keys like 'service', 'region', 'date' (more complex) later

    # Detect team
    team_match = re.search(r"team\s+(.+)", query_text, re.IGNORECASE)
    if team_match:
        detected['team'] = team_match.group(1).strip()

    # Detect subject
    subject_match = re.search(r"subject\s+(.+)", query_text, re.IGNORECASE)
    if subject_match:
        detected['subject'] = subject_match.group(1).strip()

    # Detect impacted applications
    impacted_applications_match = re.search(r"impacted\s+applications\s+(.+)", query_text, re.IGNORECASE)
    if impacted_applications_match:
        detected['impacted_application(s)'] = impacted_applications_match.group(1).strip()

    # Detect impacted regions
    impacted_regions_match = re.search(r"impacted\s+regions\s+(.+)", query_text, re.IGNORECASE)
    if impacted_regions_match:
        detected['impacted_regions'] = impacted_regions_match.group(1).strip()

    # Detect problem ticket
    problem_ticket_match = re.search(r"problem\s+ticket\s+(.+)", query_text, re.IGNORECASE)
    if problem_ticket_match:
        detected['problem_ticket'] = problem_ticket_match.group(1).strip().upper()

    # Detect problem owner
    problem_owner_match = re.search(r"problem\s+owner\s+(.+)", query_text, re.IGNORECASE)
    if problem_owner_match:
        detected['problem_owner'] = problem_owner_match.group(1).strip()

     # Detect post incident review status
    post_incident_review_status_match = re.search(r"post\s+incident\s+review\s+status\s+(.+)", query_text, re.IGNORECASE)
    if post_incident_review_status_match:
        detected['post_incident_review_status'] = post_incident_review_status_match.group(1).strip()

    # Detect section title
    section_title_match = re.search(r"section\s+title\s+(.+)", query_text, re.IGNORECASE)
    if section_title_match:
        detected['section_title'] = section_title_match.group(1).strip()
    
    return detected