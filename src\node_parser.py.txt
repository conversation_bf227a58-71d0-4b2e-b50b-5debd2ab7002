import re
from typing import List, Dict, Any, Optional, Tuple
from llama_index.core.node_parser.interface import <PERSON><PERSON><PERSON><PERSON><PERSON>
from llama_index.core.schema import Document, TextNode
from llama_index.core.node_parser import TokenTextSplitter
import logging
import json

logger = logging.getLogger(__name__)

def extract_metadata_from_first_table(markdown_text: str) -> Dict[str, str]:
    """Extract metadata from the first table in the document, handling flexible formatting."""
    # Isolate the first section (before any ### headers)
    first_section_end = markdown_text.find('###')
    first_section = markdown_text[:first_section_end] if first_section_end > 0 else markdown_text
    
    # Find the table separator
    separator_match = re.search(r'^[\s|]*[-:|]+[\s|]*$', first_section, re.MULTILINE)
    if not separator_match:
        return {}
        
    lines = first_section.split('\n')
    table_start = 0
    
    # Locate the separator line
    for i, line in enumerate(lines):
        if re.match(r'^[\s|]*[-:|]+[\s|]*$', line):
            table_start = i
            break

    if table_start <= 0:
        return {}
    
    # Extract key-value pairs from data rows
    metadata = {}
    for i in range(table_start + 1, len(lines)):
        line = lines[i].strip()
        if not line:
            continue
            
        # Split by pipe, handle rows without leading/trailing pipes
        cells = [cell.strip().replace('*', '') for cell in line.split('|') if cell.strip()]
        if len(cells) >= 2:
            key, value = cells[0], cells[1]
            metadata[key] = value
        elif len(cells) == 1 and metadata:
            # Append to the last key if it’s a continuation
            last_key = list(metadata.keys())[-1]
            metadata[last_key] += " " + cells[0]
    
    return metadata

def extract_table_as_json(section_text: str) -> List[Dict[str, str]]:
    """Extract table data as JSON, handling multi-line cells and inconsistent formatting."""
    lines = section_text.split('\n')
    table_data = []
    headers = []
    in_table = False
    current_row = {}

    for line in lines:
        stripped = line.strip()
        
        if not in_table:
            # Identify the table separator to start parsing
            if re.match(r'^[\s|]*[-:|]+[\s|]*$', stripped):
                in_table = True
                # Headers are in the line before the separator
                header_idx = lines.index(line) - 1
                if header_idx >= 0:
                    header_line = lines[header_idx].strip()
                    headers = [h.strip().replace('*', '') for h in header_line.split('|') if h.strip()]
                continue
        else:
            if stripped == "" or '|' not in stripped:
                # Continuation of the previous cell
                if current_row and headers:
                    last_header = list(current_row.keys())[-1]
                    current_row[last_header] += " " + stripped
                continue
            else:
                # New row
                cells = [cell.strip() for cell in stripped.split('|') if cell.strip()]
                if len(cells) == len(headers):
                    if current_row:
                        table_data.append(current_row)
                    current_row = {headers[j]: cells[j] for j in range(len(headers))}
                elif len(cells) < len(headers) and current_row:
                    # Partial row, append to current row
                    for j, cell in enumerate(cells):
                        current_row[headers[j]] += " " + cell
    
    # Append the last row
    if current_row:
        table_data.append(current_row)
    
    return table_data

def format_table_for_reading(table_data: List[Dict[str, str]]) -> str:
    """Format table data as readable text for TextNodes."""
    if not table_data:
        return ""
            
    formatted_rows = []
    for row in table_data:
        row_text = []
        for key, value in row.items():
            row_text.append(f"{key}: {value}")
        formatted_rows.append("\n".join(row_text))
            
    return "\n\n".join(formatted_rows)

class PIRNodeParser(NodeParser):
    def get_nodes_from_documents(
        self, documents: List[Document], show_progress: bool = False, **kwargs
    ) -> List[TextNode]:
        """Process documents into nodes with enhanced table parsing and JSON representation."""
        all_nodes: List[TextNode] = []
        
        for i, doc in enumerate(documents):
            markdown_text = doc.text
            source_info = doc.metadata.get('file_name', doc.doc_id or f"doc_{i+1}")
            logger.info(f"Processing document: {source_info}")
            
            # Extract raw metadata
            raw_metadata = extract_metadata_from_first_table(markdown_text)
            
            # Map raw metadata to standardized fields
            base_metadata = self._standardize_metadata(raw_metadata, source_info)
            base_metadata["doc_id"] = doc.doc_id or f"doc_{i+1}"
            
            # Split into sections and extract date
            sections, extracted_date = self._split_markdown_by_pir_headers(markdown_text)
            if extracted_date:
                base_metadata['date'] = extracted_date
            
            # Store section tables for JSON representation
            document_json = {"metadata": base_metadata, "sections": {}}
            
            # Process each section
            for section_title, section_markdown in sections.items():
                if not section_markdown.strip():
                    continue
                
                # Extract table as JSON
                table_data = extract_table_as_json(section_markdown)
                
                # Store table data in document JSON if found
                if table_data:
                    document_json["sections"][section_title] = table_data
                    
                    # Create human-readable text version of the table
                    readable_text = format_table_for_reading(table_data)
                    
                    # Replace the original table with the readable text
                    formatted_section = section_markdown
                    separator_match = re.search(r'^[\s|]*[-:|]+[\s|]*$', section_markdown, re.MULTILINE)
                    if separator_match:
                        separator_pos = section_markdown.find(separator_match.group(0))
                        if separator_pos > 0:
                            # Find start of table (header line)
                            table_start = section_markdown.rfind('\n', 0, separator_pos)
                            if table_start < 0:
                                table_start = 0
                                
                            # Find end of table
                            table_end_match = re.search(r'\n\s*\n', section_markdown[separator_pos:])
                            table_end = (separator_pos + table_end_match.end()) if table_end_match else len(section_markdown)
                            
                            # Replace table with readable text
                            formatted_section = section_markdown[:table_start] + '\n' + readable_text + '\n' + section_markdown[table_end:]
                else:
                    # No table in this section
                    formatted_section = section_markdown
                
                # Create metadata for this node
                node_metadata = base_metadata.copy()
                node_metadata["section_title"] = section_title
                
                # Include JSON table data if available
                # if table_data:
                #    node_metadata["table_data"] = json.dumps(table_data)
                
                # Create TextNode
                node_id = f"{base_metadata.get('incident_id', base_metadata['doc_id'])}_section_{section_title.lower().replace(' ', '_')}"
                node = TextNode(
                    text=formatted_section.strip(),
                    metadata=node_metadata,
                    id_=node_id,
                    excluded_embed_metadata_keys=["source_info", "doc_id", "table_data"],
                    excluded_llm_metadata_keys=["source_info", "doc_id", "incident_id"]
                )
                all_nodes.append(node)
            
            # Create a dedicated JSON node for the entire document
            # json_text = json.dumps(document_json, indent=2)
            # json_node_id = f"{base_metadata.get('incident_id', base_metadata['doc_id'])}_json"
            
            # json_node = TextNode(
            #     text=json_text,
            #     metadata={
            #         **base_metadata,
            #         "content_type": "json",
            #         "description": "Complete JSON representation of document tables and metadata"
            #     },
            #     id_=json_node_id,
            #     excluded_embed_metadata_keys=["source_info", "doc_id"],
            #     excluded_llm_metadata_keys=["source_info", "doc_id", "incident_id"]
            # )
            # all_nodes.append(json_node)
        
        return all_nodes
        
    def _standardize_metadata(self, raw_metadata: Dict[str, str], source_info: str) -> Dict[str, Any]:
        """Standardize raw metadata into consistent fields."""
        # Define mappings from raw field names to standard field names
        field_mappings = {
            "incident number": "incident_id",
            "incident ticket": "incident_id",
            "impact": "severity",
            "problem ticket": "problem_ticket",
            "impacted application(s)": "impacted_applications",
            "impacted regions": "impacted_regions",
            "problem owner": "problem_owner",
            "post incident review status": "pir_status"
        }
        
        # Initialize standard metadata with default empty values
        standard_metadata = {
            "incident_id": "",
            "severity": "",
            "problem_ticket": "",
            "impacted_applications": "",
            "impacted_regions": "",
            "problem_owner": "",
            "pir_status": "",
            "source_info": source_info
        }
        
        # Map raw metadata to standard fields
        for raw_key, raw_value in raw_metadata.items():
            # Normalize key name
            normalized_key = raw_key.lower().strip()
            
            # Find the standard field name if available (partial match)
            standard_key = None
            for field_key in field_mappings:
                if field_key in normalized_key:
                    standard_key = field_mappings[field_key]
                    break
            if standard_key:
                standard_key = field_mappings[normalized_key]
                
                # Handle special cases
                # if standard_key in ["impacted_applications", "impacted_regions"]:
                #     # Split comma-separated values into list
                #     standard_metadata[standard_key] = [
                #         item.strip() for item in raw_value.split(",") if item.strip()
                #     ]
                # elif standard_key == "incident_id":
                #     standard_metadata[standard_key] = raw_value.upper()
                # elif standard_key == "severity":
                #     standard_metadata[standard_key] = raw_value.capitalize()
                # else:
                standard_metadata[standard_key] = raw_value
                    
        return standard_metadata

    def _split_markdown_by_pir_headers(
        self, markdown_text: str
    ) -> Tuple[Dict[str, str], Optional[str]]:
        """Splits markdown by H3 headers and extracts date from Timeline."""
        sections: Dict[str, str] = {}
        extracted_date: Optional[str] = None
        headers = list(re.finditer(r"^(###\s+.*)$", markdown_text, re.MULTILINE))
        for i, header_match in enumerate(headers):
            header_line = header_match.group(1).strip()
            start = header_match.end()
            end = headers[i + 1].start() if i + 1 < len(headers) else len(markdown_text)
            content = markdown_text[start:end].strip()
            section_title = header_line.replace("### ", "").strip()
            sections[section_title] = content
            if "timeline" in section_title.lower():
                date_match = re.search(r"Date:\s*(\d{8})", content, re.IGNORECASE)
                if date_match:
                    date_str = date_match.group(1)
                    extracted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
        return sections, extracted_date

    def _parse_nodes(
        self, nodes: List[TextNode], show_progress: bool = False, **kwargs
    ) -> List[TextNode]:
        """Pass-through method for interface compatibility."""
        return nodes