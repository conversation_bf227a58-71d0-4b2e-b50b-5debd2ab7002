from llama_index.core import VectorStoreIndex
from llama_index.core.retrievers import VectorIndexAutoRetriever
from llama_index.core.vector_stores.types import VectorStoreInfo, MetadataInfo
from llama_index.core.schema import QueryBundle, NodeWithScore
from llama_index.core.llms.llm import LLM
from llama_index.llms.openai import OpenAI
from llama_index.core.tools import QueryEngineTool, ToolMetadata, FunctionTool
# from llama_index.core.vector_stores import VectorStoreIndex
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.core.indices.query.schema import QueryType
from typing import List, Optional, Any, Dict, Tuple
from src.utils.filter_utils import detect_pir_filters
import logging
import re

logger = logging.getLogger(__name__)


def get_auto_retriever(index: VectorStoreIndex, similarity_top_k: int = 5) -> VectorIndexAutoRetriever:
    """
    Creates a LlamaIndex VectorIndexAutoRetriever which uses an LLM to generate metadata filters dynamically.

    Args:
        index (VectorStoreIndex): The index to retrieve from.
        similarity_top_k (int): The number of nodes to retrieve from the index.

    Returns:
        VectorIndexAutoRetriever: The auto-retriever that dynamically generates metadata filters.
    """
    # Define metadata schema using AttributeInfo objects
    # This allows the LLM to understand the structure of our metadata and generate
    # appropriate filters when processing user queries
    metadata_info = [
        MetadataInfo(
            name="incident_id",
            type="str",
            description="Unique ID of the incident, e.g., INC123456"
        ),
        MetadataInfo(
            name="severity",
            type="str",
            description="Severity level (High, Medium, Low)"
        ),
        MetadataInfo(
            name="section_title", 
            type="str",
            description="The section of the PIR document (Summary, Timeline, Root Cause Analysis, etc.)"
        ),
        MetadataInfo(
            name="labels",
            type="list[str]",
            description="Keywords or tags associated with the incident"
        )
    ]

    # Create VectorStoreInfo with content and metadata information
    # This object bundles information about what the vector store contains and how to filter it
    vector_store_info = VectorStoreInfo(
        content_info="PIR section content",
        metadata_info=metadata_info
    )

    # Get the LLM from Settings to ensure we're using a properly configured LLM
    from llama_index.core import Settings
    llm = Settings.llm
    
    if llm is None:
        logger.warning("No LLM found in Settings, auto-retriever may not work properly")

    # Create the auto-retriever with the vector store info
    retriever = VectorIndexAutoRetriever(
        index,
        vector_store_info=vector_store_info,
        llm=llm,  # Explicitly set the LLM
        similarity_top_k=similarity_top_k,
        verbose=True  # helps debug LLM filter generation
    )

    return retriever


def get_advanced_query_engine(index: VectorStoreIndex, filter_dict: Optional[Dict[str, str]] = None, 
                             similarity_top_k: int = 10, rerank_top_n: int = 3):
    """
    Creates an advanced query engine with metadata filters and reranking.

    Args:
        index (VectorStoreIndex): The index to query against.
        filter_dict (Dict[str, str], optional): Dictionary of metadata filters.
        similarity_top_k (int): Number of nodes to retrieve initially.
        rerank_top_n (int): Number of nodes to keep after reranking.

    Returns:
        BaseQueryEngine: The configured query engine.
    """
    # Set up reranker
    reranker = SentenceTransformerRerank(
        top_n=rerank_top_n,
        model="cross-encoder/ms-marco-MiniLM-L-6-v2"
    )
    
    # Convert filter_dict to MetadataFilters format if provided
    filters = []
    if filter_dict:
        for key, value in filter_dict.items():
            filters.append({"key": key, "value": value})
    
    # Create the query engine with filters and reranking
    query_engine = index.as_query_engine(
        similarity_top_k=similarity_top_k,
        node_postprocessors=[reranker],
        filters=filters
    )
    
    return query_engine


def create_pir_retrieval_tool(index: VectorStoreIndex, similarity_top_k: int = 10, rerank_top_n: int = 3) -> FunctionTool:
    """
    Creates a LlamaIndex Tool wrapping the advanced query engine that returns only the context string.
    
    This version is simplified to return ONLY the concatenated context string from retrieved documents,
    making it easier for the agent to process. Source details will be handled in the app.py file
    via intermediate steps.

    Args:
        index (VectorStoreIndex): The index to retrieve from.
        similarity_top_k (int): The number of nodes to retrieve from the index.
        rerank_top_n (int): The number of nodes to rerank.

    Returns:
        FunctionTool: The retrieval tool that returns only context string.
    """

    def run_retrieval_and_rerank_for_agent(query_text: str) -> str:
        """
        Retrieves and re-ranks relevant sections from Post-Incident Review (PIR) documents 
        and returns only the concatenated context string.
        """
        logger.info(f"PIR Retrieval Tool running for: '{query_text}'")
        
        # Detect filters from query text
        filter_dict = detect_pir_filters(query_text)
        if filter_dict:
            logger.info(f"Detected filters for tool query: {filter_dict}")
            
        try:
            # Get advanced query engine with filters
            query_engine = get_advanced_query_engine(
                index, 
                filter_dict=filter_dict,
                similarity_top_k=similarity_top_k, 
                rerank_top_n=rerank_top_n
            )
            
            # Execute query
            response = query_engine.query(query_text)
            
            # Check if we got any results
            if not response.source_nodes:
                logger.warning("Tool: No source nodes found.")
                return "No relevant information found in PIR documents."
            
            # Format ONLY the context string
            context_str = "\n\n---\n\n".join([node.get_content() for node in response.source_nodes])
            logger.info(f"Tool returning context string derived from {len(response.source_nodes)} sources.")
            
            # Return only the context string
            result = context_str
            
        except Exception as e:
            logger.error(f"Query engine failed for tool: {e}", exc_info=True)
            result = f"Error: Failed to retrieve documents ({e})"
            
        return result

    tool_metadata = ToolMetadata(
        name="PIR_Retriever_Tool",
        description="Retrieves and re-ranks relevant sections from Post-Incident Review (PIR) documents based on a user query. It automatically filters based on Incident IDs (INCxxxxxx) or severity (high/medium/low) mentioned in the query."
    )
    
    return FunctionTool.from_defaults(
        fn=run_retrieval_and_rerank_for_agent, 
        name=tool_metadata.name, 
        description=tool_metadata.description
    )
