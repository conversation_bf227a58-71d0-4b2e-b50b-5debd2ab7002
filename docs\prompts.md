# AI IDE Prompts for Incident PIR RAG Application

This document contains prompts designed for AI IDEs (like Roo Cline, Cursor) to guide the step-by-step implementation of the Incident PIR RAG application using Chainlit, LangChain, LlamaIndex, and custom OpenAI-compatible models.

---

## 1. Confluence Data Loading and Indexing

```text
# ROO CLINE/CURSOR PROMPT: Implement Confluence PIR Data Loader and Indexer

**Module**: `confluence_loader.py`, `pir_processor.py`, `embedding_handler.py`, `indexer.py`

**Goal**: Create Python modules to load, process, and index Confluence PIR pages, optimized for structured retrieval and performance with potentially large datasets.

**Detailed Steps**:

1.  **`confluence_loader.py` (Confluence API Connector)**:
    *   **Function**: `load_pir_pages(space_key: str, home_page_id: str, recursive: bool = True) -> List[Dict[str, Any]]`
    *   **Action**: Connect to Confluence API using `requests` with robust retry logic for rate limiting (429 errors) and connection issues. Use API token authentication. Implement pagination to handle large numbers of pages efficiently. Recursively fetch child pages under `home_page_id`. Filter pages to only load PIR pages based on `is_pir_page` function (implement this function to check for "PIR" or "Incident" in title/labels).
    *   **Input**: Confluence URL, Username, API Token (from environment variables: `CONFLUENCE_URL`, `CONFLUENCE_USERNAME`, `CONFLUENCE_API_TOKEN`), `space_key`, `home_page_id`.
    *   **Output**: List of Confluence page dictionaries (JSON response from API). Each dict should include `id`, `title`, `body.storage.value` (HTML content), `metadata.labels`, etc.
    *   **Error Handling**: Log errors, handle connection exceptions gracefully.
    *   **Testing**: Write a basic test to connect to Confluence (mock API if necessary for testing without live Confluence) and fetch a small number of pages. Log the number of pages fetched and any errors.

2.  **`pir_processor.py` (PIR HTML Structure Processor)**:
    *   **Class**: `PIRProcessor` with method `process_page(page: Dict[str, Any]) -> PIRDocument`
    *   **Action**: Parse the HTML content (`body.storage.value`) of each Confluence page using `BeautifulSoup`. Extract structured data based on the PIR HTML format (see `Incident-PIR.html` example).
        *   Extract metadata from `<h1>` (Date, Incident ID, Team Name, Subject).
        *   Identify sections using `<h3>` tags (e.g., "Incident Summary", "Incident Timeline", "Root Cause").
        *   Extract table data from `<table>` elements, parsing key-value pairs and data tables. Store table data as structured dictionaries within `PIRSection`.
    *   **Output**: `PIRDocument` dataclass object for each page, containing structured sections (`PIRSection` dataclass - title, content, section_type ["header", "table", "text"], metadata) and document-level metadata.
    *   **Structure-Aware Retrieval**: Ensure the processing explicitly identifies and structures content based on `<h3>` sections and `<table>` elements for targeted retrieval later.
    *   **Testing**: Create a test case using a sample `Incident-PIR.html` string. Assert that `process_page` correctly parses sections, tables, and metadata. Log extracted sections and metadata for verification.

3.  **`embedding_handler.py` (Custom Embedding Model)**:
    *   **Class**: `EmbeddingHandler` with method `create_embeddings(texts: List[str], metadata_list: List[Dict[str, Any]]) -> List[EmbeddingChunk]`
    *   **Action**:  Use `requests` to interact with the custom OpenAI-compatible embedding model API. Send batches of text chunks to the API endpoint (`CUSTOM_EMBED_API_ENDPOINT`) for embedding. Implement retry logic for API errors.
    *   **Input**: Text chunks (list of strings), Metadata list (list of dictionaries corresponding to texts), `CUSTOM_EMBED_API_ENDPOINT`, `CUSTOM_EMBED_API_KEY` (from environment variables).
    *   **Output**: List of `EmbeddingChunk` dataclass objects (id, document_id, text, embedding: List[float], metadata).
    *   **Custom Embedding Model Integration**: Ensure code uses `CUSTOM_EMBED_API_ENDPOINT` and API key for embedding requests, formatting requests as required by your custom API (OpenAI-compatible structure expected).
    *   **Testing**: Mock the embedding API call to return dummy embeddings. Test that `create_embeddings` function correctly batches texts and handles API responses. Log the number of embeddings created and any API errors.

4.  **`indexer.py` (Indexer Orchestration)**:
    *   **Class**: `PIRIndexer` with method `index_pir_pages(space_key: str, home_page_id: str, recursive: bool = True, force_reindex: bool = False)`
    *   **Action**: Orchestrate the indexing pipeline.
        *   Initialize `ConfluenceLoader`, `PIRProcessor`, `EmbeddingHandler`.
        *   Load PIR pages using `ConfluenceLoader`.
        *   Process each page with `PIRProcessor`.
        *   Prepare chunks for embedding from `PIRDocument` sections (document-level summary chunk, section-level chunks, table-row level chunks).
        *   Create embeddings for chunks using `EmbeddingHandler`.
        *   Store embeddings and metadata in a vector database (e.g., ChromaDB - use `VECTOR_DB_PATH` from environment variable for persistence). Implement incremental indexing by checking document hashes to avoid re-indexing unchanged pages (unless `force_reindex=True`).
    *   **Input**: `space_key`, `home_page_id`, `recursive`, `force_reindex`, Confluence credentials, Embedding API details, Vector DB path (all loaded via environment variables or command-line arguments using `argparse`).
    *   **Output**: Indexed PIR documents in the vector database. Log the number of pages indexed, chunks created, and any errors. Update `index_metadata.json` in `cache_dir` (from env variable or default "./cache") to track indexing progress.
    *   **Performance**: Implement batch processing for API calls and vector database writes to optimize indexing speed for large datasets. Consider using asynchronous operations if feasible for API calls.
    *   **Error Handling**: Robust error handling for all stages (Confluence loading, HTML processing, embedding, vector DB storage). Log detailed error messages.
    *   **Testing**: Create a test that simulates the entire indexing pipeline using a small set of dummy Confluence pages (or local HTML files). Verify that documents are loaded, processed, embedded, and stored in the vector database. Check `index_metadata.json` is updated.

**Environment Variables**: Configure `.env` file with: `CONFLUENCE_URL`, `CONFLUENCE_USERNAME`, `CONFLUENCE_API_TOKEN`, `CUSTOM_EMBED_API_ENDPOINT`, `CUSTOM_EMBED_API_KEY`, `VECTOR_DB_PATH`, `CACHE_DIR`.

**Testing Recommendations**: For each module, create unit tests using `pytest`. Mock external API calls (Confluence API, Embedding API, Vector DB interactions) for isolated testing. For end-to-end testing of `indexer.py`, use a small sample dataset and verify the indexing process and data in the vector store. Log extensively during development and testing to track progress and debug issues.
```

## 2. Query Vectorization

```text
# ROO CLINE/CURSOR PROMPT: Implement Query Vectorization Module

**Module**: `query_vectorizer.py`

**Goal**: Create a Python module to convert user queries into vectors, optimized for PIR document retrieval.

**Detailed Steps**:

1.  **`query_vectorizer.py` (Query Vectorizer Class)**:
    *   **Class**: `QueryVectorizer`
    *   **Initialization**:  Accept `embedding_endpoint`, `api_key`, `model_name` (for custom embedding model), and `cache_size` as arguments in `__init__`. Initialize `EmbeddingHandler` using `embedding_endpoint` and `api_key`. Implement a simple LRU cache (`self.query_cache`) for query vectors to improve performance for repeated queries.
    *   **Function**: `vectorize_query(query: str, query_type: Optional[str] = None) -> Dict[str, Any]`
        *   **Action**:
            *   **Cache Check**:  First, check if the vector for the given `query` (and `query_type` if provided) exists in `self.query_cache`. If found, return cached result immediately.
            *   **Preprocessing**: Call `_preprocess_query(query, query_type)` to preprocess the user query. Implement preprocessing steps like lowercasing, whitespace normalization, acronym expansion (e.g., PIR, RCA, SLA), and potentially type-specific context addition (e.g., append "timeline context" for timeline queries).
            *   **Query Expansion**: Call `_expand_query(processed_query)` to generate expanded query variations. Implement basic expansion strategies, e.g., adding versions focused on incident IDs if present, adding question-type specific phrases (e.g., "incident summary" for "what happened" queries).
            *   **Embedding**: Use `self.embedding_handler.create_embeddings` to generate embeddings for all expanded queries.
            *   **Result Structuring**: Create a dictionary containing: original `query`, `processed_query`, `expanded_queries`, the embedding of the *first* (original) expanded query (`primary_embedding`), `alternative_embeddings` (embeddings of other expanded queries), and `timestamp`.
            *   **Cache Update**: Store the generated result in `self.query_cache` using a hash of the query as the key. Implement LRU cache eviction if cache exceeds `cache_size`.
        *   **Input**: User query string (`query`), optional `query_type` (string).
        *   **Output**: Dictionary containing query information and embeddings (see "Result Structuring" above).  Include error key if embedding fails.
        *   **Error Handling**: Implement error handling within `create_embeddings` call. If embedding fails, log the error and return a result dictionary with an "error" key, but still include `query` and `processed_query` for debugging.
    *   **Function**: `cached_vectorize_query(query: str, query_type: Optional[str] = None) -> np.ndarray`
        *   **Action**: This is a cached wrapper around `vectorize_query` specifically to return just the embedding vector (NumPy array). Use `@lru_cache` decorator for efficient caching. Call `vectorize_query` internally, extract the primary embedding, convert to NumPy array, and return it. For cache misses, `vectorize_query` will be called. If embedding retrieval fails (error key in `vectorize_query` result), log an error and return a zero-vector of the embedding dimension as a fallback.
        *   **Input**: User query string (`query`), optional `query_type` (string).
        *   **Output**: NumPy array representing the query embedding.

2.  **Helper Functions (within `QueryVectorizer` class)**:
    *   `_preprocess_query(query: str, query_type: Optional[str] = None) -> str`: Implements query preprocessing logic (lowercase, whitespace, acronym expansion, type context).
    *   `_expand_query(query: str) -> List[str]`: Implements query expansion logic (ID-focused queries, question-type variations).
    *   `_compute_query_hash(query: str) -> str`:  Computes MD5 hash of the query string for caching keys.

**Environment Variables**:  Requires `CUSTOM_EMBED_API_ENDPOINT`, `CUSTOM_EMBED_API_KEY` (already defined in `.env` for indexing).

**Testing Recommendations**:
*   Unit tests using `pytest` for `QueryVectorizer` class.
*   Mock `EmbeddingHandler.create_embeddings` to return dummy embeddings for isolated testing.
*   Test `vectorize_query` function for different query types and ensure preprocessing, expansion, embedding, and caching are working as expected.
*   Test `cached_vectorize_query` to verify LRU caching and fallback behavior on embedding errors.
*   Log query, processed query, expanded queries, and cache hits/misses during testing to verify behavior.
```

## 3. Semantic Similarity Search

```text
# ROO CLINE/CURSOR PROMPT: Implement Semantic Similarity Search Module

**Module**: `semantic_search.py`

**Goal**: Create a Python module for advanced semantic search of PIR documents, incorporating query classification, structured retrieval, and reranking.

**Detailed Steps**:

1.  **`semantic_search.py` (SemanticSearchEngine Class)**:
    *   **Class**: `SemanticSearchEngine`
    *   **Initialization**: `__init__(vector_db_path: str, embedding_endpoint: str, embedding_api_key: str, reranking_endpoint: Optional[str] = None, reranking_api_key: Optional[str] = None, max_results: int = 10)`
        *   Initialize `PIRQueryClassifier`.
        *   [**PLACEHOLDER - Vector DB Initialization**: In a real implementation, initialize your chosen vector database here (e.g., ChromaDB) using `vector_db_path` and configure it to use embeddings from `embedding_endpoint` and `embedding_api_key`. For this step, a *placeholder* initialization with logging is sufficient, as detailed in `indexer.py`]. The goal is to create a *retriever* object from the vector database.
    *   **Function**: `search(query: str, query_embedding: Optional[List[float]] = None, filters: Optional[Dict[str, Any]] = None, top_k: int = 10) -> Dict[str, Any]`
        *   **Action**:
            *   **Query Classification**: Use `PIRQueryClassifier.classify_query(query)` to determine `query_type` (GENERAL, METADATA, TIMELINE, ROOT_CAUSE, IMPACT, ACTION, TABLE_DATA).
            *   **Constraint Detection**: Call `_detect_query_constraints(query)` to extract filters from the query text (e.g., incident ID, date, team). Combine these with any explicitly passed `filters` parameter.
            *   **Retriever Selection**: Call `_get_retriever_for_query(query_type)` to choose the appropriate retriever strategy (e.g., different retrievers for timeline vs. metadata queries - for now, a single retriever is sufficient). [**PLACEHOLDER - Retriever Strategy**: For this step, a *single* basic retriever from the initialized vector database is sufficient. Advanced retriever strategies (Parent Document, Multi-Vector, Self-Query) are for future enhancement. Focus on getting basic semantic search working first].
            *   **Retrieval**: [**PLACEHOLDER - Actual Retrieval**: Instead of actual retrieval for this step, implement `_simulate_retrieval(query, query_type, query_constraints)` to *simulate* search results. This function should return a list of dummy result dictionaries mimicking the structure of real search results (id, text, metadata, score). Base the simulated results loosely on the `query_type` to test query classification routing.]  In a real implementation, this would be replaced with a call to your vector database retriever using `query_embedding` and combined filters.
            *   **Reranking**: Call `_apply_reranking(query, raw_results, top_k)` to rerank the (simulated) results. [**PLACEHOLDER - Reranking**:  Implement a *dummy* reranking function `_apply_reranking` that simply sorts the `raw_results` by their 'score' in descending order and returns the top `top_k`. Reranking API integration is a future enhancement].
            *   **Formatting**: Call `_format_results(reranked_results, query)` to convert raw results into a list of `SearchResult` dataclass objects.
            *   **Table Data Extraction**: If `query_type` is `TABLE_DATA`, call `_extract_table_data(formatted_results)` to extract structured table data from results of table or table-row section types.
        *   **Input**: `query` string, optional `query_embedding` (list of floats), optional `filters` (dictionary), `top_k` (integer).
        *   **Output**: Dictionary containing: `query`, `query_type`, `constraints`, `results` (list of `SearchResult` objects), `table_data` (list of structured table data dicts - only if `query_type == TABLE_DATA`), `total_results` (integer - count of raw results before reranking), `retriever_used` (string - name of retriever used).

2.  **Helper Classes and Functions (within `semantic_search.py` module)**:
    *   `class QueryType(Enum)`: Defines enum for query types (GENERAL, METADATA, etc.).
    *   `class PIRQueryClassifier`:
        *   `__init__()`: Initializes query classification patterns (regex patterns for each `QueryType`).
        *   `classify_query(query: str) -> QueryType`: Classifies a query string into a `QueryType` based on regex pattern matching.
    *   `@dataclass class SearchResult`: Defines dataclass for search result objects (id, document_id, text, metadata, score, source_url, section_title).
    *   `_detect_query_constraints(query: str) -> Dict[str, Any]`: Extracts filters (constraints) from query string (e.g., incident ID, date keywords). Implement basic regex-based extraction for incident ID and date patterns.
    *   `_get_retriever_for_query(query_type: QueryType)`: [**PLACEHOLDER**] For now, always returns a placeholder retriever name (e.g., "base_retriever"). In the future, this function will map `query_type` to different retriever instances.
    *   `_apply_reranking(query: str, results: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]`: [**DUMMY IMPLEMENTATION**]  Sorts results by score (descending) and returns top `top_k` results.
    *   `_format_results(raw_results: List[Dict[str, Any]], query: str) -> List[SearchResult]`: Formats raw result dictionaries into `SearchResult` objects.
    *   `_extract_table_data(results: List[SearchResult]) -> List[Dict[str, Any]]`: Extracts structured table data from `SearchResult` objects where `section_type` is "table" or "table_row".
    *   `_simulate_retrieval(query: str, query_type: QueryType, constraints: Dict[str, Any]) -> List[Dict[str, Any]]`: [**DUMMY IMPLEMENTATION**] Simulates retrieval results as a list of dictionaries. Create dummy results that loosely match the `query_type` to test routing and formatting.

3.  **`PIRRetrievalService` Class (within `semantic_search.py` module)**:
    *   **Class**: `PIRRetrievalService`
    *   **Initialization**: `__init__(vector_db_path: str, embedding_endpoint: str, embedding_api_key: str, reranking_endpoint: Optional[str] = None, reranking_api_key: Optional[str] = None)`
        *   Initialize `QueryVectorizer`.
        *   Initialize `SemanticSearchEngine`.
    *   **Function**: `retrieve(query: str, filters: Optional[Dict[str, Any]] = None, top_k: int = 10) -> Dict[str, Any]`
        *   **Action**:
            *   Vectorize the query using `QueryVectorizer.vectorize_query(query)`.
            *   Check for errors in vectorization result (error key). If error, return an error response dict.
            *   Perform semantic search using `SemanticSearchEngine.search(query, query_embedding, filters, top_k)`.
            *   Add `processed_query` and `expanded_queries` from the `QueryVectorizer` result to the search results dictionary before returning.
        *   **Input**: `query` string, optional `filters` (dictionary), `top_k` (integer).
        *   **Output**: Dictionary containing search results and query processing metadata (similar output structure to `SemanticSearchEngine.search`, but also includes `processed_query`, `expanded_queries`). Includes "error" key if vectorization fails.

**Environment Variables**: Requires `VECTOR_DB_PATH`, `EMBEDDING_ENDPOINT`, `EMBEDDING_API_KEY`, `RERANKING_ENDPOINT` (optional), `RERANKING_API_KEY` (optional) -  extend `.env` file if reranking is used.

**Testing Recommendations**:
*   Unit tests using `pytest` for `PIRQueryClassifier`, `SemanticSearchEngine`, and `PIRRetrievalService`.
*   Mock vector database interactions, embedding calls, and reranking API calls for isolated testing.
*   Test `PIRQueryClassifier` for correct query type classification for various queries.
*   Test `SemanticSearchEngine.search` with different query types and verify that (simulated) retrieval and formatting work correctly.
*   Test `PIRRetrievalService.retrieve` to ensure query vectorization and semantic search are correctly orchestrated and errors are handled.
*   Log query, query type, constraints, retriever used, and results during testing to verify routing and behavior.
```

## 4. ReAct Agent Configuration (LangChain)

```text
# ROO CLINE/CURSOR PROMPT: Implement LangChain ReAct Agent for PIR Retrieval

**Module**: `pir_agent.py`

**Goal**: Create a Python module to configure a LangChain ReAct agent for interacting with the PIR retrieval system and an email tool.

**Detailed Steps**:

1.  **`pir_agent.py` (Agent Components)**:
    *   **Tool Definition - `PIRRetrievalTool`**:
        *   **Class**: `PIRRetrievalTool` (inherits from `langchain.tools.BaseTool`).
        *   `name`: "pir_search"
        *   `description`: Detailed description for agent tool selection (see example in implementation details).
        *   `args_schema`: `PIRSearchInput` (Pydantic model for input validation - define fields `query: str`, `filters: Optional[Dict[str, Any]]`).
        *   `__init__(retrieval_service: PIRRetrievalService)`: Constructor to accept initialized `PIRRetrievalService`.
        *   `_run(self, query: str, filters: Optional[Dict[str, Any]] = None) -> str`:
            *   Call `self.retrieval_service.retrieve(query=query, filters=filters, top_k=5)`.
            *   Format the retrieved results into a human-readable string using `_format_search_results(results: Dict[str, Any]) -> str`. Include section titles, document IDs, and snippet content in the formatted string.
            *   Handle potential errors from `retrieval_service.retrieve` and return error messages as strings.
        *   `_format_search_results(self, results: Dict[str, Any]) -> str`: Helper function to format search results into a descriptive string for the agent.
    *   **Tool Definition - `SendEmailTool`**:
        *   **Class**: `SendEmailTool` (inherits from `langchain.tools.BaseTool`).
        *   `name`: "send_email"
        *   `description`: Detailed description emphasizing user confirmation and logging (see example in implementation details).
        *   `args_schema`: `SendEmailInput` (Pydantic model for input validation - define fields `to: str`, `subject: str`, `body: str`).
        *   `_run(self, to: str, subject: str, body: str) -> str`:
            *   Implement a *stub* email sending functionality. Do *not* send real emails.
            *   Log the email details (to, subject, body) using `logging.info`.
            *   Return a confirmation string like: `"Email sent (stub implementation - logged details). To: {to}, Subject: {subject}"`.

2.  **`PIRAgent` Class (Agent Configuration)**:
    *   **Class**: `PIRAgent`
    *   `__init__(llm: BaseLanguageModel, retrieval_service: PIRRetrievalService, verbose: bool = False)`:
        *   Initialize `self.llm`, `self.retrieval_service`, `self.verbose`.
        *   Call `self._create_tools()` to create tool instances and store them in `self.tools`.
        *   Call `self._create_agent()` to configure and initialize the ReAct agent executor (`AgentExecutor`) and store it in `self.agent_executor`.
    *   `_create_tools(self) -> List[BaseTool]`:
        *   Instantiate `PIRRetrievalTool` and `SendEmailTool`, passing the `retrieval_service` to `PIRRetrievalTool`.
        *   Return a list containing both tool instances.
    *   `_create_agent(self) -> AgentExecutor`:
        *   **Prompt Template**: Define a comprehensive `PromptTemplate` for the ReAct agent (see example in implementation details). This template should instruct the agent on its role, how to use tools (`pir_search`, `send_email`), how to format output, and handle cases where information is not found. Include placeholders for `{format_instructions}`, `{input}`, `{agent_scratchpad}`.  Leave `{format_instructions}` empty for now.
        *   **Agent Creation**: Use `langchain.agents.create_react_agent(llm=self.llm, tools=self.tools, prompt=prompt)` to create the ReAct agent.
        *   **Agent Executor**: Create `AgentExecutor` using `AgentExecutor.from_agent_and_tools(agent=agent, tools=self.tools, verbose=self.verbose, handle_parsing_errors=True, max_iterations=10, early_stopping_method="generate")`.
        *   Return the `agent_executor`.
    *   `run(self, query: str) -> Dict[str, Any]`:
        *   Run the agent using `self.agent_executor.run(query)`.
        *   Capture and format the agent's response, tool calls, and intermediate steps into a dictionary. Return this dictionary. Include error handling: wrap agent execution in a `try...except` block. If an error occurs, log it and return an error response dictionary (including "error" key and user-friendly error message).

**Environment Variables**: No new environment variables are introduced in this step. Relies on previously defined environment variables for embedding and vector DB.

**Testing Recommendations**:
*   Unit tests using `pytest` for `PIRRetrievalTool` and `SendEmailTool` classes. Mock `PIRRetrievalService.retrieve` and `SendEmailTool`'s email sending logic for isolated testing.
*   Test `PIRAgent.run` function with various queries. Mock the LLM's responses (using `langchain_community.llms.fake.FakeListLLM`) to control agent behavior and test tool usage scenarios, including cases where the agent should use `pir_search` multiple times and potentially use `send_email`.
*   Verify that the agent follows the prompt instructions, uses tools correctly, and returns structured responses.
*   Log agent actions, tool inputs, and outputs during testing to debug and verify agent behavior.
```

## 5. Reasoning LLM Integration (Custom Model)

```text
# ROO CLINE/CURSOR PROMPT: Implement Custom Reasoning LLM Integration

**Module**: `custom_reasoning_llm.py`

**Goal**: Create a Python module to wrap the custom reasoning LLM with an OpenAI-compatible interface for seamless LangChain integration.

**Detailed Steps**:

1.  **`custom_reasoning_llm.py` (CustomReasoningLLM Class)**:
    *   **Class**: `CustomReasoningLLM` (inherits from `langchain.llms.base.LLM`).
    *   **Initialization**: `__init__(api_base: str, api_key: str, model_name: str = "reasoning-model", max_tokens: int = 2000, temperature: float = 0.2, max_retries: int = 3, timeout: int = 120, **kwargs)`
        *   Constructor to accept API endpoint (`api_base`), API key (`api_key`), model parameters (`model_name`, `max_tokens`, `temperature`, `timeout`), and retry settings (`max_retries`). Store these as class attributes. Initialize logging.
    *   **`_llm_type` Property**:  `@property _llm_type(self) -> str`: Return a string identifying the LLM type, e.g., `"custom_reasoning_llm"`.
    *   **`_format_context(context: List[Dict[str, Any]]) -> str`**:
        *   **Action**: Helper function to format retrieved context for inclusion in the LLM prompt. Iterate through the list of context dictionaries (each representing a retrieved snippet). Create a formatted string that includes "Document X", incident ID, section title, and snippet content for each context item. Return the combined formatted context string. Handle cases with no context gracefully.
    *   **`_call_api(prompt: str, **kwargs) -> Dict[str, Any]`**:
        *   **Action**:  Make the API call to the custom reasoning model endpoint (`api_base/v1/completions`) using `requests.post`.
            *   Set headers: `Content-Type: application/json`, `Authorization: Bearer {api_key}`.
            *   Prepare JSON payload: `{"model": model_name, "prompt": prompt, "max_tokens": max_tokens, "temperature": temperature, **kwargs}`.
            *   Implement retry logic using `tenacity` decorator (`@retry`) for `requests.RequestException` and `TimeoutError`. Configure `stop=stop_after_attempt(max_retries)`, `wait=wait_exponential(...)`. Log each retry attempt. Set `timeout` for `requests.post`.
            *   Handle API errors (non-200 status codes) by logging the error and raising `requests.RequestException`.
            *   Return the JSON response from the API.
    *   **`_extract_thinking(text: str) -> Tuple[str, str]`**:
        *   **Action**: Parse the model's output text to extract "thinking" steps enclosed within `<think></think>` XML-like tags. Use regex (`re.search`) with `re.DOTALL` flag to find content between tags. If `<think>` tags are found:
            *   Extract the content within the tags as the "thinking" string.
            *   Remove the `<think>...</think>` tags from the original response text using `re.sub` to get the cleaned response.
            *   Return a tuple: `(cleaned_response, thinking_content)`.
        *   If no `<think>` tags are found, return the original text and an empty string for "thinking": `(text, "")`.
    *   **`_call(self, prompt: str, stop: Optional[List[str]] = None, run_manager: Optional[CallbackManagerForLLMRun] = None, **kwargs) -> str`**:
        *   **Action**: This is the core function called by LangChain.
            *   Log the prompt (first 100 characters for brevity) for debugging.
            *   Extract `context` from `kwargs` (using `kwargs.pop("context", [])`).
            *   Call `_format_context(context)` to format the context string. Prepend the formatted context to the `prompt`.
            *   Add `stop` sequences to `kwargs` if provided (`if stop: kwargs["stop"] = stop`).
            *   Call `_call_api(prompt, **kwargs)` to get the raw API response.
            *   Extract the text response from the API response JSON (assuming OpenAI-like format: `response["choices"][0]["text"]`). Handle cases where "choices" or "text" are missing, logging a warning.
            *   Call `_extract_thinking(text)` to parse out thinking steps. Store the thinking content in `self._last_thinking` attribute (for later retrieval via `get_last_thinking`).
            *   Return the `cleaned_response` (response text without `<think>` tags).
            *   Implement error handling: Wrap API call in a `try...except` block. Log any exceptions and return an error message string (e.g., `"Error: {str(e)}" `).
    *   **`get_last_thinking(self) -> str`**:
        *   **Action**:  Return the thinking content extracted from the *most recent* call to `_call`. Retrieve this from `self._last_thinking` attribute.

2.  **`ReasoningLLMWithContext` Class (Context Integration)**:
    *   **Class**: `ReasoningLLMWithContext` (inherits from `CustomReasoningLLM`).
    *   `__init__(api_base: str, api_key: str, retrieval_service: PIRRetrievalService, max_context_items: int = 5, **kwargs)`:
        *   Constructor: Call `super().__init__(api_base=api_base, api_key=api_key, **kwargs)` to initialize `CustomReasoningLLM` part. Store `retrieval_service` and `max_context_items`.
    *   **`_get_context_for_query(query: str) -> List[Dict[str, Any]]`**:
        *   **Action**:  Retrieve relevant context snippets for a given `query` using `self.retrieval_service.retrieve(query=query, top_k=max_context_items)`. Extract the `results` list from the retrieval response. Create a list of dictionaries, each containing `"text"` (from result) and `"metadata"` (from result). Handle potential errors in `retrieval_service.retrieve` by logging the error and returning an empty list.
    *   **`generate_with_context(query: str, system_prompt: Optional[str] = None, **kwargs) -> Dict[str, Any]`**:
        *   **Action**:  Primary function to generate responses with context.
            *   Call `_get_context_for_query(query)` to retrieve context.
            *   Prepare the full prompt: If `system_prompt` is provided, combine it with the user `query`:  `f"{system_prompt}\n\nQuestion: {query}"`. Otherwise, just use `f"Question: {query}"`.
            *   Call `self._call(full_prompt, context=context, **kwargs)` to get the model's response, passing the retrieved `context` as a keyword argument.
            *   Get the "thinking" content from the last model call using `self.get_last_thinking()`.
            *   Return a dictionary containing: `"query"`, `"response"`, `"thinking"`, and `"context"`.

3.  **`create_reasoning_chain(api_base: str, api_key: str, retrieval_service, system_prompt: Optional[str] = None) -> Callable` (Factory Function)**:
    *   **Action**:  Create and return a *function* (`process_query`) that acts as the reasoning chain.
        *   Instantiate `ReasoningLLMWithContext` using provided `api_base`, `api_key`, and `retrieval_service`.
        *   Define the `process_query(query: str, **kwargs) -> Dict[str, Any]` inner function. This function should:
            *   Call `llm.generate_with_context(query, system_prompt, **kwargs)` to generate a response with context.
            *   Return the dictionary response from `generate_with_context`.
        *   Return the `process_query` function.

**Environment Variables**: Requires `REASONING_ENDPOINT`, `REASONING_API_KEY` - extend `.env` file.

**Testing Recommendations**:
*   Unit tests using `pytest` for `CustomReasoningLLM` and `ReasoningLLMWithContext` classes.
*   Mock `requests.post` to simulate API responses for different scenarios (success, errors, empty responses, responses with and without `<think>` tags).
*   Test `_call_api` for retry logic and error handling.
*   Test `_extract_thinking` to correctly parse thinking content.
*   Test `generate_with_context` to verify context retrieval and prompt formatting.
*   Test `create_reasoning_chain` to ensure the factory function returns a callable that correctly uses the LLM with context.
*   Log prompts sent to the LLM and responses received during testing to debug and verify behavior.
```

## 6. Chainlit UI Integration

```text
# ROO CLINE/CURSOR PROMPT: Implement Chainlit UI for PIR RAG Application

**Module**: `chainlit_app.py`

**Goal**: Create a Chainlit UI for the PIR RAG application, enabling user interaction, displaying agent responses, reasoning steps, and handling email actions.

**Detailed Steps**:

1.  **`chainlit_app.py` (Chainlit Application)**:
    *   **Imports**: Import `chainlit`, necessary LangChain components, and local modules (`pir_agent`, `semantic_search`, `custom_reasoning_llm`).
    *   **Global Initialization**:
        *   Declare global variables: `retrieval_service`, `reasoning_llm`, `pir_agent` (initialized to `None`).
    *   **`@cl.on_settings_update`**: Implement `update_settings(settings)` function to log settings updates from the UI (for future settings customization).
    *   **`@cl.password_auth_callback`**: Implement `auth_callback(username: str, password: str)` for basic password authentication (for demonstration - use `"admin"` username and `"password"` password). In production, use a more robust authentication method.
    *   **`extract_svg(text: str) -> List[Dict[str, str]]`**: Helper function (provided in implementation details) to extract SVG content from text responses for proper rendering in Chainlit.
    *   **`process_thinking(thinking: str) -> cl.Message`**: Asynchronous function to create and return a Chainlit `Message` element to display reasoning details in a collapsible block (`cl.Collapse`). Handle potential SVG content within `thinking` using `extract_svg` and `cl.Image` element; display text parts using `cl.Text` element within the collapse.
    *   **`format_search_results(results: List[Dict[str, Any]]) -> List[cl.Element]`**: Asynchronous function to format search results into a list of Chainlit UI elements (`cl.Accordion`). Create a `cl.Accordion` with `cl.AccordionItem` for each search result. Each `AccordionItem` should display section title, document ID, incident ID, snippet content, and a link to the source URL (if available).
    *   **`process_email_confirmation(to: str, subject: str, body: str) -> bool`**: Asynchronous function to implement user confirmation for sending emails using `cl.AskUserMessage`. Display email details (recipient, subject, body) in the `AskUserMessage`. Provide "Send Email" (confirm) and "Cancel" (reject) actions. Return `True` if user confirms, `False` otherwise.
    *   **`@cl.on_chat_start`**: Asynchronous `on_chat_start()` function to initialize the chat session:
        *   Send a welcome message using `cl.Message`.
        *   **Service Initialization**: Within a `try...except` block:
            *   Initialize `PIRRetrievalService`, `ReasoningLLMWithContext` (using environment variables for API endpoints, API keys, vector DB path), and `PIRAgent` (passing initialized `reasoning_llm` and `retrieval_service`). Use `cl.user_session.set("status", "Initializing...")` and `logger.info` to track initialization progress.
            *   Store initialized services and agent in `cl.user_session`: `cl.user_session.set("retrieval_service", ...)`, `cl.user_session.set("reasoning_llm", ...)`, `cl.user_session.set("pir_agent", ...)`.
            *   Set `cl.user_session.set("status", "Ready")` after successful initialization.
        *   **Error Handling**: In the `except` block, log the error using `logger.error` and send an error message to the user via `cl.Message` indicating initialization failure and suggesting to contact support.
    *   **`@cl.on_message`**: Asynchronous `on_message(message: cl.Message)` function to process user messages:
        *   Get `query` from `message.content`.
        *   Within a `try...except` block:
            *   **Component Retrieval**: Get `pir_agent`, `reasoning_llm` from `cl.user_session`. Check if they are initialized; if not, send an error message to the user and return.
            *   **Thinking Indicator**: Send a "Thinking..." message using `cl.Message` and `await thinking_msg.send()`.
            *   **Reasoning LLM Call**: Call `reasoning_llm.generate_with_context(query=query, system_prompt="...")` (use a system prompt defining the assistant's role).
            *   **Response Processing**: Extract `response`, `thinking`, `context` from the `generate_with_context` result dictionary.
            *   **SVG Handling**: Call `extract_svg(response)` to process response text for potential SVG content. Update the "Thinking..." message content to the *textual* part of the response using `await thinking_msg.update(content=response_content)`. If SVG elements are found, append them to `thinking_msg.elements` using `cl.Image` and `await thinking_msg.update()`.
            *   **Search Result Display**: If `context` is available, call `await format_search_results(context)` to format search results as Chainlit elements. Append these elements to `thinking_msg.elements` and update the message using `await thinking_msg.update()`.
            *   **Reasoning Display**: If `thinking` content is present, call `await process_thinking(thinking)` to create a collapsible reasoning block message and `await thinking_msg.send()` to send it.
            *   **Email Action Handling**: Check if the user query indicates an email sending intent using regex (`re.search(r'send\s+email\s+to\s+([^\s,]+@[^\s,]+)', query.lower())`). If email intent is detected:
                *   Extract potential recipient email from regex match.
                *   Generate `email_subject` and `email_body` based on query and response.
                *   Call `await process_email_confirmation(to, subject, body)` to get user confirmation.
                *   If user confirms (`confirmed == True`):
                    *   Iterate through `pir_agent.tools` to find `send_email` tool.
                    *   Call `tool._run(to=recipient, subject=subject, body=body)` to execute the email tool stub.
                    *   Send a success message via `cl.Message` indicating email "sent" (stub).
                *   If user cancels, send a cancellation message via `cl.Message`.
        *   **Error Handling**: In the `except` block, log the error using `logger.error` and send an error message to the user via `cl.Message` indicating a processing error and suggesting to rephrase the question or try again.
    *   **`if __name__ == "__main__":`**: Add this block to indicate that the script is the main Chainlit application file and log a message when initialized but before Chainlit runs it.

2.  **`config.toml` (Chainlit Configuration)**: Create a `config.toml` file (if not already present) in the same directory as `chainlit_app.py` to customize Chainlit UI appearance (name, description, theme, and enable features like prompt playground - see example `config.toml` in implementation details).

**Environment Variables**: Requires `VECTOR_DB_PATH`, `EMBEDDING_ENDPOINT`, `EMBEDDING_API_KEY`, `REASONING_ENDPOINT`, `REASONING_API_KEY` (all previously defined).

**Testing Recommendations**:
*   Start the Chainlit application using `chainlit run chainlit_app.py`.
*   Test basic chat functionality: Send various queries and verify that the agent responds with relevant information, including search results and reasoning steps displayed correctly in collapsible blocks and accordions.
*   Test SVG rendering: Verify that if the reasoning LLM generates SVG content, it is correctly rendered in the Chainlit UI.
*   Test email action: Send a query that triggers the email tool (e.g., "send <NAME_EMAIL>"). Verify that the user confirmation dialog appears, and upon confirmation, the email tool stub is executed (check logs for email details). Verify cancellation flow as well.
*   Test error handling: Simulate errors (e.g., by providing invalid API keys or vector DB path in `.env`) and verify that the UI displays user-friendly error messages during initialization and query processing.
*   Check logs (`chainlit_app.log`) for detailed information about application execution, errors, and agent behavior.
```

## 7. Email Tool Implementation (Stub)

```text
# ROO CLINE/CURSOR PROMPT: Implement Email Tool Stub

**Module**: `email_tool.py` (or reuse `pir_agent.py` and place `SendEmailTool` definition there for simplicity if preferred)

**Goal**: Create a stub implementation of the email sending tool for the PIR RAG application.

**Detailed Steps**:

1.  **`email_tool.py` (or `pir_agent.py`) - Email Tool Class**:
    *   **Class**: `SendEmailTool` (inherits from `langchain.tools.BaseTool`).
    *   `name`: "send_email"
    *   `description`: Comprehensive description for agent use (see example in implementation details), emphasizing user confirmation and logging behavior of this *stub* implementation.
    *   `args_schema`: `SendEmailInput` (Pydantic model for structured input and validation):
        *   `class SendEmailInput(BaseModel)`:
            *   `to: EmailStr = Field(..., description="Recipient email address")`: Use `EmailStr` from `pydantic` for email validation.
            *   `subject: str = Field(..., description="Email subject line")`
            *   `body: str = Field(..., description="Email body content")`
            *   `cc: Optional[List[EmailStr]] = Field(None, description="CC recipient email addresses")` (Optional CC recipients - list of `EmailStr`).
            *   `bcc: Optional[List[EmailStr]] = Field(None, description="BCC recipient email addresses")` (Optional BCC recipients - list of `EmailStr`).
            *   `attachments: Optional[List[str]] = Field(None, description="List of file paths for attachments")` (Optional attachments - list of file paths - in stub, just log file names, don't handle actual files).
    *   `__init__(self)`: Constructor - call `super().__init__()`. Initialize logging (`self.logger = logging.getLogger(__name__)`).
    *   `_run(self, to: str, subject: str, body: str, cc: Optional[List[str]] = None, bcc: Optional[List[str]] = None, attachments: Optional[List[str]] = None) -> str`:
        *   **Action**:  Implement the stub email sending logic.
            *   **Input Validation**: Use `SendEmailInput.parse_obj({"to": to, "subject": subject, "body": body, "cc": cc, "bcc": bcc, "attachments": attachments})` to validate input against the Pydantic schema. Handle `ValidationError` and return an error string if validation fails (indicating invalid email addresses or input).
            *   **Chainlit User Confirmation**: Use `await cl.AskUserMessage(...)` to ask for user confirmation *before* "sending" the email. Display email details (to, subject, body, CC, BCC, attachments - filenames only) in the confirmation message. Provide "Send Email" and "Cancel" actions.
            *   **Confirmation Handling**: If user confirms (`res and res.get("value") == "yes"`):
                *   **Logging**: Log email details (recipient, subject, body, CC, BCC, attachments - filenames if provided) using `self.logger.info` in JSON format for structured logging. Include a timestamp and a unique "email_id" (generate using `uuid.uuid4()`). Indicate in the log message that this is a *stub* implementation.
                *   **Return Success Message**: Return a confirmation string like: `"Email sent (stub implementation - details logged). Email ID: {email_id}"`.
            *   **Cancellation Handling**: If user cancels or times out:
                *   Log email cancellation using `self.logger.info`.
                *   Return a cancellation message string: `"Email sending cancelled by user."`.
    *   `_arun(self, to: str, subject: str, body: str, cc: Optional[List[str]] = None, bcc: Optional[List[str]] = None, attachments: Optional[List[str]] = None) -> str`: Implement the asynchronous version of `_run`, making sure to `await cl.AskUserMessage(...)`.

**Environment Variables**: No new environment variables.

**Testing Recommendations**:
*   Unit tests using `pytest` for `SendEmailTool` class.
*   Test `_run` and `_arun` methods with valid and invalid email addresses (using Pydantic validation).
*   Mock `cl.AskUserMessage` to control user confirmation responses during testing.
*   Verify that for confirmed emails, email details are correctly logged (check logs) and a success message is returned.
*   Verify that for cancelled emails, cancellation is logged and a cancellation message is returned.
*   Test handling of optional `cc`, `bcc`, and `attachments` parameters (verify they are logged correctly, even though attachments are not actually handled).
```

## 8. End-to-End Workflow Testing

```text
# ROO CLINE/CURSOR PROMPT: Implement End-to-End Workflow Testing

**Module**: `test_e2e.py` (or integrate into existing `test_app.py` if created earlier)

**Goal**: Create end-to-end tests to verify the complete workflow of the PIR RAG application, from data loading and indexing to query retrieval and agent interaction, including email tool stub.

**Detailed Steps**:

1.  **`test_e2e.py` (End-to-End Test Script)**:
    *   **Imports**: Import `pytest`, necessary modules from your application (`indexer`, `semantic_search`, `query_vectorizer`, `pir_agent`, `custom_reasoning_llm`), `langchain_community.llms.fake.FakeListLLM`, and `unittest.mock` for mocking.
    *   **Fixtures**:
        *   **`sample_pir_html_data(tmp_path)` Fixture**:
            *   **Action**: Create a temporary HTML file (using `tmp_path` fixture from `pytest`) containing *simplified* sample PIR data (mimicking `Incident-PIR.html` structure, but shorter and with easily verifiable content). Include key sections ("Incident Summary", "Root Cause", "Actions Arising") and table data. Save the HTML file to `tmp_path / "sample_pir.html"`.
            *   **Output**: Path to the created sample HTML file.
        *   **`indexed_vector_store(sample_pir_html_data)` Fixture (scope="module")**:
            *   **Action**:  This fixture will run once per test module and provide an *indexed* vector store.
                *   Initialize `PIRIndexer` (mock Confluence credentials and API endpoints if needed - use dummy values for testing that don't require live Confluence). Use an *in-memory* ChromaDB for `vector_db_path` (no persistence needed for testing) or a temporary directory. Use a mock `EmbeddingHandler` or a lightweight local embedding model (if feasible and fast) for testing.
                *   Call `indexer.index_pir_pages(space_key="TEST", home_page_id="123", recursive=False, force_reindex=True)` to index the sample PIR data from `sample_pir_html_data` file. Force re-indexing (`force_reindex=True`) to ensure data is indexed every test run.
                *   Return the initialized `PIRRetrievalService` (or the `SemanticSearchEngine` or vector store object directly if needed for testing different levels).
            *   **Output**: Initialized and indexed `PIRRetrievalService` object.
        *   **`mock_reasoning_llm_responses` Fixture**:
            *   **Action**: Create a `FakeListLLM` instance (from `langchain_community.llms.fake`) that will be used to mock the custom reasoning LLM. Predefine a list of responses for different agent actions and queries that will be returned by the mock LLM in sequence. Design these responses to test different scenarios: tool usage (pir_search, send_email), correct reasoning flow, and final answer generation.
            *   **Output**: `FakeListLLM` instance with predefined responses.
        *   **`pir_agent_instance(indexed_vector_store, mock_reasoning_llm_responses)` Fixture (scope="function")**:
            *   **Action**:  This fixture will run before each test function and provide an initialized `PIRAgent` instance.
                *   Initialize `CustomReasoningLLM` using `mock_reasoning_llm_responses` for the LLM (and dummy API credentials).
                *   Initialize `PIRAgent` using the mocked `CustomReasoningLLM` and the `PIRRetrievalService` from `indexed_vector_store` fixture.
                *   Return the initialized `PIRAgent` instance.
            *   **Output**: Initialized `PIRAgent` object.
    *   **Test Functions**:
        *   **`test_e2e_incident_summary_query(pir_agent_instance)`**:
            *   **Action**: Test a query about incident summary (e.g., "What is the summary of incident IN_TEST_001?").
            *   Call `pir_agent_instance.run(query="...")`.
            *   Assert that the response contains expected keywords from the sample PIR data related to the summary section.
        *   **`test_e2e_root_cause_timeline_query(pir_agent_instance)`**:
            *   **Action**: Test a more complex query about root cause and timeline (e.g., "What was the root cause and timeline of incident IN_TEST_002?").
            *   Call `pir_agent_instance.run(query="...")`.
            *   Assert that the response contains keywords related to both root cause and timeline sections from the sample data.
        *   **`test_e2e_email_action_query(pir_agent_instance, monkeypatch)`**:
            *   **Action**: Test a query that should trigger the email tool (e.g., "Send an <NAME_EMAIL> with the action items for IN_TEST_003").
            *   Use `monkeypatch` (pytest fixture) to mock the `cl.AskUserMessage` function within the `SendEmailTool` to *automatically* return a "yes" confirmation (to simulate user approval in testing).
            *   Call `pir_agent_instance.run(query="...")`.
            *   Assert that the agent response indicates that an email was "sent" (stub). Verify that email details are logged (using `caplog` pytest fixture to capture logs - assert that log messages contain email recipient, subject, etc.).
        *   **`test_e2e_no_relevant_info_query(pir_agent_instance)`**:
            *   **Action**: Test a query for which no relevant information should be found in the sample data (e.g., "What is the weather in London for IN_TEST_004?").
            *   Call `pir_agent_instance.run(query="...")`.
            *   Assert that the agent response indicates that no relevant information was found and suggests rephrasing the query.

2.  **Test Execution**: Run tests using `pytest test_e2e.py -s -v --log-level=INFO`. Use `-s` to see print outputs, `-v` for verbose output, and `--log-level=INFO` to capture INFO level logs. Review test outputs and logs to verify test success and debug failures.

**Environment Variables**: No new environment variables for testing specifically. Tests will reuse existing environment variable configurations (or mock them).

**Testing Recommendations**:
*   Focus on creating tests that cover the core end-to-end workflow: data loading -> indexing -> query -> retrieval -> agent reasoning -> response generation -> (stub) email action.
*   Use mocks and fake LLMs extensively to isolate components and control test behavior.
*   Use `assert` statements to verify expected outputs at each stage.
*   Utilize pytest fixtures for setup and cleanup of test resources (temporary files, vector stores, mocked objects).
*   Log extensively within test functions and during agent execution to capture detailed information for debugging and analysis. Review logs carefully to understand test execution flow and identify any issues. Aim for comprehensive coverage of positive and negative test cases (queries with and without relevant information, queries triggering different agent actions).
```

## 9. Project Requirements Document (PRD)

```text
# ROO CLINE/CURSOR PROMPT: Generate Project Requirements Document (PRD)

**Module**: `docs/prd.md` (create a new file in `docs` directory)

**Goal**: Generate a comprehensive Project Requirements Document (PRD) in Markdown format for the Confluence Incident PIR RAG application.

**Document Structure (Sections and Content)**:

1.  **Introduction**:
    *   **Problem Statement**: Describe the inefficiency and challenges of manually accessing and utilizing information within structured Confluence PIR pages. Highlight the time-consuming nature of information retrieval and analysis for incident reviews and follow-up actions.
    *   **Project Goal**: Clearly state the primary objective: To develop an AI-powered RAG application that enables efficient and accurate retrieval of information from Confluence PIR documents, empowering users to quickly answer questions about incidents, understand root causes, track timelines, and potentially automate actions based on retrieved data. Emphasize the focus on improved access to incident knowledge and streamlining incident management workflows.
    *   **Project Scope**: Define the boundaries of the project. Specify that the application will focus on:
        *   **Data Source**: Confluence PIR pages within a designated Confluence space and hierarchy, adhering to a predefined HTML structure.
        *   **Core Technologies**: Chainlit for UI, LangChain ReAct agent for orchestration, LlamaIndex for indexing and retrieval, custom OpenAI-compatible embedding and reasoning models.
        *   **Key Functionality**: Accurate retrieval of document snippets, question answering based on PIR content, and a *stubbed* email tool for demonstrating action automation (user-approved email sending simulation).
        *   **Initial Focus**: Prioritize retrieval accuracy and demonstrate a functional end-to-end RAG pipeline with the defined tech stack. Acknowledge that advanced features (e.g., real email sending, advanced reranking, complex UI enhancements, production deployment) are out of scope for the initial phase but can be considered for future iterations.

2.  **Application Flow**:
    *   **User Interaction**: Describe the step-by-step user flow within the Chainlit application. Start from the user initiating a query in the Chainlit chat interface and trace the flow through:
        *   Query input by user.
        *   Query vectorization using the custom embedding model.
        *   Structure-aware retrieval of relevant snippets from indexed Confluence PIRs.
        *   ReAct agent processing: Agent receives query and retrieved snippets, reasons using the custom reasoning LLM (with access to the PIR retrieval tool), formulates a response, and potentially utilizes the email tool (stub).
        *   Display of the agent's final answer in the Chainlit UI.
        *   Display of collapsible reasoning steps (if available from the reasoning LLM).
        *   User confirmation prompt for email actions (if triggered by the agent).

3.  **Core Features**:
    *   List and describe the essential features of the PIR RAG application:
        *   **Confluence Data Ingestion**: Automated and efficient loading of PIR pages from a specified Confluence space and parent page, handling pagination and API rate limits.
        *   **Structure-Aware Indexing**: Intelligent parsing of PIR HTML structure to extract relevant metadata (from headings, tables, specific HTML elements) and strategic chunking of documents to optimize retrieval accuracy based on PIR structure.
        *   **Custom Embedding Model**: Integration and utilization of a specified custom OpenAI-compatible embedding model for document and query vectorization, ensuring consistency across the RAG pipeline.
        *   **Accurate Snippet Retrieval**: Implementation of an optimized retrieval strategy that leverages semantic similarity search within the vector database, incorporating structure-aware chunking and metadata filtering for precise and contextually relevant snippet retrieval.
        *   **ReAct Agent Orchestration**: Utilization of a LangChain ReAct agent to orchestrate the question answering process, leveraging the PIR retrieval tool and the custom reasoning LLM to generate informative and actionable responses.
        *   **Custom Reasoning LLM**: Seamless integration with a custom OpenAI-compatible reasoning model, enabling the agent to perform reasoning, generate human-like answers, and potentially trigger actions based on retrieved information.
        *   **Chainlit User Interface**: User-friendly chat interface built with Chainlit, providing an interactive experience for querying PIR data and receiving agent responses.
        *   **Reasoning Transparency**: Display of the agent's step-by-step thought process (reasoning details) in a collapsible format within the UI, enhancing user trust and understanding of the AI's decision-making.
        *   **SVG Rendering**: Capability to properly render SVG content generated by the reasoning LLM within the Chainlit UI, supporting richer and more visual responses.
        *   **Action Tool (Email Stub)**: Inclusion of a stubbed "send email" tool that simulates email sending functionality, allowing the agent to propose and (with user confirmation) execute email actions based on retrieved PIR information, demonstrating potential automation capabilities.

4.  **Tech Stack**:
    *   Clearly list the technologies used in the project, categorized by layer:
        *   **Frontend**: Chainlit (Python library for chat UI)
        *   **Backend/Orchestration**: Python, LangChain (ReAct Agent framework, Tools)
        *   **Indexing and Retrieval**: LlamaIndex (HTML parsing, VectorStoreIndex - optional, can also use LangChain vector stores directly), LangChain (Retrievers - optional)
        *   **Vector Store**: ChromaDB (persistent vector database for storing embeddings and metadata)
        *   **Embedding Model**: [Specify Name of Custom OpenAI-compatible Embedding Model] - clearly mention "Custom OpenAI-compatible Embedding Model" and ideally the specific model name if available.
        *   **Reasoning Model**: [Specify Name of Custom OpenAI-compatible Reasoning Model (Non-Streaming)] - clearly mention "Custom OpenAI-compatible Reasoning Model (Non-Streaming)" and ideally the specific model name.
        *   **Development Tools**: Python 3.9+, Pip/Poetry (for dependency management), Git (for version control), Roo Cline/Cursor (or similar AI IDEs) for development.
        *   **Deployment (Optional - Initial Development)**: For initial development and testing, local deployment using `chainlit run app.py` is sufficient. Mention that containerization (Docker) and cloud platform deployment are potential future considerations for productionizing the application.

**Output Format**: Generate the PRD content in Markdown format (`prd.md` file). Ensure clear section headings, bullet points for feature lists, and concise and informative descriptions for each section and feature. The PRD should be a well-structured document suitable for communicating project goals, scope, and technical details to stakeholders.
```