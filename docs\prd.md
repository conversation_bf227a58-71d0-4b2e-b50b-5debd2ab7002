# Project Requirements Document: Confluence Incident PIR RAG Application

## 1. Introduction

### Problem Statement

Accessing and utilizing information within structured Confluence Post Incident Review (PIR) pages is often inefficient. Manually searching through numerous pages to find specific details about incident timelines, root causes, action items, or impact analysis is time-consuming and prone to inconsistency. This hinders effective incident review, knowledge sharing, and timely follow-up actions, ultimately impacting operational efficiency and learning from past incidents.

### Project Goal

The primary objective of this project is to develop an AI-powered Retrieval-Augmented Generation (RAG) application that enables efficient, accurate, and conversational retrieval of information from Confluence PIR documents. The application aims to empower users (e.g., SREs, engineers, managers) to quickly answer questions about past incidents, understand root causes, track timelines, identify action items, and potentially automate related tasks (like drafting email summaries) based on the retrieved data. The core focus is on improving access to incident knowledge and streamlining incident management workflows through an intuitive chat interface.

### Project Scope

#### In Scope:

*   **Data Source**: Confluence pages identified as PIRs within a designated Confluence space and hierarchy, assuming adherence to a predefined, consistent HTML structure (using H1 for title/metadata, H3 for sections, tables for structured data).
*   **Core Technologies**:
    *   UI: Chainlit
    *   Orchestration: <PERSON><PERSON><PERSON><PERSON> (ReAct Agent)
    *   Indexing/Retrieval: LangChain/LlamaIndex components, ChromaDB (Vector Store)
    *   Models: Custom OpenAI-compatible models for embeddings and reasoning (non-streaming).
*   **Key Functionality**:
    *   Automated loading and parsing of Confluence PIR pages.
    *   Structure-aware indexing of PIR content (sections, tables).
    *   Semantic search and retrieval of relevant text snippets based on user queries.
    *   Question answering leveraging retrieved context via a ReAct agent and custom reasoning LLM.
    *   Display of agent reasoning steps (transparency).
    *   Rendering of SVG content generated by the reasoning LLM.
    *   A **stubbed** email tool demonstrating potential action automation (logs email details after user confirmation via UI, does not send real emails).
*   **Initial Focus**: Prioritize retrieval accuracy, demonstrate a functional end-to-end RAG pipeline with the defined tech stack, and provide a user-friendly chat interface via Chainlit. Basic authentication for the Chainlit app.

#### Out of Scope (Initial Phase):

*   Handling highly variable or unstructured PIR formats.
*   Real email sending functionality (only stubbed logging).
*   Advanced reranking algorithms beyond basic score sorting (unless integrated via API).
*   Complex UI enhancements beyond standard Chainlit features.
*   Production deployment infrastructure (e.g., containerization scaling, advanced monitoring).
*   Support for data sources other than the specified Confluence space/hierarchy.
*   Real-time indexing (indexing is assumed to be run periodically or on demand).
*   Advanced access control based on Confluence permissions within the RAG app.

## 2. Application Flow

The user interacts with the application through a Chainlit web interface:

1.  **Authentication**: User authenticates via basic password auth (admin/password for demo).
2.  **Initialization**: On chat start, backend services (Retrieval Service, Reasoning LLM) are initialized.
3.  **Query Input**: User types a natural language question into the chat input (e.g., "What was the root cause of INC-123?", "Summarize the timeline for the API outage last week", "Send an <NAME_EMAIL> with the action items from PIR-ABC").
4.  **Query Processing (Backend - ReasoningLLMWithContext)**:
    *   The query is received by the `ReasoningLLMWithContext`.
    *   The `PIRRetrievalService` is invoked to vectorize the query using the custom embedding model (via `QueryVectorizer`).
    *   The `PIRRetrievalService` performs a semantic search against the ChromaDB vector store using the query vector, applying filters derived from the query (e.g., incident ID) if detected. Structure-aware chunks (sections, table rows) are retrieved based on relevance.
    *   The retrieved context snippets (text and metadata) are collected.
5.  **Reasoning & Response Generation (Backend - ReasoningLLMWithContext)**:
    *   The retrieved context snippets are formatted and prepended to the original user query along with any system prompt.
    *   This combined prompt is sent to the custom reasoning LLM via the `CustomReasoningLLM` wrapper.
    *   The LLM processes the prompt and context, generating a response. It may include reasoning steps within `<think>` tags.
6.  **Response Display (Chainlit UI)**:
    *   The application receives the LLM's response.
    *   A "Thinking..." message is updated with the final textual answer.
    *   Any SVG content within the response is extracted and rendered as images.
    *   Retrieved context snippets are displayed in an accordion element attached to the response message.
    *   If `<think>` tags were present, the extracted reasoning steps are displayed in a separate collapsible message block.
7.  **Email Action (Conditional)**:
    *   If the user's query indicated an intent to send an email (e.g., "send email to...") and the necessary details (recipient) are present:
        *   The Chainlit backend uses the LLM response to formulate the email subject and body.
        *   An `AskUserMessage` prompt appears in the UI, showing the email preview and asking for confirmation ("Send Email (Log Stub)" / "Cancel").
        *   If the user confirms: The `SendEmailTool` stub is executed (via `_arun`), which logs the email details (To, Subject, Body, CC/BCC/Attachments if provided, Email ID, Timestamp) to the application logs. A confirmation message is displayed in the chat.
        *   If the user cancels: A cancellation message is displayed in the chat.

## 3. Core Features

*   **Confluence Data Ingestion**: Loads PIR pages efficiently from a specified Confluence space/hierarchy using the Confluence REST API. Handles pagination, retries on transient errors (e.g., rate limiting), and filters pages based on title/labels to identify PIRs.
*   **Structure-Aware Indexing**: Parses the HTML (`storage` format) of PIR pages using `BeautifulSoup`. Extracts key metadata (Incident ID, Date, Team, Subject from H1) and identifies semantic sections based on H3 tags. Extracts and structures data from tables. Creates distinct text chunks for document sections and table rows to enable targeted retrieval. Uses ChromaDB for persistent vector storage. Implements incremental indexing based on content hash (page body + version) to avoid redundant processing.
*   **Custom Embedding Model Integration**: Utilizes a specified custom OpenAI-compatible embedding model API (via `EmbeddingHandler`) for creating vector representations of text chunks and user queries. Handles batching and API retries.
*   **Accurate Snippet Retrieval**: Employs semantic similarity search within the ChromaDB vector store using query embeddings generated by the same custom model. Leverages the structure-aware chunks and allows metadata filtering (e.g., by `incident_id`, `section_type`) to retrieve precise and contextually relevant snippets.
*   **Custom Reasoning LLM Integration**: Seamlessly integrates with a specified custom OpenAI-compatible reasoning LLM API (non-streaming, via `CustomReasoningLLM`) for generating answers based on the query and retrieved context. Handles API calls, retries, and context formatting.
*   **Reasoning Transparency**: Extracts and displays the LLM's internal thought process (content within `<think>` tags) in a collapsible UI element, providing insight into how the answer was generated.
*   **Chainlit User Interface**: Provides an interactive and user-friendly chat interface built with Chainlit for querying PIR data, viewing responses, context, and reasoning steps. Includes basic password authentication.
*   **SVG Rendering**: Capable of detecting and rendering SVG diagrams or images generated by the reasoning LLM directly within the chat interface.
*   **Action Tool (Email Stub)**: Includes a `SendEmailTool` (LangChain Tool) that simulates sending emails. It validates input (using Pydantic `EmailStr`), prompts the user for confirmation via the Chainlit UI (`cl.AskUserMessage`), and upon confirmation, logs the intended email details (including a unique ID and timestamp) in a structured format. **No actual email is sent.**

## 4. Tech Stack

*   **Frontend**: Chainlit (`chainlit` Python library)
*   **Backend/Orchestration**: Python 3.10+, LangChain (`langchain`, `langchain-community` for agents, tools, prompts)
*   **Indexing and Retrieval**: LangChain/Custom Code (HTML Parsing with `beautifulsoup4`, Chunking Logic), ChromaDB (`chromadb` client library)
*   **Vector Store**: ChromaDB (Persistent local storage)
*   **Embedding Model**: Custom OpenAI-compatible Embedding Model (via REST API) - *Specify Model Name/Provider if known*
*   **Reasoning Model**: Custom OpenAI-compatible Reasoning Model (Non-Streaming, via REST API) - *Specify Model Name/Provider if known*
*   **Development Tools**:
    *   Python 3.10+
    *   Dependency Management: `uv` (preferred) or `pip` with `requirements.txt`
    *   Code Formatting/Linting: `ruff`
    *   Testing: `pytest`, `pytest-asyncio`, `unittest.mock`
    *   Version Control: `git`
    *   Environment Variables: `python-dotenv` (`.env` file)
    *   IDE: Roo Cline / Cursor / VS Code or similar AI-assisted IDE
*   **Libraries**: `requests`, `pydantic`, `tenacity` (for retries)
*   **Deployment (Initial Development)**: Local execution via `chainlit run src/chainlit_app.py -w`. Containerization (Docker) and cloud deployment (e.g., Docker Compose, Kubernetes, Cloud Run) are potential future considerations.