import pytest
from unittest.mock import MagicMock, patch
import chromadb
from llama_index import VectorStoreIndex, ServiceContext
from llama_index.vector_stores import ChromaVectorStore

from src.indexing import get_vector_index

@patch("src.indexing.chromadb.PersistentClient")
@patch("src.indexing.ChromaVectorStore")
@patch("src.indexing.load_index_from_storage")
@patch("src.indexing.VectorStoreIndex")
@patch("src.indexing.ServiceContext.from_defaults")
def test_get_vector_index(mock_service_context, mock_vector_store_index, mock_load_index_from_storage, mock_chroma_vector_store, mock_persistent_client):
    # Mock setup
    mock_client = MagicMock()
    mock_persistent_client.return_value = mock_client
    mock_chroma_vector_store.return_value = MagicMock()
    mock_load_index_from_storage.return_value = MagicMock()
    mock_vector_store_index.return_value = MagicMock()
    mock_service_context.return_value = MagicMock()

    # Test load path
    index = get_vector_index(load_path="load_path", build_path="build_path", nodes=[MagicMock()])
    mock_load_index_from_storage.assert_called_once()
    assert index is not None

    mock_load_index_from_storage.reset_mock()

    # Test build path
    index = get_vector_index(load_path=None, build_path="build_path", nodes=[MagicMock()])
    mock_vector_store_index.assert_called_once()
    assert index is not None

    mock_vector_store_index.reset_mock()

    # Test error path (no nodes provided)
    with pytest.raises(ValueError):
        get_vector_index(load_path="load_path", build_path="build_path", nodes=[])