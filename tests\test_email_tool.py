from io import StringIO
import pytest
from unittest.mock import MagicMock
from langchain.tools import Tool

from src.email_tool import send_email_stub, create_email_tool

def test_send_email_stub(capsys):
    # Call the function
    result = send_email_stub("<EMAIL>", "Test Subject", "Test Body")

    # Capture the print output
    captured = capsys.readouterr()

    # Assert the print output
    assert "Sending <NAME_EMAIL> with subject Test Subject" in captured.out
    assert "Body: Test Body" in captured.out

    # Assert the return string
    assert result == "Email sent successfully!"


def test_create_email_tool():
    # Call the function
    tool = create_email_tool()

    # Assert that it returns a Tool instance
    assert isinstance(tool, Tool)

    # Assert that the schema is correct
    assert tool.args_schema is not None
    assert tool.name == "send_email"
    assert tool.description.startswith("Useful for sending emails")
