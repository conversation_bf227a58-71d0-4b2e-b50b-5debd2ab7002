# Environment and local settings
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.idea/
.vscode/
*.code-workspace
.DS_Store

# Python cache
__pycache__/
src/__pycache__
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Storage and data
storage/
.storage/
.files/
data/
*.vector
*.faiss
*.pkl
*.bin

# Logs
logs/
*.log
npm-debug.log*

# Tests
.coverage
htmlcov/
.pytest_cache/
coverage.xml
*.cover

# Chainlit
.chainlit
