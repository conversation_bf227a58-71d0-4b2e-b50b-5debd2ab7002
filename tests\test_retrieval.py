import pytest
from unittest.mock import MagicMock

from src.retrieval import get_advanced_query_engine, detect_pir_filters

def test_get_advanced_query_engine():
    # Mock VectorStoreIndex and SentenceTransformerRerank
    mock_index = MagicMock()
    mock_rerank = MagicMock()

    # Test with filters
    query_engine = get_advanced_query_engine(
        index=mock_index,
        rerank=mock_rerank,
        filters=[{"key": "severity", "value": "high"}]
    )
    mock_index.as_query_engine.assert_called_once()
    kwargs = mock_index.as_query_engine.call_args.kwargs
    assert kwargs["filters"] == [{"key": "severity", "value": "high"}]
    assert "node_postprocessors" in kwargs

    # Test without filters
    mock_index.reset_mock()
    query_engine = get_advanced_query_engine(
        index=mock_index,
        rerank=mock_rerank,
        filters=[]
    )
    mock_index.as_query_engine.assert_called_once()
    kwargs = mock_index.as_query_engine.call_args.kwargs
    assert kwargs["filters"] == []
    assert "node_postprocessors" in kwargs


def test_detect_pir_filters():
    # Test with filters
    query = "PIRs with severity high and app App1"
    filters = detect_pir_filters(query)
    assert filters == [{"key": "severity", "value": "high"}, {"key": "apps", "value": "App1"}]

    # Test without filters
    query = "PIRs"
    filters = detect_pir_filters(query)
    assert filters == []

    # Test with special characters in value
    query = "PIRs with team Team-Awesome"
    filters = detect_pir_filters(query)
    assert filters == [{"key": "team", "value": "Team-Awesome"}]
