from llama_index.core import VectorStoreIndex
from llama_index.core.retrievers import VectorIndexAutoRetriever
from llama_index.core.vector_stores.types import VectorStoreInfo, MetadataInfo
from llama_index.core.schema import QueryBundle, NodeWithScore
from llama_index.core.llms.llm import LLM
from llama_index.llms.openai import OpenAI
from llama_index.core.tools import QueryEngineTool, ToolMetadata, FunctionTool
# from llama_index.core.vector_stores import VectorStoreIndex
from llama_index.core.postprocessor import SentenceTransformerRerank
from typing import List, Optional, Any, Dict, Tuple
import logging
import logging

logger = logging.getLogger(__name__)


def get_auto_retriever(index: VectorStoreIndex, similarity_top_k: int = 5) -> VectorIndexAutoRetriever:
    """
    Creates a LlamaIndex VectorIndexAutoRetriever which uses an LLM to generate metadata filters dynamically.

    Args:
        index (VectorStoreIndex): The index to retrieve from.
        similarity_top_k (int): The number of nodes to retrieve from the index.

    Returns:
        VectorIndexAutoRetriever: The auto-retriever that dynamically generates metadata filters.
    """
    # Define metadata schema using AttributeInfo objects
    # This allows the LLM to understand the structure of our metadata and generate
    # appropriate filters when processing user queries
    metadata_info = [
        MetadataInfo(
            name="incident_id",
            type="str",
            description="Unique ID of the incident, e.g., INC123456"
        ),
        MetadataInfo(
            name="severity",
            type="str",
            description="Severity level (High, Medium, Low)"
        ),
        MetadataInfo(
            name="section_title", 
            type="str",
            description="The section of the PIR document (Summary, Timeline, Root Cause Analysis, etc.)"
        ),
        MetadataInfo(
            name="labels",
            type="list[str]",
            description="Keywords or tags associated with the incident"
        )
    ]

    # Create VectorStoreInfo with content and metadata information
    # This object bundles information about what the vector store contains and how to filter it
    vector_store_info = VectorStoreInfo(
        content_info="PIR section content",
        metadata_info=metadata_info
    )

    # Get the LLM from Settings to ensure we're using a properly configured LLM
    from llama_index.core import Settings
    llm = Settings.llm
    
    if llm is None:
        logger.warning("No LLM found in Settings, auto-retriever may not work properly")

    # Create the auto-retriever with the vector store info
    retriever = VectorIndexAutoRetriever(
        index,
        vector_store_info=vector_store_info,
        llm=llm,  # Explicitly set the LLM
        similarity_top_k=similarity_top_k,
        verbose=True  # helps debug LLM filter generation
    )

    return retriever


def create_pir_retrieval_tool(index: VectorStoreIndex, similarity_top_k: int = 10, rerank_top_n: int = 3) -> FunctionTool:
    """
    Creates a LlamaIndex Tool wrapping the `VectorIndexAutoRetriever` and `SentenceTransformerRerank` logic.

    Args:
        index (VectorStoreIndex): The index to retrieve from.
        similarity_top_k (int): The number of nodes to retrieve from the index.
        rerank_top_n (int): The number of nodes to rerank.

    Returns:
        FunctionTool: The retrieval tool.
    """

    def run_retrieval_and_rerank(query_text: str) -> Dict[str, Any]:
        """
        Retrieves and re-ranks relevant sections from Post-Incident Review (PIR) documents based on a user query.
        """
        logger.info(f"PIR Retrieval Tool running for: '{query_text}'")
        try:
            retriever = get_auto_retriever(index, similarity_top_k=similarity_top_k)  # Creates auto-retriever (with LLM filter generation)
            retrieved_nodes = retriever.retrieve(query_text)  # Returns List[NodeWithScore]
            if not retrieved_nodes:
                return {'response': 'No relevant documents found.', 'sources': []}
            
            # Apply reranking to improve relevance
            reranker = SentenceTransformerRerank(top_n=rerank_top_n, model="cross-encoder/ms-marco-MiniLM-L-6-v2")
            # reranker = SentenceTransformerRerank(top_n=rerank_top_n, model="all-MiniLM-L6-v2")
            query_bundle = QueryBundle(query_text)
            reranked_nodes = reranker.postprocess_nodes(retrieved_nodes, query_bundle=query_bundle)
            logger.info(f"Retrieved {len(retrieved_nodes)} nodes, reranked to {len(reranked_nodes)} nodes.")

            # Format structured output for the agent
            context_str = "\n\n---\n\n".join([node.get_content() for node in reranked_nodes])  # Concatenated context
            sources = [{'id': node.node.node_id, 'text': node.node.text, 'score': node.score, 'metadata': node.node.metadata} for node in reranked_nodes]  # Use reranked nodes
            result = {'retrieved_context': context_str, 'sources': sources} # Provide context string for agent
            logger.info(f"Tool returning context from {len(sources)} sources.")
        except Exception as e:
            logger.error(f"Retrieval/Rerank failed for tool: {e}", exc_info=True)
            result = {"error": f"Failed to retrieve/rerank documents: {e}"}
        return result

    tool_metadata = ToolMetadata(
        name="PIR_Retriever_Tool",
        description="Retrieves and re-ranks relevant sections from Post-Incident Review (PIR) documents based on a user query. It automatically filters based on Incident IDs (INCxxxxxx) or severity (high/medium/low) mentioned in the query."
    )
    return FunctionTool(fn=run_retrieval_and_rerank, metadata=tool_metadata) # Pass metadata directly
