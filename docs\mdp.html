<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incident PIR RAG Application - MDP Plan</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.2/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 0;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        h1, h2, h3, h4 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        h1 { font-size: 2.25rem; }
        h2 { font-size: 1.875rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }
        p, ul, ol {
            margin-bottom: 1rem;
        }
        ul, ol {
            padding-left: 1.5rem;
        }
        ul { list-style-type: disc; }
        ol { list-style-type: decimal; }
        code {
            font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
            background-color: #f5f5f5;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }
        pre {
            background-color: #f5f5f5;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1.5rem;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        .step-card {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background-color: #f8fafc;
        }
        .optimization-card {
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
        }
        .warning-card {
            border-left: 4px solid #f59e0b;
            padding-left: 1rem;
            margin-bottom: 1.5rem;
            background-color: #fffbeb;
        }
        .prompt-card {
            border: 1px solid #ddd;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background-color: #f8f9fa;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5rem;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 0.75rem;
            text-align: left;
        }
        th {
            background-color: #f1f5f9;
            font-weight: 600;
        }
        .sequence-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #3b82f6;
            color: white;
            font-weight: 600;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-3xl font-bold text-center mb-8">Incident PIR RAG Application - Optimized MDP Plan</h1>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold border-b pb-2 mb-4">1. Introduction and Overview</h2>
            <p>
                This document provides an analysis of the high-level strategy for the Incident PIR RAG application, 
                optimizes the approach specifically for LangChain and LlamaIndex frameworks, and defines a 
                Minimal Deliverable Product (MDP) plan with sequential implementation steps. The plan focuses on 
                creating small, independently testable components that can be validated and committed to Git.
            </p>
            
            <h3 class="text-xl font-bold mt-6 mb-2">Project Objectives</h3>
            <p>
                The Incident PIR RAG application aims to:
            </p>
            <ul class="list-disc pl-8 mb-4">
                <li>Efficiently index and retrieve information from structured incident Post-Implementation Review (PIR) reports</li>
                <li>Leverage the HTML structure of these reports for improved retrieval precision</li>
                <li>Utilize a ReAct agent to process and act upon retrieved information</li>
                <li>Present information through a user-friendly Chainlit interface</li>
            </ul>
            
            <h3 class="text-xl font-bold mt-6 mb-2">Key Technical Components</h3>
            <ul class="list-disc pl-8 mb-4">
                <li>Confluence data loading and HTML parsing</li>
                <li>Custom embedding model integration (OpenAI-compatible API)</li>
                <li>Structure-aware document indexing</li>
                <li>Specialized retrieval strategies for PIR documents</li>
                <li>LangChain ReAct agent for orchestrating actions</li>
                <li>Custom reasoning model integration (OpenAI-compatible API)</li>
                <li>Chainlit UI for user interaction</li>
            </ul>
        </div>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold border-b pb-2 mb-4">2. Analysis of Current Strategy (Prompts 1-5)</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Prompt 1: Confluence Data Loading and Indexing</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold">Current Approach:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Custom HTML parsing with BeautifulSoup</li>
                            <li>Manual extraction of metadata from header sections</li>
                            <li>Complex custom function to process tables</li>
                            <li>Creation of separate documents for different sections</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold">Key Limitations:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Reinvents functionality already available in LlamaIndex</li>
                            <li>Requires maintenance of custom parsing logic</li>
                            <li>May not scale efficiently with many Confluence pages</li>
                            <li>Error handling for parsing edge cases is complex</li>
                        </ul>
                    </div>
                </div>
                <div class="optimization-card">
                    <h4 class="font-bold">Optimization Opportunities:</h4>
                    <ul class="list-disc pl-6">
                        <li>Use LlamaIndex's <code>ConfluenceReader</code> to directly load pages</li>
                        <li>Leverage LlamaIndex's <code>HTMLNodeParser</code> with custom tag configurations</li>
                        <li>Utilize LangChain's metadata extraction utilities</li>
                        <li>Implement batched processing for better performance with large page counts</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Prompt 2: Query Vectorization</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold">Current Approach:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Custom implementation for query vectorization</li>
                            <li>Direct API calls to custom embedding model</li>
                            <li>Manual handling of query preprocessing</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold">Key Limitations:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Duplicates functionality already in LangChain</li>
                            <li>Requires maintaining custom API integration code</li>
                            <li>Limited caching and optimization</li>
                        </ul>
                    </div>
                </div>
                <div class="optimization-card">
                    <h4 class="font-bold">Optimization Opportunities:</h4>
                    <ul class="list-disc pl-6">
                        <li>Use LangChain's <code>OpenAIEmbeddings</code> class with custom endpoint</li>
                        <li>Leverage built-in caching mechanisms</li>
                        <li>Utilize query transformation pipelines for preprocessing</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Prompt 3: Semantic Similarity Search</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold">Current Approach:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Multiple specialized retrievers (Parent Document, Multi-Vector, Table-Specific)</li>
                            <li>Complex retriever routing logic</li>
                            <li>Custom code for table cell extraction</li>
                            <li>Advanced ensemble retriever implementation</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold">Key Limitations:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Highly complex for initial implementation</li>
                            <li>Each retriever requires separate maintenance</li>
                            <li>May introduce overhead for simple queries</li>
                            <li>Difficult to test thoroughly</li>
                        </ul>
                    </div>
                </div>
                <div class="optimization-card">
                    <h4 class="font-bold">Optimization Opportunities:</h4>
                    <ul class="list-disc pl-6">
                        <li>Start with LlamaIndex's <code>VectorIndexRetriever</code> with metadata filtering</li>
                        <li>Use LangChain's <code>SelfQueryRetriever</code> for metadata-based filtering</li>
                        <li>Implement table-specific retrieval as a separate enhancement</li>
                        <li>Add more complex retrievers incrementally after core functionality works</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Prompt 4: ReAct Agent Configuration</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold">Current Approach:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Complex LLM router chain implementation</li>
                            <li>Multiple specialized router templates</li>
                            <li>Custom parsing of router outputs</li>
                            <li>Advanced ensemble fallback mechanisms</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold">Key Limitations:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Unnecessarily complex for initial implementation</li>
                            <li>Many potential failure points</li>
                            <li>Difficult to debug agent reasoning</li>
                            <li>Resource-intensive with multiple LLM calls</li>
                        </ul>
                    </div>
                </div>
                <div class="optimization-card">
                    <h4 class="font-bold">Optimization Opportunities:</h4>
                    <ul class="list-disc pl-6">
                        <li>Start with simpler LangChain <code>ReActChain</code> implementation</li>
                        <li>Define focused tools for core functionality only</li>
                        <li>Use agent executor with structured tool calling when available</li>
                        <li>Implement logging of agent steps for debugging</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Prompt 5: Reasoning LLM Integration</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-bold">Current Approach:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Custom implementation for handling non-streaming output</li>
                            <li>Complex reranking pipeline with multiple transformers</li>
                            <li>Custom document compressor implementations</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-bold">Key Limitations:</h4>
                        <ul class="list-disc pl-6 mb-4">
                            <li>Reinvents functionality in LangChain</li>
                            <li>Complex pipeline adds maintenance burden</li>
                            <li>Multiple transformations can impact performance</li>
                            <li>Custom logic for parsing model outputs</li>
                        </ul>
                    </div>
                </div>
                <div class="optimization-card">
                    <h4 class="font-bold">Optimization Opportunities:</h4>
                    <ul class="list-disc pl-6">
                        <li>Use LangChain's <code>ChatOpenAI</code> class with custom endpoint</li>
                        <li>Leverage built-in parsing of structured outputs</li>
                        <li>Implement simpler post-processing for thinking/reasoning sections</li>
                        <li>Add result caching for improved performance</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold border-b pb-2 mb-4">3. Optimized Approach for LangChain and LlamaIndex</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Framework-Specific Optimizations</h3>
                
                <h4 class="font-bold mt-4">LlamaIndex Optimizations</h4>
                <ul class="list-disc pl-6 mb-4">
                    <li><strong>Built-in Document Loading:</strong> Use <code>ConfluenceReader</code> to directly load PIR documents</li>
                    <li><strong>Structure-Aware Parsing:</strong> Configure <code>HTMLNodeParser</code> with PIR-specific tags</li>
                    <li><strong>Metadata Extraction:</strong> Leverage automatic metadata extraction with custom extractors</li>
                    <li><strong>Hierarchical Indexing:</strong> Use <code>DocumentSummaryIndex</code> to create summary nodes for sections</li>
                    <li><strong>Query Engine Customization:</strong> Configure query engines with PIR-specific retrievers</li>
                </ul>
                
                <h4 class="font-bold mt-4">LangChain Optimizations</h4>
                <ul class="list-disc pl-6 mb-4">
                    <li><strong>Embedding Integration:</strong> Use <code>OpenAIEmbeddings</code> with custom endpoints</li>
                    <li><strong>Metadata Filtering:</strong> Implement <code>SelfQueryRetriever</code> for efficient filtering</li>
                    <li><strong>ReAct Agent:</strong> Leverage <code>create_react_agent</code> with custom tools</li>
                    <li><strong>Structured Output:</strong> Use <code>StructuredOutputParser</code> for consistent reasoning format</li>
                    <li><strong>Memory Integration:</strong> Add conversation memory for multi-turn interactions</li>
                </ul>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Architecture Optimizations</h3>
                
                <div class="warning-card">
                    <h4 class="font-bold">Simplification Strategy:</h4>
                    <p>
                        The original architecture introduces too much complexity for an initial implementation. 
                        We'll simplify by focusing on a core set of components that leverage framework capabilities 
                        directly, then add advanced features incrementally.
                    </p>
                </div>
                
                <h4 class="font-bold mt-4">Revised Architecture Overview</h4>
                <ol class="list-decimal pl-6 mb-4">
                    <li>
                        <strong>Data Loading:</strong> LlamaIndex <code>ConfluenceReader</code> → HTML Documents
                    </li>
                    <li>
                        <strong>Document Processing:</strong> LlamaIndex <code>HTMLNodeParser</code> → Structured Nodes
                    </li>
                    <li>
                        <strong>Indexing:</strong> LlamaIndex <code>VectorStoreIndex</code> + Metadata → Searchable Index
                    </li>
                    <li>
                        <strong>Query Processing:</strong> LangChain <code>SelfQueryRetriever</code> → Filtered Results
                    </li>
                    <li>
                        <strong>Agent Orchestration:</strong> LangChain <code>ReActAgent</code> → Reasoning + Actions
                    </li>
                    <li>
                        <strong>UI Layer:</strong> Chainlit Components → Interactive Interface
                    </li>
                </ol>
                
                <div class="border rounded-lg p-4 bg-blue-50 mb-6">
                    <h4 class="font-bold">Key Advantages of Revised Architecture:</h4>
                    <ul class="list-disc pl-6">
                        <li>Maximizes use of built-in framework capabilities</li>
                        <li>Reduces custom code that requires maintenance</li>
                        <li>Creates clearer separation of responsibilities</li>
                        <li>Allows for incremental testing of each component</li>
                        <li>Provides extension points for future enhancements</li>
                    </ul>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-bold mb-2">Retrieval Strategy Simplification</h3>
                
                <p>
                    Instead of implementing multiple complex retrievers simultaneously, we'll focus on a 
                    progressive enhancement approach:
                </p>
                
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <h4 class="font-bold">Phase 1: Metadata-Aware Vector Retrieval</h4>
                    <ul class="list-disc pl-6">
                        <li>Index documents with rich metadata (section, table type, incident properties)</li>
                        <li>Use <code>SelfQueryRetriever</code> to filter by metadata before similarity search</li>
                        <li>Implement basic post-processing to improve result relevance</li>
                    </ul>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <h4 class="font-bold">Phase 2: Structure-Aware Retrieval</h4>
                    <ul class="list-disc pl-6">
                        <li>Add parent-child relationships between documents (section → paragraphs)</li>
                        <li>Implement table-specific retrieval for structured data</li>
                        <li>Add relevance scoring based on document structure</li>
                    </ul>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-4 mb-4">
                    <h4 class="font-bold">Phase 3: Advanced Retrieval Techniques</h4>
                    <ul class="list-disc pl-6">
                        <li>Implement hybrid retrieval (keyword + semantic)</li>
                        <li>Add query-specific reranking</li>
                        <li>Integrate multi-vector approaches for complex queries</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold border-b pb-2 mb-4">4. Minimal Deliverable Product (MDP) Plan</h2>
            
            <p class="mb-4">
                Based on the optimized approach, we'll define a Minimal Deliverable Product (MDP) that:
            </p>
            <ul class="list-disc pl-6 mb-6">
                <li>Delivers core functionality with minimal complexity</li>
                <li>Can be implemented and tested incrementally</li>
                <li>Leverages framework capabilities effectively</li>
                <li>Provides a foundation for future enhancements</li>
            </ul>
            
            <h3 class="text-xl font-bold mb-4">MDP Features</h3>
            
            <table class="w-full border-collapse">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="border px-4 py-2">Feature</th>
                        <th class="border px-4 py-2">Description</th>
                        <th class="border px-4 py-2">Priority</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border px-4 py-2">Confluence Loading</td>
                        <td class="border px-4 py-2">Load PIR documents from Confluence using built-in connectors</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">HTML Parsing</td>
                        <td class="border px-4 py-2">Parse HTML structure with metadata extraction</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Basic Vectorization</td>
                        <td class="border px-4 py-2">Create embeddings using custom model with standard interfaces</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Metadata Filtering</td>
                        <td class="border px-4 py-2">Filter results by incident properties (ID, date, etc.)</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Simple ReAct Agent</td>
                        <td class="border px-4 py-2">Basic agent with retrieval and email tools</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Reasoning Integration</td>
                        <td class="border px-4 py-2">Connect custom reasoning model with thinking extraction</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Basic Chainlit UI</td>
                        <td class="border px-4 py-2">Simple interface with collapsible reasoning blocks</td>
                        <td class="border px-4 py-2">Core</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Table-Specific Retrieval</td>
                        <td class="border px-4 py-2">Enhanced retrieval for table data in PIR documents</td>
                        <td class="border px-4 py-2">Enhancement</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Section-Aware Retrieval</td>
                        <td class="border px-4 py-2">Improved retrieval based on document sections</td>
                        <td class="border px-4 py-2">Enhancement</td>
                    </tr>
                    <tr>
                        <td class="border px-4 py-2">Advanced Agent Capabilities</td>
                        <td class="border px-4 py-2">Multi-tool orchestration with complex reasoning</td>
                        <td class="border px-4 py-2">Enhancement</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 class="text-xl font-bold mt-6 mb-4">MDP Exclusions</h3>
            <p>The following features are explicitly excluded from the MDP:</p>
            <ul class="list-disc pl-6 mb-4">
                <li>Multiple specialized retrievers working in parallel</li>
                <li>Complex router chain implementation</li>
                <li>Advanced reranking pipelines</li>
                <li>Real email sending functionality (stub only)</li>
                <li>Complex document compression techniques</li>
            </ul>
        </div>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold border-b pb-2 mb-4">5. Implementation Steps with Sequential Prompts</h2>
            
            <p class="mb-4">
                The following sequential steps provide a clear path to implement the MDP. Each step 
                produces independently testable code suitable for Git commits.
            </p>
            
            <h3 class="text-xl font-bold mb-4">Step 1: Project Setup and Dependencies</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">1</span> Project Structure Setup</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a project structure for the Incident PIR RAG application using Python. Include:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>A main <code>app.py</code> file for the Chainlit application</li>
                        <li>A <code>requirements.txt</code> file with necessary dependencies</li>
                        <li>A <code>config.py</code> file for configuration settings</li>
                        <li>Modules for data loading, indexing, retrieval, and agent components</li>
                        <li>A basic README.md with setup instructions</li>
                    </ol>
                    Dependencies should include LangChain, LlamaIndex, Chainlit, and other essential packages. 
                    Follow best practices for Python project structure, with clear separation of concerns.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">2</span> Configuration Setup</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a <code>config.py</code> file with configuration settings for the Incident PIR RAG application.
                    Include configurations for:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Confluence API connection (URL, username, API key)</li>
                        <li>Custom embedding model endpoint (URL, API key, dimensions)</li>
                        <li>Custom reasoning model endpoint (URL, API key, parameters)</li>
                        <li>Vector store settings (type, persistence path)</li>
                        <li>Retrieval parameters (top_k, similarity threshold)</li>
                        <li>Logging configuration</li>
                    </ol>
                    Use environment variables for sensitive information with secure defaults. 
                    Include type hints and documentation for each configuration option.
                </div>
            </div>
            
            <h3 class="text-xl font-bold mt-6 mb-4">Step 2: Data Loading and Processing</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">3</span> Confluence Connector</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>confluence_loader.py</code> for loading Incident PIR pages from Confluence.
                    Use LlamaIndex's <code>ConfluenceReader</code> to:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Connect to Confluence using credentials from config</li>
                        <li>Load pages under a specified space key and parent page ID</li>
                        <li>Implement batched loading for performance with many pages</li>
                        <li>Add error handling and logging</li>
                        <li>Include a simple test function that loads a sample page</li>
                    </ol>
                    The module should have a main function <code>load_confluence_documents</code> that returns 
                    a list of LlamaIndex Document objects. Include documentation and type hints.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">4</span> HTML Node Parser</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>html_parser.py</code> for parsing Incident PIR HTML documents into structured nodes.
                    Use LlamaIndex's <code>HTMLNodeParser</code> to:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Configure parsing for PIR-specific HTML structure (headings, tables)</li>
                        <li>Extract metadata from document headers and reference tables</li>
                        <li>Create separate nodes for different sections</li>
                        <li>Handle table structures with special processing</li>
                        <li>Maintain relationships between parent and child nodes</li>
                    </ol>
                    The module should have a main function <code>process_documents</code> that converts 
                    LlamaIndex Documents into structured Nodes with rich metadata. Include a test function 
                    with a sample HTML document.
                </div>
            </div>
            
            <h3 class="text-xl font-bold mt-6 mb-4">Step 3: Embedding and Indexing</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">5</span> Custom Embedding Model Integration</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>embeddings.py</code> to integrate the custom embedding model.
                    Use LangChain's <code>OpenAIEmbeddings</code> class to:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Configure the custom embedding endpoint (from config)</li>
                        <li>Implement embedding generation for both documents and queries</li>
                        <li>Add caching for improved performance</li>
                        <li>Include error handling with retries for API failures</li>
                        <li>Create a simple test function to verify embedding generation</li>
                    </ol>
                    The module should provide a function <code>get_embedding_model</code> that returns 
                    a configured LangChain embedding model instance ready for use with retrieval components.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">6</span> Vector Store Integration</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>vector_store.py</code> for document indexing and storage.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Integration with Chroma vector store (configurable)</li>
                        <li>Functions to create and load indexes</li>
                        <li>Metadata handling for structured PIR data</li>
                        <li>Persistence mechanisms for the vector store</li>
                        <li>A simple query interface for testing</li>
                    </ol>
                    The module should include functions <code>create_index</code> and <code>load_index</code>
                    that handle the creation and loading of the vector store with the parsed nodes.
                    Include documentation and type hints.
                </div>
            </div>
            
            <h3 class="text-xl font-bold mt-6 mb-4">Step 4: Retrieval System</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">7</span> Metadata-Aware Retriever</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>retriever.py</code> for implementing the metadata-aware retrieval system.
                    Use LangChain's <code>SelfQueryRetriever</code> to:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Define metadata schema for PIR document properties</li>
                        <li>Implement filtering by incident properties (ID, date, team, etc.)</li>
                        <li>Create a basic hybrid retrieval method (metadata + semantic)</li>
                        <li>Add simple post-processing to improve result quality</li>
                        <li>Include functions to test retrieval with sample queries</li>
                    </ol>
                    The module should provide a function <code>get_retriever</code> that creates a configured
                    retriever instance ready for use with the agent. Include documentation and type hints.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">8</span> Retrieval Tool</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>retrieval_tool.py</code> to implement a LangChain tool for document retrieval.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>A LangChain Tool that wraps the retriever</li>
                        <li>Proper input/output schema definitions</li>
                        <li>Formatting of retrieved documents for the agent</li>
                        <li>Logging of retrieval requests and results</li>
                        <li>A simple test function to verify tool functionality</li>
                    </ol>
                    The module should provide a function <code>create_retrieval_tool</code> that returns
                    a configured LangChain Tool instance ready for use with the ReAct agent.
                    Include documentation and type hints.
                </div>
            </div>
            
            <h3 class="text-xl font-bold mt-6 mb-4">Step 5: Agent Implementation</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">9</span> Custom Reasoning Model Integration</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>reasoning_model.py</code> to integrate the custom reasoning model.
                    Use LangChain's <code>ChatOpenAI</code> class to:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Configure the custom model endpoint (from config)</li>
                        <li>Implement support for non-streaming output</li>
                        <li>Extract thinking/reasoning from model responses</li>
                        <li>Add error handling with appropriate fallbacks</li>
                        <li>Include a simple test function</li>
                    </ol>
                    The module should provide a function <code>get_reasoning_model</code> that returns
                    a configured LangChain chat model instance ready for use with the agent.
                    Include documentation and type hints.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">10</span> Email Tool Implementation</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>email_tool.py</code> to implement a stub email tool for the agent.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>A LangChain Tool for sending emails (stub implementation)</li>
                        <li>Integration with Chainlit's ask user functionality for approval</li>
                        <li>Proper input schema with recipient, subject, and body</li>
                        <li>Detailed logging of email "sending" actions</li>
                        <li>A simple test function</li>
                    </ol>
                    The module should provide a function <code>create_email_tool</code> that returns
                    a configured LangChain Tool instance ready for use with the ReAct agent.
                    Include documentation and type hints.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">11</span> ReAct Agent Configuration</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>agent.py</code> to implement and configure the ReAct agent.
                    Use LangChain's <code>create_react_agent</code> to:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Configure the agent with the reasoning model</li>
                        <li>Add the retrieval and email tools</li>
                        <li>Implement a custom prompt template for PIR-specific tasks</li>
                        <li>Add logging of agent steps and decisions</li>
                        <li>Include a simple function to run the agent with a test query</li>
                    </ol>
                    The module should provide a function <code>create_agent</code> that returns
                    a configured LangChain agent ready for use with Chainlit.
                    Include documentation and type hints.
                </div>
            </div>
            
            <h3 class="text-xl font-bold mt-6 mb-4">Step 6: Chainlit UI Integration</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">12</span> Chainlit UI Components</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>ui_components.py</code> with Chainlit UI components.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Function to create collapsible reasoning/thinking blocks</li>
                        <li>Helper for displaying retrieved documents with source information</li>
                        <li>User approval dialog for email actions</li>
                        <li>Progress indicators for long-running operations</li>
                        <li>Error message formatting</li>
                    </ol>
                    The module should provide helper functions that simplify UI interactions
                    in the main Chainlit app. Include documentation and type hints.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">13</span> Main Chainlit Application</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create the main <code>app.py</code> file for the Chainlit application.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Chainlit setup with appropriate event handlers</li>
                        <li>Index initialization on startup</li>
                        <li>Message handling with agent invocation</li>
                        <li>Display of agent responses with collapsible reasoning</li>
                        <li>SVG rendering support for any diagrams in responses</li>
                        <li>Error handling and user feedback</li>
                    </ol>
                    The file should define a complete Chainlit application that integrates all
                    components developed in previous steps. Include documentation and comments.
                </div>
            </div>
            
            <h3 class="text-xl font-bold mt-6 mb-4">Step 7: Testing and Documentation</h3>
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">14</span> Unit and Integration Tests</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a <code>tests</code> directory with unit and integration tests.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Unit tests for each module using pytest</li>
                        <li>Integration tests for key workflows</li>
                        <li>Mock data and fixtures for testing</li>
                        <li>Test for the end-to-end workflow</li>
                        <li>Performance benchmarks for retrieval operations</li>
                    </ol>
                    The tests should verify the functionality of each component individually
                    and ensure they work together correctly. Include documentation on how to run the tests.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">15</span> Sample PIR Data Generator</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Create a module <code>generate_sample_data.py</code> for generating sample PIR data.
                    Implement:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Function to create sample PIR HTML documents</li>
                        <li>Various incident types and severities</li>
                        <li>Realistic data patterns for testing</li>
                        <li>Command-line interface for generating data</li>
                        <li>Documentation of the data format</li>
                    </ol>
                    The module should make it easy to generate test data for development and testing
                    without requiring access to real Confluence pages. Include documentation and type hints.
                </div>
            </div>
            
            <div class="prompt-card">
                <h4 class="font-bold flex items-center"><span class="sequence-number">16</span> README and Documentation</h4>
                <p class="text-sm text-gray-600 mb-2">Prompt for AI Code Generator:</p>
                <div class="bg-gray-100 p-3 rounded-md text-sm">
                    Update the project documentation with comprehensive information.
                    Include:
                    <ol class="list-decimal pl-6 mb-2">
                        <li>Detailed README.md with setup and usage instructions</li>
                        <li>Configuration guide with environment variable documentation</li>
                        <li>API documentation for key modules</li>
                        <li>Usage examples and screenshots</li>
                        <li>Troubleshooting guide</li>
                        <li>Future enhancement roadmap</li>
                    </ol>
                    The documentation should make it easy for new developers to understand, setup,
                    and extend the application. Include information on the architecture and design decisions.
                </div>
            </div>
        </div>
        
        <div class="mb-8">
            <h2 class="text-2xl font-bold border-b pb-2 mb-4">6. Conclusion</h2>
            
            <p class="mb-4">
                This document has provided:
            </p>
            <ul class="list-disc pl-6 mb-4">
                <li>A thorough analysis of the original high-level strategy</li>
                <li>Optimization recommendations for LangChain and LlamaIndex integration</li>
                <li>A simplified architectural approach for the MDP</li>
                <li>A detailed, step-by-step implementation plan</li>
                <li>Sequential AI prompts for incremental development</li>
            </ul>
            
            <p class="mb-4">
                By following this implementation plan, you can build the Incident PIR RAG application
                incrementally, with each step producing testable code suitable for validation and Git commits.
                The plan prioritizes core functionality while providing a clear path for future enhancements.
            </p>
            
            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                <h4 class="font-bold">Key Success Factors:</h4>
                <ul class="list-disc pl-6">
                    <li>Focus on incremental delivery of working components</li>
                    <li>Leverage framework capabilities to reduce custom code</li>
                    <li>Test each component independently before integration</li>
                    <li>Start with simpler retrieval approaches before adding complexity</li>
                    <li>Document design decisions and implementation details</li>
                </ul>
            </div>
            
            <p>
                With this optimized approach, the Incident PIR RAG application can be successfully
                implemented as a robust, maintainable system that effectively retrieves and processes
                information from structured incident reports.
            </p>
        </div>
    </div>
</body>
</html>