from llama_index.core import VectorStoreIndex, StorageContext, load_index_from_storage
from src.embedding import get_embedding_model
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core.schema import TextNode
from llama_index.core.base.embeddings.base import BaseEmbedding
from chromadb.config import Settings
import chromadb
import os
import logging
from typing import Optional, List

logger = logging.getLogger(__name__)

def get_vector_index(
    nodes: Optional[List[TextNode]],
    persist_dir: str = "./storage/pir_chroma_index",
    collection_name: str = "pir_documents",
) -> VectorStoreIndex:
    """
    Builds or loads a persistent VectorStoreIndex using ChromaDB.

    Args:
        nodes (Optional[List[TextNode]]): List of TextNodes to build the index if it doesn't exist.
        persist_dir (str): Directory to persist the index. Defaults to "./storage/pir_chroma_index".
        collection_name (str): Name of the ChromaDB collection. Defaults to "pir_documents".

    Returns:
        VectorStoreIndex: The loaded or built index.

    Raises:
        ValueError: If the index cannot be loaded and no nodes are provided to build.
    """
    # Ensure the persistence directory exists
    persist_dir = os.path.abspath(persist_dir)
    os.makedirs(persist_dir, exist_ok=True)
    logger.info(f"Initializing Chroma DB at {persist_dir}")

    # Initialize ChromaDB client and collection
    client = chromadb.PersistentClient(path=persist_dir, settings=Settings(anonymized_telemetry=False))
    logger.info(f"Getting/Creating Chroma collection: {collection_name}")
    chroma_collection = client.get_or_create_collection(collection_name)

    # Create ChromaVectorStore
    vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

    if nodes:
        # Build Scenario: Explicitly create and populate docstore, then build index and persist context.
        logger.info("Nodes provided. Building new index...")
        from llama_index.core.storage.docstore.simple_docstore import SimpleDocumentStore

        # 1. Initialize components
        docstore = SimpleDocumentStore()
        # 2. Add nodes explicitly to the docstore
        docstore.add_documents(nodes)
        logger.info(f"Added {len(nodes)} nodes to SimpleDocumentStore.")

        # 3. Create StorageContext with explicit components (vector_store already created)
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            docstore=docstore
            # index_store will be created implicitly or use SimpleIndexStore() if needed
        )

        # 4. Build the index using the populated storage_context
        #    Using VectorStoreIndex.from_documents might be cleaner if nodes are already in docstore/vectorstore
        #    Let's stick with the original way first, it should handle nodes already in context.
        embed_model = get_embedding_model()
        index = VectorStoreIndex(nodes, storage_context=storage_context, embed_model=embed_model)
        logger.info(f"Finished building index. Index docstore has {len(index.docstore.docs)} nodes.")

        # 5. Persist the *entire* context (which now explicitly includes the populated docstore)
        logger.info(f"Persisting index components to {persist_dir}")
        storage_context.persist(persist_dir=persist_dir) # Persist the context directly
        logger.info(f"Successfully persisted new index components.")
    else:
        # Load Scenario: Create context with persist_dir to load all components.
        logger.info(f"No nodes provided. Attempting to load index from {persist_dir}")
        try:
            # Provide persist_dir here to load docstore, index_store etc. alongside the vector_store
            storage_context = StorageContext.from_defaults(vector_store=vector_store, persist_dir=persist_dir)
            index = load_index_from_storage(storage_context=storage_context)
            # Verify loading worked by checking docstore
            doc_count = len(index.docstore.docs)
            logger.info(f"Successfully loaded index with {doc_count} nodes from {persist_dir}")
        except Exception as e:
            # Catch potential errors during loading (e.g., missing files, version mismatch)
            logger.error(f"Failed to load index from {persist_dir}. Error: {e}")
            raise ValueError(f"Cannot load index from {persist_dir}. Ensure it was built correctly.") from e

    return index
