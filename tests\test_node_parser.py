import pytest
from bs4 import BeautifulSoup
from unittest.mock import MagicMock
from llama_index import Document, TextNode

from src.node_parser import PIRNodeParser

SAMPLE_HTML = """
<html>
<body>
<div id="id">PIR-2024-001</div>
<div id="date">2024-04-07</div>
<div id="team">Team Awesome</div>
<div id="subject">Subject: Important Issue</div>
<table class="details">
    <tr><th>Severity</th><td>High</td></tr>
    <tr><th>Apps</th><td>App1, App2</td></tr>
    <tr><th>Regions</th><td>RegionA, RegionB, RegionC</td></tr>
</table>
<div id="labels">label1, label2, label3</div>
<table>
    <tr><th>Header 1</th><th>Header 2</th></tr>
    <tr><td>Row 1, Col 1</td><td>Row 1, Col 2</td></tr>
    <tr><td>Row 2, Col 1</td><td>Row 2, Col 2</td></tr>
</table>
</body>
</html>
"""

def test_extract_base_metadata():
    soup = BeautifulSoup(SAMPLE_HTML, 'html.parser')
    metadata = PIRNodeParser._extract_base_metadata(soup)
    assert metadata["id"] == "PIR-2024-001"
    assert metadata["date"] == "2024-04-07"
    assert metadata["team"] == "Team Awesome"
    assert metadata["subject"] == "Subject: Important Issue"
    assert metadata["severity"] == "High"
    assert metadata["apps"] == ["App1", "App2"]
    assert metadata["regions"] == ["RegionA", "RegionB", "RegionC"]
    assert metadata["labels"] == ["label1", "label2", "label3"]

    # Test missing elements
    missing_soup = BeautifulSoup("<html><body></body></html>", 'html.parser')
    missing_metadata = PIRNodeParser._extract_base_metadata(missing_soup)
    assert missing_metadata["id"] is None
    assert missing_metadata["date"] is None
    assert missing_metadata["team"] is None
    assert missing_metadata["subject"] is None
    assert missing_metadata["severity"] is None
    assert missing_metadata["apps"] == []
    assert missing_metadata["regions"] == []
    assert missing_metadata["labels"] == []


def test_parse_table_to_markdown():
    html_table = """
    <table>
        <tr><th>Header 1</th><th>Header 2</th></tr>
        <tr><td>Row 1, Col 1</td><td>Row 1, Col 2</td></tr>
        <tr><td>Row 2, Col 1</td><td>Row 2, Col 2</td></tr>
    </table>
    """
    soup = BeautifulSoup(html_table, 'html.parser')
    markdown = PIRNodeParser._parse_table_to_markdown(soup.find('table'))
    expected_markdown = """
| Header 1 | Header 2 |
|---|---|
| Row 1, Col 1 | Row 1, Col 2 |
| Row 2, Col 1 | Row 2, Col 2 |
"""
    assert markdown.strip() == expected_markdown.strip()


def test_get_nodes_from_documents():
    mock_document = Document(text=SAMPLE_HTML)
    node_parser = PIRNodeParser()
    nodes = node_parser.get_nodes_from_documents([mock_document])
    assert len(nodes) == 4  # Assuming 3 text sections + 1 table

    # Check base metadata propagation
    assert nodes[0].metadata["id"] == "PIR-2024-001"

    # Check table content
    table_node = nodes[2]
    assert "Header 1" in table_node.get_content()
    assert "Row 1, Col 1" in table_node.get_content()
    assert table_node.metadata["section_title"] == "details"
