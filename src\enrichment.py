from typing import List
# from llama_index.core.extractors import MetadataExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.schema import TextNode
import logging

logger = logging.getLogger(__name__)

def enrich_nodes_with_llm(nodes: List[TextNode], llm: LLM, extractors: List[any]) -> List[TextNode]:
    """
    Enriches nodes with metadata extracted by LLM-powered extractors.

    Args:
        nodes: A list of TextNode objects to enrich.
        llm: An initialized LlamaIndex LLM wrapper.
        extractors: A list of configured MetadataExtractor instances.

    Returns:
        The (mutated) list of nodes, with added metadata.
    """
    for extractor in extractors:
        try:
            nodes = extractor.process_nodes(nodes)
            logger.info(f"Successfully applied extractor: {extractor.__class__.__name__}")
        except Exception as e:
            logger.error(f"Error applying extractor {extractor.__class__.__name__}: {e}")

    return nodes