<h1>YYYYMMDD - IN000000 - Team Name - Incident Subject</h1>
<table data-table-width="760" data-layout="default" ac:local-id="6038444f-4087-4811-ab79-b311c2764d08">
	<tbody>
		<tr>
			<th>
				<p>
					<strong>Reference</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Details</strong>
				</p>
			</th>
		</tr>
		<tr>
			<td>
				<p>Impacted Application(s)</p>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Impacted Regions</p>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Incident Number</p>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Incident Ticket</p>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Impact</p>
			</td>
			<td>
				<p>High</p>
			</td>
		</tr>
		<tr>
			<td>
				<p>Problem Ticket</p>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Problem Owner</p>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Post Incident Review Status</p>
			</td>
			<td>
				<p>Done</p>
			</td>
		</tr>
	</tbody>
</table>
<h3>Incident Summary (high level description of the incident)</h3>
<p>&lt;description of incident&gt;</p>
<h3>Incident Timeline</h3>
<p>Date: YYYYMMDD</p>
<p>Timezone: GMT / HKT / IST</p>
<table data-table-width="760" data-layout="default" ac:local-id="117792df-3cf5-46a3-9df4-4a95f8178174">
	<colgroup>
		<col style="width: 397.0px;"/>
		<col style="width: 109.0px;"/>
		<col style="width: 253.0px;"/>
	</colgroup>
	<tbody>
		<tr>
			<th>
				<p>
					<strong>Incident Detail</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Timeline</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Details</strong>
				</p>
			</th>
		</tr>
		<tr>
			<td>
				<p>Start TIme</p>
				<p>&lt;Time that Incident Started&gt;</p>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Detected Time </p>
				<p>&lt;Time that issue was detected, state whether this was via alerting, end user&gt;</p>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Fixed Time</p>
				<p>&lt;Time the issue was fixed or workaround applied&gt;</p>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
		</tr>
		<tr>
			<td>
				<p>Fully Recovery Time </p>
				<p>&lt;Time the issue was fully recovered&gt; </p>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
		</tr>
	</tbody>
</table>
<h3>Business Impact Summary</h3>
<table data-table-width="760" data-layout="default" ac:local-id="330c59a8-0776-43ca-80fa-3bad008e807b">
	<tbody>
		<tr>
			<th>
				<p>
					<strong>Actual Business Impact Summary</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Service Outage</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>If &ldquo;Yes&rdquo;, Full or Degraded Outage</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>If &ldquo;Yes&rdquo;, added in SOW?</strong>
				</p>
			</th>
		</tr>
		<tr>
			<td>
				<ul>
					<li>
						<p>In bullet points, including user, operation, system service, SLA</p>
					</li>
				</ul>
			</td>
			<td>
				<p>Yes / No</p>
			</td>
			<td>
				<p>Full / Degrade</p>
			</td>
			<td>
				<p>Yes / No</p>
			</td>
		</tr>
	</tbody>
</table>
<h3>Was it Change Related?</h3>
<table data-table-width="760" data-layout="default" ac:local-id="c0a5527d-22c8-4087-aa41-a82f912b23e8">
	<tbody>
		<tr>
			<th>
				<p>
					<strong>Change Related?</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>If &ldquo;Yes&rdquo;, what&rsquo;s the Change  number</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>If &ldquo;Yes&rdquo;, what&rsquo;s Change Sub Type</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>If &ldquo;Yes&rdquo;, is it linked with Incident in SOW</strong>
				</p>
			</th>
		</tr>
		<tr>
			<td>
				<p>Yes / No</p>
			</td>
			<td>
				<p>CHG0000000</p>
			</td>
			<td>
				<p>DevOps / Normal</p>
			</td>
			<td>
				<p>Yes / No</p>
			</td>
		</tr>
	</tbody>
</table>
<h3>Root Cause</h3>
<table data-table-width="760" data-layout="default" ac:local-id="55c44fae-5a70-4e85-89f8-50e8354da5cc">
	<tbody>
		<tr>
			<th>
				<p>
					<strong>Root Cause Analysis</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Root Cause Analysis Detail</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Status (Completed or not)</strong>
				</p>
			</th>
		</tr>
		<tr>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
		</tr>
	</tbody>
</table>
<h3>Actions Arising</h3>
<p>(Has an interim work-around been established? Actions as improvement, as well as how to address any lessons learned)</p>
<table data-table-width="760" data-layout="default" ac:local-id="15ea80c9-3fc2-491c-b1fc-f80555f021c6">
	<tbody>
		<tr>
			<th>
				<p>
					<strong>Root Cause Fix</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Action</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Status (Completed or not)</strong>
				</p>
			</th>
			<th>
				<p>
					<strong>Jira (optional)</strong>
				</p>
			</th>
		</tr>
		<tr>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
			<td>
				<p/>
			</td>
		</tr>
	</tbody>
</table>
<h3>Labels</h3>
<p/>
