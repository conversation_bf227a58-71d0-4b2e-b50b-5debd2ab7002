# src/node_parser.py
import re
import logging
from typing import List, Dict, Any, Optional, Tuple

from llama_index.core.node_parser.interface import NodeParser
from llama_index.core.schema import Document, TextNode
from llama_index.core.node_parser import TokenTextSplitter # Keep for chunking section content

logger = logging.getLogger(__name__)

class PIRNodeParser(NodeParser):
    """
    Parses Markdown PIR documents based on a specific template.
    Extracts metadata from the first table and date from the Timeline section.
    Creates TextNodes per specified section header.
    """

    def get_nodes_from_documents(
        self, documents: List[Document], show_progress: bool = False, **kwargs
    ) -> List[TextNode]:
        """
        Processes a list of Markdown documents and extracts TextNodes for each section.
        """
        all_nodes: List[TextNode] = []
        if not documents:
            logger.warning("get_nodes_from_documents received an empty list of documents.")
            return all_nodes

        for i, doc in enumerate(documents):
            markdown_text = doc.text
            # Use file_name from metadata if available, otherwise doc_id
            source_info = doc.metadata.get('file_name') or doc.doc_id or f"doc_{i+1}"

            if not markdown_text or not markdown_text.strip():
                logger.warning(f"Document {source_info} has empty or whitespace-only content. Skipping.")
                continue

            logger.info(f"Processing document: {source_info}")

            # 1. Extract metadata from the first table
            base_metadata = self._extract_metadata_from_first_table(markdown_text, source_info)
            if not base_metadata or not base_metadata.get('incident_id'):
                logger.warning(f"Could not extract base metadata or incident_id from first table in {source_info}. Proceeding with minimal metadata.")
                # Ensure base_metadata is a dict even if extraction failed partially
                if base_metadata is None:
                     base_metadata = {}
                base_metadata.setdefault('source_info', source_info) # Ensure source_info is present

            # Always add doc_id
            base_metadata["doc_id"] = doc.doc_id or f"doc_{i+1}"

            # 2. Split content by headers and extract date from Timeline
            sections, extracted_date = self._split_markdown_by_pir_headers(markdown_text)

            if not sections:
                 logger.warning(f"No sections extracted based on ### headers in {source_info}. Check document structure.")
                 # Optionally, create a single node for the whole doc if no sections found
                 # continue # Or skip if sections are mandatory

            # 3. Add extracted date to metadata
            if extracted_date:
                base_metadata['date'] = extracted_date
                logger.info(f"Extracted date '{extracted_date}' from Timeline section in {source_info}")
            else:
                logger.warning(f"Could not extract date from Timeline section in {source_info}")

            # 4. Create nodes for each section
            section_counter: Dict[str, int] = {}
            for section_title, section_markdown in sections.items():
                if not section_markdown or not section_markdown.strip():
                    logger.debug(f"Skipping empty section '{section_title}' in {source_info}")
                    continue

                logger.debug(f"Processing section: '{section_title}'")

                # Format specific sections if they contain tables
                formatted_text = section_markdown
                if section_title in ["Timeline", "Actions Arising"]:
                    # Check if the section looks like a table before formatting
                    if section_markdown.strip().startswith('|') and '\n|' in section_markdown:
                         formatted_text = self._format_markdown_table_section(section_markdown)
                         logger.debug(f"Applied table formatting to section '{section_title}'")
                    else:
                         logger.debug(f"Section '{section_title}' does not appear to be a table, skipping table formatting.")


                # --- Metadata and Node ID ---
                node_metadata = base_metadata.copy()
                node_metadata["section_title"] = section_title
                # Ensure incident_id exists for node naming, fallback to doc_id
                incident_id_for_node = base_metadata.get('incident_id', base_metadata.get('doc_id', 'unknown_doc'))
                section_name_safe = re.sub(r'\W+', '_', section_title.lower())
                section_counter[section_name_safe] = section_counter.get(section_name_safe, 0) + 1
                current_section_count = section_counter[section_name_safe]
                node_base_id = f"{incident_id_for_node}_section_{section_name_safe}_{current_section_count}"
                # ---------------------------

                # --- Text Splitting (Optional but recommended for large sections) ---
                try:
                    # Adjust chunk size/overlap as needed
                    text_splitter = TokenTextSplitter(chunk_size=1024, chunk_overlap=200) # Larger chunk for Markdown
                    text_chunks = text_splitter.split_text(formatted_text)
                    logger.debug(f"Split section '{section_title}' into {len(text_chunks)} chunks.")
                except Exception as e:
                     logger.error(f"Failed to split text for section '{section_title}' (ID: {node_base_id}): {e}", exc_info=True)
                     text_chunks = [formatted_text] # Fallback to single chunk
                     logger.warning("Using unsplit text for the node due to splitter error.")
                # --------------------

                # --- Node Creation ---
                for i, chunk in enumerate(text_chunks):
                    if chunk and chunk.strip():
                        chunk_node_id = f"{node_base_id}_chunk_{i+1}"
                        # Ensure metadata is JSON serializable (convert lists/dicts if needed, though current ones should be fine)
                        serializable_metadata = {
                            k: v if isinstance(v, (str, int, float, bool, type(None))) else str(v)
                            for k, v in node_metadata.items()
                        }
                        try:
                            node = TextNode(
                                text=chunk.strip(),
                                metadata=serializable_metadata,
                                id_=chunk_node_id,
                                excluded_embed_metadata_keys=["source_info", "doc_id"], # Example: Exclude from embedding
                                excluded_llm_metadata_keys=["source_info", "doc_id", "incident_id", "problem_ticket"] # Example: Exclude from LLM prompt
                            )
                            all_nodes.append(node)
                            logger.debug(f"Created node: {chunk_node_id} for section '{section_title}'")
                        except Exception as node_error:
                            logger.error(f"Failed to create TextNode for chunk {chunk_node_id}: {node_error}", exc_info=True)
                            logger.error(f"Problematic metadata: {serializable_metadata}")
                    else:
                         logger.debug(f"Skipping empty or whitespace chunk {i+1} for node base ID {node_base_id}")
                # -------------------

        logger.info(f"Finished processing. Total nodes extracted: {len(all_nodes)}")
        return all_nodes

    def _extract_metadata_from_first_table(
        self, markdown_text: str, source_info: str
    ) -> Dict[str, Any]:
        """
        Extracts metadata key-value pairs from the first Markdown table found.
        Also extracts 'Labels' if present below a 'Labels' header.
        """
        metadata: Dict[str, Any] = {"source_info": source_info}
        logger.debug(f"Starting metadata extraction from first table for: {source_info}")

        # Regex to find the first block that looks like a Markdown table
        # It looks for at least one header row (|---|---|) and one data row (| val | val |)
        # Regex to find the first block that looks like a Markdown table.
        # Focuses on the separator line followed by at least one data row.
        # Allows for optional/flexible header row before the separator.
        table_match = re.search(
            # Optional header row (might not start/end with | or even exist)
            r"(?:^[^\n]*\|[^\n]*\n)?" # Optional non-capturing group for any line with at least one pipe
            # Mandatory Separator row (flexible format)
            r"^\s*\|?[-:|\s]+\|[-:|\s]+\|?\s*?\n"
            # At least one data row starting with |
            r"(?:^\s*\|.*\|.*\|\s*?\n)+",
            markdown_text,
            re.MULTILINE
        )

        if not table_match:
            logger.warning(f"No Markdown table found matching the expected structure in {source_info}")
        else:
            table_block = table_match.group(0)
            logger.debug(f"Found potential metadata table block:\n{table_block}")
            lines = table_block.strip().split('\n')
            rows_parsed = 0
            # Skip header and separator lines, parse data rows
            for line in lines[2:]: # Start from the first data row
                match = re.match(r"^\s*\|\s*(.*?)\s*\|\s*(.*?)\s*\|", line)
                if match:
                    key_text = match.group(1).strip().lower()
                    value_text = match.group(2).strip()

                    # Map keys to metadata fields
                    if not key_text or not value_text: # Skip rows with empty key or value
                        continue

                    rows_parsed += 1
                    if 'impacted application' in key_text:
                        metadata['impacted_applications'] = [app.strip() for app in value_text.split(';') if app.strip()]
                    elif 'impacted region' in key_text:
                        metadata['impacted_regions'] = [reg.strip() for reg in value_text.split(';') if reg.strip()]
                    elif 'incident number' in key_text or 'incident ticket' in key_text:
                        if 'incident_id' not in metadata: # Take the first one found
                             # Basic normalization: ensure INC prefix if missing and looks like a number
                             norm_id = value_text.upper()
                             if norm_id.isdigit():
                                 norm_id = f"INC{norm_id}"
                             elif not norm_id.startswith("INC"):
                                 # Attempt to find number part if prefix is different
                                 id_num_match = re.search(r'\d{8,}', norm_id)
                                 if id_num_match:
                                     norm_id = f"INC{id_num_match.group(0)}"
                                 # else keep original if format is unexpected
                             metadata['incident_id'] = norm_id
                    elif 'impact' == key_text: # Exact match preferred
                        metadata['severity'] = value_text.capitalize()
                    elif 'problem owner' in key_text:
                        metadata['problem_owner'] = value_text
                    elif 'problem ticket' in key_text:
                        metadata['problem_ticket'] = value_text.upper() # Normalize problem ticket too
                    elif 'post incident review status' in key_text:
                        metadata['pir_status'] = value_text
                    # Add more elif clauses here for other potential keys from the table
                    # Example:
                    # elif 'date' in key_text: # If date is in the table too
                    #     metadata['table_date'] = value_text # Store separately or prioritize

            logger.debug(f"Parsed {rows_parsed} rows from the first table in {source_info}.")
            if rows_parsed == 0:
                 logger.warning(f"Found a table structure, but couldn't parse key-value pairs from rows in {source_info}")

        # Extract Labels separately (assuming they are under a specific header, not necessarily in the table)
        # Look for "### Labels" (case-insensitive) followed by content on the next lines
        # This regex captures the text block after "### Labels" until the next header or end of doc
        labels_match = re.search(
            r"^\s*#{1,3}\s*Labels\s*?\n+" # ### Labels header (allow H1-H3)
            r"((?:^\s*(?!#).*\n?)+)", # Capture lines that don't start with #
            markdown_text,
            re.MULTILINE | re.IGNORECASE
        )
        if labels_match:
            labels_text = labels_match.group(1).strip()
            # Assume labels are comma or semicolon separated, potentially on multiple lines
            labels = [label.strip() for label in re.split(r'[,\s\n;]+', labels_text) if label.strip()]
            if labels:
                metadata['labels'] = labels
                logger.debug(f"Extracted labels: {labels} in {source_info}")
            else:
                logger.warning(f"Found 'Labels' section but no labels extracted from text: '{labels_text}' in {source_info}")
        else:
            logger.debug(f"No 'Labels' section found matching pattern in {source_info}")


        logger.debug(f"Final extracted metadata for {source_info}: {metadata}")
        return metadata

    def _split_markdown_by_pir_headers(
        self, markdown_text: str
    ) -> Tuple[Dict[str, str], Optional[str]]:
        """
        Splits the Markdown text into sections based on specific H3 headers.
        Also extracts the date from the 'Timeline' section.
        """
        sections: Dict[str, str] = {}
        extracted_date: Optional[str] = None
        logger.debug("Starting section splitting based on H3 headers.")

        # Define canonical section names and their *starting patterns* (case-insensitive)
        # Using specific headers from the example PIR template
        header_patterns = {
            "Summary": r"^###\s*Incident Summary.*",
            "Timeline": r"^###\s*Incident Timeline.*", # Match the header in the mock file
            "Business Impact": r"^###\s*Business Impact Summary.*",
            "Change Related": r"^###\s*Was it Change Related\?.*",
            "Root Cause": r"^###\s*Root Cause.*", # Assuming Root Cause Analysis maps here
            "Actions Arising": r"^###\s*Actions Arising.*",
            # Add others if they consistently appear with ###
            # "Problem Statement": r"^###\s*Problem Statement.*",
            # "Detection": r"^###\s*Detection.*",
            # "Mitigation/Resolution": r"^###\s*Mitigation/Resolution.*",
            # "Lessons Learned": r"^###\s*Lessons Learned.*",
        }

        # Find all H3 headers and their positions
        headers = list(re.finditer(r"^(###\s+.*)$", markdown_text, re.MULTILINE))
        logger.debug(f"Found {len(headers)} potential H3 headers.")

        if not headers:
            logger.warning("No H3 headers found. Cannot split document into sections.")
            return sections, extracted_date

        for i, current_header_match in enumerate(headers):
            header_line = current_header_match.group(1).strip()
            start_index = current_header_match.end() # Content starts after the header line
            end_index = len(markdown_text) # Default to end of document

            # Find the start index of the *next* H3 header
            if i + 1 < len(headers):
                end_index = headers[i+1].start()

            # Extract the content block for this header
            content_block = markdown_text[start_index:end_index].strip()

            # Match the found header against our defined patterns
            matched_section_name = None
            for canonical_name, pattern in header_patterns.items():
                if re.match(pattern, header_line, re.IGNORECASE):
                    matched_section_name = canonical_name
                    logger.debug(f"Header '{header_line}' matched pattern for section: '{canonical_name}'")
                    break # Take the first match

            if matched_section_name:
                sections[matched_section_name] = content_block

                # Special handling for Timeline to extract date
                if matched_section_name == "Timeline" and not extracted_date:
                    # Look for "Date: YYYYMMDD" or "Date: YYYY-MM-DD" at the start of a line
                    date_match = re.search(
                        r"^\s*Date:\s*(\d{8}|\d{4}-\d{2}-\d{2})\s*$",
                        content_block,
                        re.MULTILINE | re.IGNORECASE
                    )
                    if date_match:
                        date_str = date_match.group(1)
                        # Normalize to YYYY-MM-DD
                        if len(date_str) == 8 and date_str.isdigit():
                            extracted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
                        elif len(date_str) == 10 and date_str[4] == '-' and date_str[7] == '-':
                             extracted_date = date_str # Already in correct format
                        else:
                             logger.warning(f"Found date-like string '{date_str}' in Timeline, but format is unexpected.")
                             # extracted_date = date_str # Optionally keep non-standard format

                        if extracted_date:
                             logger.info(f"Found date '{extracted_date}' in Timeline section.")
                             # No need to break date search here, as we only search within the Timeline section once

            else:
                logger.debug(f"Header '{header_line}' did not match any defined section patterns.")

        logger.debug(f"Finished section splitting. Found {len(sections)} sections: {list(sections.keys())}")
        return sections, extracted_date


    def _format_markdown_table_section(self, section_markdown: str) -> str:
        """
        Formats a Markdown table section into a more readable text block.
        Converts each row | Key | Value | into "Key: Value".
        Handles simple tables; complex tables might need more robust parsing.
        """
        lines = section_markdown.strip().split('\n')
        formatted_lines = []
        header_skipped = False
        separator_skipped = False

        for line in lines:
            line = line.strip()
            if not line.startswith('|'):
                # Keep non-table lines as is (e.g., introductory text)
                formatted_lines.append(line)
                continue

            if not header_skipped: # Skip the first |---|---| line
                 header_skipped = True
                 continue

            if not separator_skipped and re.match(r"^\s*\|(?:[-:]+\|)+[-:]+\|?\s*$", line):
                separator_skipped = True
                continue # Skip the separator line

            # Parse data rows: | Cell1 | Cell2 | ... |
            cells = [cell.strip() for cell in line.strip('|').split('|')]
            if len(cells) >= 2:
                 # Simple Key: Value format assuming first two columns are most important
                 key = cells[0]
                 value = cells[1]
                 if key and value: # Only add if both key and value seem present
                      formatted_lines.append(f"{key}: {value}")
                 elif key: # Handle rows that might only have a key/first cell
                      formatted_lines.append(key)
                 # Optionally handle more columns:
                 # formatted_lines.append(" - ".join(filter(None, cells)))
            elif len(cells) == 1 and cells[0]:
                 formatted_lines.append(cells[0]) # Keep single-cell rows


        return "\n".join(filter(None, formatted_lines)) # Join non-empty lines

    # This method is part of the BaseNodeParser interface but might not be needed
    # if get_nodes_from_documents does all the work.
    # Keep it for potential future use or override if necessary.
    def _parse_nodes(
        self, nodes: List[TextNode], show_progress: bool = False, **kwargs
    ) -> List[TextNode]:
        """
        Parses a list of TextNodes into a refined list of TextNodes.
        (Currently a pass-through).
        """
        # For now, return the nodes as-is. Custom parsing logic could be added here.
        logger.debug(f"PIRNodeParser._parse_nodes called with {len(nodes)} nodes. Pass-through.")
        return nodes