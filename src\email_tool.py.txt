from langchain_core.tools import Tool
from langchain_core.pydantic_v1 import BaseModel, Field
import logging

# Configure logging
logger = logging.getLogger(__name__)

class EmailInput(BaseModel):
    """
    Pydantic model for email input arguments.
    """
    recipient: str = Field(description="The email address of the recipient.")
    subject: str = Field(description="The subject line of the email.")
    body: str = Field(description="The body content of the email.")

def send_email_stub(recipient: str, subject: str, body: str) -> str:
    """
    Stub function for sending an email. Logs and prints the email details without actually sending the email.

    Args:
        recipient (str): The email address of the recipient.
        subject (str): The subject line of the email.
        body (str): The body content of the email.

    Returns:
        str: A confirmation message indicating that the email draft has been prepared.
    """
    log_msg = f"--- Email STUB ---\nTo: {recipient}\nSubject: {subject}\nBody: {body[:100]}...\n--- END STUB ---"
    logger.info(log_msg)
    print(log_msg)  # Also print to console for immediate feedback during tests
    
    return f"Email draft prepared for {recipient} with subject '{subject}'. Action required by user/system to actually send."

def create_email_tool() -> Tool:
    """
    Creates a LangChain Tool for preparing email drafts.

    Returns:
        Tool: A LangChain Tool configured for preparing email drafts.
    """
    return Tool(
        name="Email_Sender_Tool",
        func=send_email_stub,
        description="Prepares an email draft. The email is not actually sent; action is required by the user or system to send it.",
        args_schema=EmailInput
    )
