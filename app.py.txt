import os
import logging

# ---> CONFIGURE LOGGING FIRST <---
# Set level=logging.DEBUG here to configure the root logger AND its default handler
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - [%(name)s] %(message)s',
    force=True # Add force=True to override any potential default config by libraries
)
# Get the logger for this specific file (app.py)
logger = logging.getLogger(__name__)
logger.debug("Root logger configured for DEBUG level in app.py")
# ---------------------------------

import chainlit as cl
from llama_index.core import Settings
from llama_index.core.schema import Document, QueryBundle
from llama_index.llms.openai_like import OpenAILike
from llama_index.llms.openai import OpenAI
from llama_index.core.postprocessor import SentenceTransformerRerank
from src.embedding import get_embedding_model
from src.indexing import get_vector_index
from src.retrieval import create_pir_retrieval_tool
from src.agent_config import create_pir_agent, LlamaIndexToolWrapper
from src.email_tool import create_email_tool
from src.data_loader import load_pirs_from_confluence, load_html_content, load_confluence_child_pages
from src.node_parser import PIRNodeParser
from langchain_openai import ChatOpenAI
from langchain_core.agents import AgentAction
from dotenv import load_dotenv

# Configure logging
# logger = logging.getLogger(__name__)  # Define the logger object


@cl.on_chat_start
async def on_chat_start():
    """Initialize the application, load the persistent index, and set up the LangChain agent."""
    persist_dir = "./storage/pir_chroma_index"

    load_dotenv()

    # Using OpenAI-like embedding model EMBEDDING_MODEL_NAME, API_KEY and API_BASE_URL
    # defined in the environment variable to handle technical nuance found in PIRs.
    api_key = os.getenv("API_KEY")
    api_base = os.getenv("API_BASE_URL")

    # Notify user of initialization
    await cl.Message(content="Initializing application and loading index...").send()

    from llama_index.llms.openai_like import OpenAILike
    llamaindex_llm = OpenAILike(
        model="deepseek-r1-distill-qwen-32b-250120",
        api_base=api_base,
        api_key=api_key,
        max_tokens=4096,
        api_version="250120",
        temperature=0.1,
        is_chat_model=True
    )
    
    try:
        # Instantiate the agent's LLM
        llm = ChatOpenAI(
            model="deepseek-r1-distill-qwen-32b-250120",
            openai_api_base=api_base,
            openai_api_key=api_key,
            temperature=0.1
        )
    except Exception as e:
        await cl.Message(content=f"❌ **Error initializing LLM:** {e}").send()
        return

    # Set up embedding model
    embed_model = get_embedding_model()
    Settings.embed_model = embed_model
    Settings.llm = llamaindex_llm  # LLM for retrieval engine might be needed later

    # Check if mock data should be loaded
    LOAD_MOCK_DATA = os.getenv("LOAD_MOCK_DATA", "true").lower() == "true"
    nodes = []

    if LOAD_MOCK_DATA:
        # Load mock data from a file
        mock_file_path = "./tests/test_data/mock_pir_pages/mock_pir_INC12345678.html"  # Adjust the path as needed
        raw_content = load_html_content(mock_file_path)
        if raw_content:
            raw_doc = Document(text=raw_content)
            parser = PIRNodeParser()
            nodes = parser.get_nodes_from_documents([raw_doc])
            logger.info(f"Loaded and parsed {len(nodes)} nodes from mock data.")
        else:
            logger.error("Failed to load mock data.")
    else:
        # Load from Confluence
        parent_id = os.getenv("CONFLUENCE_PARENT_PAGE_ID")
        if not parent_id:
            logger.error("CONFLUENCE_PARENT_PAGE_ID not set in environment variables.")
            await cl.Message(content="❌ Error: CONFLUENCE_PARENT_PAGE_ID not set.").send()
            return

        await cl.Message(content=f"Loading child pages from Confluence parent ID: {parent_id}...").send()
        confluence_docs = await cl.make_async(load_confluence_child_pages)(parent_id)
        if confluence_docs:
            parser = PIRNodeParser()  # Use custom parser
            nodes = parser.get_nodes_from_documents(confluence_docs)  # Process loaded LlamaIndex docs
            logging.info(f"Processed {len(nodes)} nodes from Confluence.")
            await cl.Message(content=f"✅ Loaded and processed {len(nodes)} pages from Confluence.").send()
        else:
            logging.warning(f"Failed to load from Confluence parent ID {parent_id} or no child documents found.")
            await cl.Message(content=f"⚠️ Failed to load documents from Confluence.").send()

    if not nodes:
        await cl.Message(content="❌ Error: No data loaded (mock or Confluence). Cannot build index.").send()
        return

    try:
        # Attempt to load the index
        index = get_vector_index(nodes=nodes if nodes else None, persist_dir=persist_dir)
    except ValueError as e:
        await cl.Message(content=f"❌ **Error:** Index not found at `{persist_dir}`. Please run `python run_pipeline.py --build` first.").send()
        return
    except Exception as e:
        await cl.Message(content=f"❌ **Error loading index:** {e}").send()
        return

    # Store the index in the session
    cl.user_session.set("vector_index", index)

    # Create the email tool
    email_tool = create_email_tool()

    query_tool = create_pir_retrieval_tool(index, similarity_top_k=5, rerank_top_n=1)
    # Extract name and description from the LlamaIndex tool's metadata
    tool_name = query_tool.metadata.name
    tool_description = query_tool.metadata.description
    # Wrap the LlamaIndex FunctionTool, passing name and description explicitly
    langchain_compatible_tool = LlamaIndexToolWrapper(tool=query_tool, name=tool_name, description=tool_description)
    

    # Define the tools list
    tools = [langchain_compatible_tool, email_tool]
    logger.info(f"Tools list: {tools}")

    # Create the agent executor
    agent_executor = create_pir_agent(llm, tools)

    # Store the agent executor in the session
    cl.user_session.set("agent_executor", agent_executor)

    # Notify user that the index and agent are loaded
    await cl.Message(content=f"✅ Index and agent loaded. Ready to answer questions about indexed PIRs and prepare email drafts.").send()


@cl.on_message
async def on_message(message: cl.Message):
    """Handle user messages using the LangChain agent and display sources from intermediate steps."""
    # Retrieve the agent executor from the session
    agent_executor = cl.user_session.get("agent_executor")
    if not agent_executor:
        await cl.Message(content="Error: Agent executor not initialized. Please restart the chat.").send()
        return

    query_text = message.content
    
    try:
        # Invoke the agent with the user's query
        response = await cl.make_async(agent_executor.invoke)({"input": query_text})
        
        # Extract the final answer from the agent's response
        agent_answer = response.get('output', 'I could not generate a response to your query.')
        
        # Extract sources from the observation in intermediate steps
        source_elements = []
        intermediate_steps = response.get('intermediate_steps', [])
        
        if intermediate_steps:
            for action, observation in intermediate_steps:
                # Look for steps where the PIR retriever tool was used
                if isinstance(action, AgentAction) and action.tool == "PIR_Retriever_Tool":
                    # Observation is the dict: {'retrieved_context': ..., 'sources': [...]}
                    if isinstance(observation, dict) and 'sources' in observation:
                        sources = observation['sources']
                        # Format sources into cl.Text elements
                        for i, source in enumerate(sources):
                            metadata = source.get('metadata', {})
                            score = source.get('score', 0)
                            text_preview = source.get('text', '')[:350].strip() + "..."
                            content = f"**Source {i + 1}** (Score: {score:.2f})\n*Section:* {metadata.get('section_title', 'N/A')}\n\n{text_preview}"
                            source_elements.append(cl.Text(content=content, name=f"Source_{i + 1}", display="inline"))
                    elif isinstance(observation, dict) and 'error' in observation:
                        source_elements.append(cl.Text(content=f"⚠️ Tool Error: {observation['error']}", name="Error", display="inline"))
                    break  # Only process the first PIR retriever tool usage
        
        # Send the agent's answer with the source elements
        await cl.Message(content=agent_answer, elements=source_elements).send()

    except Exception as e:
        logger.error(f"Agent execution failed: {e}", exc_info=True)
        await cl.Message(content=f"Error processing query: {e}").send()


if __name__ == "__main__":
    cl.run()
