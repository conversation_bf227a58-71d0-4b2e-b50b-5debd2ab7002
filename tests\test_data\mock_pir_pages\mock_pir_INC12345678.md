**Reference**| **Details**  
---|---  
Impacted Application(s)| API Gateway  
Impacted Regions| EMEA, APAC  
Incident Number| INC12345678  
Incident Ticket| INC12345678  
Impact| High  
Problem Ticket| PRB12345678  
Problem Owner| Alice B.  
Post Incident Review Status| Done  
  
### Incident Summary (high level description of the incident)

A high severity incident occurred on 2023-11-15 involving API Gateway
timeouts, significantly impacting the Auth Service across EMEA and APAC
regions. The issue led to customer login failures and transaction errors.

### Incident Timeline

Date: 20231115

Timezone: GMT / HKT / IST

**Incident Detail**| **Timeline**| **Details**  
---|---|---  
Start Time<Time that Incident Started>| 14:05 UTC| Initial reports of elevated
latency received from internal monitoring.  
Detected Time <Time that issue was detected, state whether this was via
alerting, end user>| 14:15 UTC| Monitoring alerts triggered for 5xx errors on
API Gateway endpoints. Investigation initiated by API Services team.  
Fixed Time<Time the issue was fixed or workaround applied>| 15:30 UTC|
Identified configuration error in Auth Service client retry logic. Rollback of
recent change deployed to production.  
Fully Recovery Time <Time the issue was fully recovered> | 15:45 UTC| Services confirmed stable, error rates returned to normal. Monitoring validated.  
  
### Business Impact Summary

**Actual Business Impact Summary**| **Service Outage**| **If "Yes", Full or Degraded Outage**| **If "Yes", added in SOW?**  
---|---|---|---  
Significant customer impact with login failures and payment transaction errors reported via support channels. Estimated 15% transaction failure rate during peak impact.| Yes| Degrade| Yes  
  
### Was it Change Related?

**Change Related?**| **If "Yes", what's the Change number**| **If "Yes", what's Change Sub Type**| **If "Yes", is it linked with Incident in SOW**  
---|---|---|---  
No| CHG0000001| DevOps| Yes  
  
### Root Cause

**Root Cause Analysis**| **Root Cause Analysis Detail** | **Status (Completed or not)**
---|---|---  
Configuration error in upstream service retry logic.| Timeout settings for retries from API Gateway to the Auth Service client were too aggressive
(100ms) following the Auth Service v2.1 update. This caused cascading failures
under moderate load as retries overwhelmed the service.| Completed  
  
### Actions Arising

(Has an interim work-around been established? Actions as improvement, as well
as how to address any lessons learned)

**Root Cause Fix**| **Action**| **Status (Completed or not)**| **Jira (optional)**  
---|---|---|---  
Configuration Fix| Increase Auth Service client timeout to 500ms and implement
exponential backoff strategy in API Gateway client.| Completed| JIRA-101  
Monitoring| Add specific monitoring dashboard for API Gateway -> Auth Service
latency and error rates, with refined alerting thresholds.| In Progress|
JIRA-102  
Process Improvement| Review pre-deployment testing procedures for inter-
service dependency configuration changes.| Planned| JIRA-103  
  
### Labels

production, high-impact, api-gateway, auth-service, configuration, emea, apac,
timeout