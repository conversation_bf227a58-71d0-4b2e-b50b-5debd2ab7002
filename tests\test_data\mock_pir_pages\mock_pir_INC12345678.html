<h2>20231115 - INC12345678 - API Services - API Gateway Timeout</h2>
<table>
    <tr>
        <td>Severity</td>
        <td>High</td>
    </tr>
    <tr>
        <td>Impacted Application(s)</td>
        <td>API Gateway; Auth Service</td>
    </tr>
    <tr>
        <td>Impacted Regions</td>
        <td>EMEA; APAC</td>
    </tr>
    <tr>
        <td>Incident Number</td>
        <td>INC12345678</td>
    </tr>
    <tr>
        <td>Incident Ticket</td>
        <td>INC12345678</td>
    </tr>
    <tr>
        <td>Problem Ticket</td>
        <td>PRB12345678</td>
    </tr>
    <tr>
        <td>Problem Owner</td>
        <td>Alice B.</td>
    </tr>
    <tr>
        <td>Post Incident Review Status</td>
        <td>Done</td>
    </tr>
</table>
<h2>Incident Summary</h2>
<p>A high severity incident occurred on 2023-11-15 involving API Gateway timeouts, significantly impacting the Auth Service across EMEA and APAC regions. The issue led to customer login failures and transaction errors.</p>
<h2>Incident Timeline</h2>
<table>
    <thead>
        <tr>
            <th>Incident Detail</th>
            <th>Timeline</th>
            <th>Details</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Start Time</td>
            <td>14:05 UTC</td>
            <td>Initial reports of elevated latency received from internal monitoring.</td>
        </tr>
        <tr>
            <td>Detected Time</td>
            <td>14:15 UTC</td>
            <td>Monitoring alerts triggered for 5xx errors on API Gateway endpoints. Investigation initiated by API Services team.</td>
        </tr>
        <tr>
            <td>Fixed Time</td>
            <td>15:30 UTC</td>
            <td>Identified configuration error in Auth Service client retry logic. Rollback of recent change deployed to production.</td>
        </tr>
        <tr>
            <td>Fully Recovery Time</td>
            <td>15:45 UTC</td>
            <td>Services confirmed stable, error rates returned to normal. Monitoring validated.</td>
        </tr>
    </tbody>
</table>
<h2>Business Impact Summary</h2>
<table>
    <tr>
        <td>Actual Business Impact Summary</td>
        <td>Service Outage</td>
        <td>If "Yes", Full or Degraded Outage</td>
        <td>If "Yes", added in SOW?</td>
    </tr>
    <tr>
        <td>Significant customer impact with login failures and payment transaction errors reported via support channels. Estimated 15% transaction failure rate during peak impact.</td>
        <td>Yes</td>
        <td>Degraded</td>
        <td>Yes</td>
    </tr>
</table>
<h2>Was it Change Related?</h2>
<table>
    <tr>
        <td>Change Related?</td>
        <td>If "Yes", what's the Change number</td>
        <td>If "Yes", what's Change Sub Type</td>
        <td>If "Yes", is it linked with Incident in SOW</td>
    </tr>
    <tr>
        <td>No</td>
        <td>N/A</td>
        <td>N/A</td>
        <td>N/A</td>
    </tr>
</table>
<h2>Root Cause Analysis</h2>
<table>
    <tr>
        <td>Root Cause Analysis</td>
        <td>Root Cause Analysis Detail</td>
        <td>Status (Completed or not)</td>
    </tr>
    <tr>
        <td>Configuration error in upstream service retry logic.</td>
        <td>Timeout settings for retries from API Gateway to the Auth Service client were too aggressive (100ms) following the Auth Service v2.1 update. This caused cascading failures under moderate load as retries overwhelmed the service.</td>
        <td>Completed</td>
    </tr>
</table>
<h2>Actions Arising</h2>
<table>
    <thead>
        <tr>
            <th>Root Cause Fix</th>
            <th>Action</th>
            <th>Status (Completed or not)</th>
            <th>Jira (optional)</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Configuration Fix</td>
            <td>Increase Auth Service client timeout to 500ms and implement exponential backoff strategy in API Gateway client.</td>
            <td>Completed</td>
            <td>JIRA-101</td>
        </tr>
        <tr>
            <td>Monitoring</td>
            <td>Add specific monitoring dashboard for API Gateway -> Auth Service latency and error rates, with refined alerting thresholds.</td>
            <td>In Progress</td>
            <td>JIRA-102</td>
        </tr>
        <tr>
            <td>Process Improvement</td>
            <td>Review pre-deployment testing procedures for inter-service dependency configuration changes.</td>
            <td>Planned</td>
            <td>JIRA-103</td>
        </tr>
    </tbody>
</table>
<h2>Labels</h2>
<p>production, high-impact, api-gateway, auth-service, configuration, emea, apac, timeout</p> 