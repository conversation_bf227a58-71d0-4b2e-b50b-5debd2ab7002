<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incident PIR RAG Application - Prompts #7-9</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 { font-size: 2.2rem; font-weight: 700; margin-top: 2rem; margin-bottom: 1rem; color: #1a365d; }
        h2 { font-size: 1.8rem; font-weight: 600; margin-top: 1.8rem; margin-bottom: 0.8rem; color: #2c5282; }
        h3 { font-size: 1.5rem; font-weight: 600; margin-top: 1.5rem; margin-bottom: 0.7rem; color: #2b6cb0; }
        h4 { font-size: 1.3rem; font-weight: 600; margin-top: 1.3rem; margin-bottom: 0.6rem; color: #3182ce; }
        p { margin-bottom: 1rem; }
        ul, ol { margin-bottom: 1rem; padding-left: 2rem; }
        li { margin-bottom: 0.5rem; }
        pre { margin-bottom: 1rem; overflow-x: auto; border-radius: 0.375rem; }
        code { font-family: Menlo, Monaco, Consolas, 'Courier New', monospace; }
        .prompt-container {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            background-color: #f8fafc;
        }
        .prompt-header {
            background-color: #edf2f7;
            padding: 1rem;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
        }
        .prompt-content {
            padding: 1.5rem;
        }
        .implementation-container {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            background-color: #f7fafc;
        }
        .implementation-header {
            background-color: #ebf4ff;
            padding: 1rem;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #2c5282;
        }
        .implementation-content {
            padding: 1.5rem;
        }
        .code-block {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            font-family: Menlo, Monaco, Consolas, 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <header class="mb-8">
        <h1 class="text-4xl font-bold text-center text-blue-800">Incident PIR RAG Application</h1>
        <p class="text-xl text-center text-blue-600">Complete AI IDE Prompts and Implementation Details for Components #7-9</p>
    </header>

    <main>
        <section id="intro" class="mb-12">
            <h2>Introduction</h2>
            <p>This document provides comprehensive AI IDE prompts and implementation details for building the final components of the Incident PIR RAG application. These prompts are designed for AI IDEs such as Roo Cline, Cursor, and Windsurf to generate code segments that can be easily validated and integrated into the application.</p>
            <p>The following components are covered in this document:</p>
            <ol>
                <li><strong>Email Tool Implementation</strong> - Creating a stub for the email functionality with Chainlit user approval</li>
                <li><strong>End-to-End Workflow Testing</strong> - Testing the complete application with sample data and user queries</li>
                <li><strong>Project Requirements Document</strong> - Creating a comprehensive PRD for the application</li>
            </ol>
        </section>

        <!-- Component 7: Email Tool Implementation -->
        <section id="email-tool-implementation" class="mb-16">
            <h2 class="text-2xl font-bold text-blue-800 mb-4">7. Email Tool Implementation</h2>
            
            <div class="prompt-container">
                <div class="prompt-header">AI IDE Prompt</div>
                <div class="prompt-content">
                    <p><strong>Task:</strong> Implement a stub "send_email" tool for the incident PIR RAG application that integrates with Chainlit for user approval.</p>
                    
                    <h4>Context:</h4>
                    <p>I'm building an incident PIR RAG application using LangChain and Chainlit. I need to implement a stub "send_email" tool that our ReAct agent can use to send emails based on incident information. Instead of actually sending emails, the tool should log the details and use Chainlit's "ask user" functionality to request approval before "sending" the email.</p>
                    
                    <h4>Requirements:</h4>
                    <ul>
                        <li>Create a LangChain tool called "send_email" that accepts parameters for recipient, subject, body, and optional attachments</li>
                        <li>Use Chainlit's "ask_user" function to request user approval before proceeding</li>
                        <li>Log email details to console and application logs</li>
                        <li>Handle both plain text and HTML email formats</li>
                        <li>Support multiple recipients (list or comma-separated string)</li>
                        <li>Support CC and BCC fields</li>
                        <li>Return appropriate success/failure messages for the ReAct agent to process</li>
                        <li>Include proper error handling and validation</li>
                        <li>Add type hints and docstrings for better code readability</li>
                    </ul>
                    
                    <h4>Expected Input:</h4>
                    <ul>
                        <li>Recipient(s): Email address(es) to send to</li>
                        <li>Subject: Email subject line</li>
                        <li>Body: Email content (plain text or HTML)</li>
                        <li>CC: Optional CC recipients</li>
                        <li>BCC: Optional BCC recipients</li>
                        <li>Attachments: Optional file paths or URLs to attach (stub only, no actual file handling needed)</li>
                    </ul>
                    
                    <h4>Expected Output:</h4>
                    <ul>
                        <li>Success message if user approves the email</li>
                        <li>Rejection message if user denies the email</li>
                        <li>Proper log entries showing all email details</li>
                    </ul>
                    
                    <h4>Integration Points:</h4>
                    <ul>
                        <li>LangChain ReAct agent tool definition</li>
                        <li>Chainlit UI for user approval</li>
                        <li>Application logging system</li>
                    </ul>
                    
                    <p>Please provide the implementation for this tool as a standalone Python module that I can import into my main application. Include a simple test function to verify the tool works correctly.</p>
                </div>
            </div>
            
            <div class="implementation-container">
                <div class="implementation-header">Implementation Details</div>
                <div class="implementation-content">
                    <h3>Email Tool Implementation</h3>
                    
                    <p>We'll create a dedicated Python module called <code>email_tool.py</code> to implement the email functionality as a LangChain tool that integrates with Chainlit for user approval.</p>
                    
                    <h4>1. Create the email_tool.py module</h4>
                    
                    <div class="code-block">
```python
# email_tool.py
"""
Email Tool module for the Incident PIR RAG application.
This module provides a LangChain-compatible tool for sending emails,
with Chainlit integration for user approval.
"""

import logging
import json
from typing import List, Optional, Union, Dict, Any
from datetime import datetime
import chainlit as cl
from langchain.tools import BaseTool
from pydantic import BaseModel, EmailStr, validator, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EmailRequest(BaseModel):
    """Model for email request parameters."""
    to: Union[str, List[str]] = Field(..., description="Recipient email address(es)")
    subject: str = Field(..., description="Email subject line")
    body: str = Field(..., description="Email body content")
    cc: Optional[Union[str, List[str]]] = Field(None, description="CC recipient(s)")
    bcc: Optional[Union[str, List[str]]] = Field(None, description="BCC recipient(s)")
    is_html: bool = Field(False, description="Whether the email body is HTML")
    attachments: Optional[List[str]] = Field(None, description="List of attachment paths or URLs")
    
    @validator('to', 'cc', 'bcc')
    def convert_string_to_list(cls, v):
        """Convert comma-separated email strings to lists."""
        if isinstance(v, str):
            return [email.strip() for email in v.split(',')]
        return v
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging and display."""
        return {
            "to": self.to,
            "subject": self.subject,
            "body": self.body[:500] + "..." if len(self.body) > 500 else self.body,
            "cc": self.cc,
            "bcc": self.bcc,
            "is_html": self.is_html,
            "attachments": self.attachments,
            "timestamp": datetime.now().isoformat()
        }

class SendEmailTool(BaseTool):
    """Tool for sending emails with user approval via Chainlit."""
    
    name = "send_email"
    description = """
    Send an email with the specified details.
    Input should be a JSON string with the following fields:
    - to: Email address(es) of the recipient(s). Can be a single email or a list of emails.
    - subject: Subject line of the email.
    - body: Content of the email.
    - cc: Optional. CC recipient(s). Can be a single email or a list of emails.
    - bcc: Optional. BCC recipient(s). Can be a single email or a list of emails.
    - is_html: Optional. Whether the email body is HTML. Default is False.
    - attachments: Optional. List of attachment paths or URLs.
    
    Example: {"to": "<EMAIL>", "subject": "Incident Report", "body": "Details about the incident..."}
    """
    
    async def _arun(self, tool_input: str) -> str:
        """Asynchronous execution of the email tool with Chainlit integration."""
        try:
            # Parse the input JSON
            if isinstance(tool_input, str):
                try:
                    email_data = json.loads(tool_input)
                except json.JSONDecodeError:
                    return "Error: Input must be a valid JSON string. Please check your input format."
            else:
                email_data = tool_input
            
            # Validate email request
            try:
                email_request = EmailRequest(**email_data)
            except Exception as e:
                return f"Error: Invalid email request parameters: {str(e)}"
            
            # Log the email request
            logger.info(f"Email request received: {email_request.to_dict()}")
            
            # Format email preview for user approval
            email_preview = self._format_email_preview(email_request)
            
            # Ask for user approval using Chainlit
            result = await self._request_user_approval(email_request, email_preview)
            return result
            
        except Exception as e:
            error_msg = f"Error in send_email tool: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def _run(self, tool_input: str) -> str:
        """Synchronous execution is not supported with Chainlit."""
        return "This tool only supports asynchronous execution with Chainlit."
    
    async def _request_user_approval(self, email_request: EmailRequest, email_preview: str) -> str:
        """Request user approval for sending the email using Chainlit's ask_user function."""
        try:
            # Create the approval message with formatted email preview
            approval_message = f"### Email Approval Request\n\n{email_preview}\n\nDo you approve sending this email?"
            
            # Ask the user for approval
            response = await cl.AskUserMessage(
                content=approval_message,
                author="Email System",
                timeout=120,  # 2 minutes timeout
            ).send()
            
            # Process the user response
            if response and response.lower() in ["yes", "y", "approve", "approved", "ok", "okay", "sure", "confirm"]:
                # Log the approved email
                logger.info(f"Email APPROVED by user: {email_request.to_dict()}")
                
                # In a real implementation, this is where we would send the actual email
                # For now, we just log it as if it was sent
                return self._handle_approved_email(email_request)
            else:
                # Log the rejected email
                logger.info(f"Email REJECTED by user: {email_request.to_dict()}")
                return "Email sending cancelled by user."
                
        except Exception as e:
            error_msg = f"Error requesting user approval: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def _handle_approved_email(self, email_request: EmailRequest) -> str:
        """Handle approved email (stub implementation)."""
        # Log detailed information about the "sent" email
        log_data = {
            "action": "SEND_EMAIL",
            "status": "SUCCESS",
            "timestamp": datetime.now().isoformat(),
            "email_details": email_request.to_dict()
        }
        logger.info(f"EMAIL_SENT: {json.dumps(log_data)}")
        
        # Return success message to the agent
        return f"Email successfully sent to {', '.join(email_request.to)} with subject: '{email_request.subject}'"
    
    def _format_email_preview(self, email_request: EmailRequest) -> str:
        """Format the email for preview in the approval request."""
        preview = f"""
**To:** {', '.join(email_request.to)}
**Subject:** {email_request.subject}
**CC:** {', '.join(email_request.cc) if email_request.cc else 'None'}
**BCC:** {', '.join(email_request.bcc) if email_request.bcc else 'None'}
**Format:** {'HTML' if email_request.is_html else 'Plain Text'}
**Attachments:** {', '.join(email_request.attachments) if email_request.attachments else 'None'}

**Body:**
{email_request.body[:1000]} {'...' if len(email_request.body) > 1000 else ''}

"""
        return preview


# Function to create and return the tool
def get_send_email_tool() -> SendEmailTool:
    """Create and return an instance of the SendEmailTool."""
    return SendEmailTool()


# Simple test function to verify the tool works
async def test_send_email_tool():
    """Test function for the SendEmailTool."""
    tool = get_send_email_tool()
    test_input = json.dumps({
        "to": "<EMAIL>",
        "subject": "Test Email from PIR RAG Application",
        "body": "This is a test email to verify the email tool is working correctly.",
        "cc": "<EMAIL>",
        "is_html": False
    })
    
    result = await tool._arun(test_input)
    print(f"Result: {result}")
    return result


if __name__ == "__main__":
    import asyncio
    # This will only work in an async environment with Chainlit running
    # In practice, this would be called from the Chainlit app
    print("This module is designed to be imported and used with Chainlit.")
    print("Running standalone will not work due to Chainlit integration.")
                </div>
                
                <h4>2. Integration with LangChain ReAct Agent</h4>
                
                <div class="code-block">
Copy# agent_config.py (excerpt showing email tool integration)
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from email_tool import get_send_email_tool

def create_agent(llm, retriever):
    """Create a ReAct agent with the necessary tools."""
    
    # Define the tools
    tools = [
        Tool(
            name="SearchIncidentReports",
            func=retriever.get_relevant_documents,
            description="Search for information in incident reports. Input should be a search query related to incidents."
        ),
        get_send_email_tool(),  # Add the email tool
        # Add other tools as needed
    ]
    
    # Create the agent
    agent = create_react_agent(
        llm=llm,
        tools=tools,
        prompt=REACT_AGENT_PROMPT  # Define this prompt elsewhere in your application
    )
    
    # Create the agent executor
    agent_executor = AgentExecutor.from_agent_and_tools(
        agent=agent,
        tools=tools,
        verbose=True,
        handle_parsing_errors=True
    )
    
    return agent_executor
                </div>
                
                <h4>3. Example Usage within Chainlit App</h4>
                
                <div class="code-block">
Copy# app.py (excerpt showing email tool usage in Chainlit)
import chainlit as cl
from agent_config import create_agent
from langchain.schema import HumanMessage
from email_tool import get_send_email_tool

# Initialize components
llm = None  # Initialize your custom LLM
retriever = None  # Initialize your custom retriever
agent = None

@cl.on_chat_start
async def on_chat_start():
    global llm, retriever, agent
    # Initialize your LLM and retriever here
    
    # Create the agent with tools including email
    agent = create_agent(llm, retriever)
    
    # Welcome message
    await cl.Message(
        content="Welcome to the Incident PIR RAG Assistant. Ask me about incidents or request me to send emails with incident information."
    ).send()

@cl.on_message
async def on_message(message: cl.Message):
    # Process the user's message
    response = await agent.arun(message.content)
    
    # Send the response back to the user
    await cl.Message(content=response).send()
                </div>
                
                <h4>4. Test Case for Email Tool</h4>
                
                <div class="code-block">
Copy# test_email_tool.py
import asyncio
import json
import pytest
from unittest.mock import patch, AsyncMock
from email_tool import get_send_email_tool

# Mock Chainlit's ask_user function
@pytest.fixture
def mock_chainlit_ask():
    with patch('chainlit.AskUserMessage.send', new_callable=AsyncMock) as mock_ask:
        # Set up the mock to return "yes" (approval)
        mock_ask.return_value = "yes"
        yield mock_ask

@pytest.mark.asyncio
async def test_email_tool_approved(mock_chainlit_ask):
    """Test the email tool with user approval."""
    # Get the tool
    email_tool = get_send_email_tool()
    
    # Create test input
    test_input = json.dumps({
        "to": "<EMAIL>",
        "subject": "Test Incident Report",
        "body": "This is a test email about an incident.",
        "cc": "<EMAIL>",
        "is_html": False
    })
    
    # Run the tool
    result = await email_tool._arun(test_input)
    
    # Check that Chainlit ask was called
    assert mock_chainlit_ask.called
    
    # Check the result
    assert "successfully sent" in result
    assert "<EMAIL>" in result
    assert "Test Incident Report" in result

@pytest.mark.asyncio
async def test_email_tool_rejected(mock_chainlit_ask):
    """Test the email tool with user rejection."""
    # Set up the mock to return "no" (rejection)
    mock_chainlit_ask.return_value = "no"
    
    # Get the tool
    email_tool = get_send_email_tool()
    
    # Create test input
    test_input = json.dumps({
        "to": "<EMAIL>",
        "subject": "Test Incident Report",
        "body": "This is a test email about an incident."
    })
    
    # Run the tool
    result = await email_tool._arun(test_input)
    
    # Check the result
    assert "cancelled" in result

@pytest.mark.asyncio
async def test_email_tool_invalid_input():
    """Test the email tool with invalid input."""
    # Get the tool
    email_tool = get_send_email_tool()
    
    # Run the tool with invalid JSON
    result = await email_tool._arun("not valid json")
    
    # Check the result
    assert "Error" in result
    assert "valid JSON" in result

@pytest.mark.asyncio
async def test_email_tool_missing_fields():
    """Test the email tool with missing required fields."""
    # Get the tool
    email_tool = get_send_email_tool()
    
    # Run the tool with missing required fields
    result = await email_tool._arun(json.dumps({"subject": "Test", "body": "Test"}))
    
    # Check the result
    assert "Error" in result
    assert "Invalid email request" in result
                </div>
                
                <h4>5. Email Templates for Common Incident Scenarios (Optional Enhancement)</h4>
                
                <div class="code-block">
Copy# email_templates.py
"""
Email templates for common incident scenarios.
These templates can be used with the send_email tool to generate consistent emails.
"""

from string import Template
from typing import Dict, Any

class EmailTemplates:
    """Collection of email templates for various incident-related communications."""
    
    @staticmethod
    def incident_notification(incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an email for initial incident notification.
        
        Args:
            incident_data: Dictionary containing incident details
                - incident_id: The incident ID
                - subject: The incident subject
                - impact: The impact level
                - start_time: When the incident started
                - detected_time: When the incident was detected
                - impacted_apps: The impacted applications
                - impacted_regions: The impacted regions
                
        Returns:
            Dictionary with email to, subject, and body
        """
        subject_template = Template("Incident Notification: $incident_id - $subject")
        
        body_template = Template("""
Dear Team,

This is to notify you of a new incident that has been detected:

Incident ID: $incident_id
Subject: $subject
Impact Level: $impact
Start Time: $start_time
Detection Time: $detected_time
Impacted Applications: $impacted_apps
Impacted Regions: $impacted_regions

Please monitor the situation and follow the incident response procedures.

Regards,
Incident Management Team
        """)
        
        subject = subject_template.substitute(
            incident_id=incident_data.get("incident_id", "UNKNOWN"),
            subject=incident_data.get("subject", "Incident Alert")
        )
        
        body = body_template.substitute(
            incident_id=incident_data.get("incident_id", "UNKNOWN"),
            subject=incident_data.get("subject", "Incident Alert"),
            impact=incident_data.get("impact", "UNKNOWN"),
            start_time=incident_data.get("start_time", "UNKNOWN"),
            detected_time=incident_data.get("detected_time", "UNKNOWN"),
            impacted_apps=incident_data.get("impacted_apps", "UNKNOWN"),
            impacted_regions=incident_data.get("impacted_regions", "UNKNOWN")
        )
        
        return {
            "to": incident_data.get("recipients", []),
            "subject": subject,
            "body": body,
            "is_html": False
        }
    
    @staticmethod
    def incident_resolution(incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an email for incident resolution notification.
        
        Args:
            incident_data: Dictionary containing incident details
                - incident_id: The incident ID
                - subject: The incident subject
                - resolution_time: When the incident was resolved
                - root_cause: Brief description of the root cause
                - fix_details: How the issue was fixed
                - next_steps: Any follow-up actions required
                
        Returns:
            Dictionary with email to, subject, and body
        """
        subject_template = Template("RESOLVED: Incident $incident_id - $subject")
        
        body_template = Template("""
Dear Team,

This is to inform you that the following incident has been resolved:

Incident ID: $incident_id
Subject: $subject
Resolution Time: $resolution_time

Root Cause:
$root_cause

Fix Details:
$fix_details

Next Steps:
$next_steps

A full Post Incident Review will be scheduled and communicated separately.

Thank you for your support during this incident.

Regards,
Incident Management Team
        """)
        
        subject = subject_template.substitute(
            incident_id=incident_data.get("incident_id", "UNKNOWN"),
            subject=incident_data.get("subject", "Incident Resolution")
        )
        
        body = body_template.substitute(
            incident_id=incident_data.get("incident_id", "UNKNOWN"),
            subject=incident_data.get("subject", "Incident Resolution"),
            resolution_time=incident_data.get("resolution_time", "UNKNOWN"),
            root_cause=incident_data.get("root_cause", "Under investigation"),
            fix_details=incident_data.get("fix_details", "Details pending"),
            next_steps=incident_data.get("next_steps", "Will be communicated soon")
        )
        
        return {
            "to": incident_data.get("recipients", []),
            "subject": subject,
            "body": body,
            "is_html": False
        }
    
    @staticmethod
    def pir_scheduled(incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an email for PIR meeting scheduling.
        
        Args:
            incident_data: Dictionary containing incident details
                - incident_id: The incident ID
                - subject: The incident subject
                - meeting_time: The scheduled PIR meeting time
                - meeting_link: The meeting link (virtual meetings)
                - location: The meeting location (physical meetings)
                - required_attendees: List of required attendees
                - optional_attendees: List of optional attendees
                
        Returns:
            Dictionary with email to, subject, and body
        """
        subject_template = Template("PIR Meeting: Incident $incident_id - $subject")
        
        body_template = Template("""
Dear Team,

A Post Incident Review (PIR) meeting has been scheduled for the following incident:

Incident ID: $incident_id
Subject: $subject

Meeting Details:
Time: $meeting_time
$location_or_link

Please come prepared to discuss:
1. Timeline of the incident
2. Detection and response process
3. Root cause analysis
4. Action items and preventive measures

The goal is to identify improvements to prevent similar incidents in the future.

Required Attendees: $required_attendees
Optional Attendees: $optional_attendees

Regards,
Incident Management Team
        """)
        
        # Determine if virtual or physical meeting
        if incident_data.get("meeting_link"):
            location_or_link = f"Meeting Link: {incident_data.get('meeting_link')}"
        else:
            location_or_link = f"Location: {incident_data.get('location', 'TBD')}"
        
        subject = subject_template.substitute(
            incident_id=incident_data.get("incident_id", "UNKNOWN"),
            subject=incident_data.get("subject", "Post Incident Review")
        )
        
        body = body_template.substitute(
            incident_id=incident_data.get("incident_id", "UNKNOWN"),
            subject=incident_data.get("subject", "Post Incident Review"),
            meeting_time=incident_data.get("meeting_time", "UNKNOWN"),
            location_or_link=location_or_link,
            required_attendees=", ".join(incident_data.get("required_attendees", [])),
            optional_attendees=", ".join(incident_data.get("optional_attendees", []))
        )
        
        # Set up recipients
        to_list = incident_data.get("required_attendees", [])
        cc_list = incident_data.get("optional_attendees", [])
        
        return {
            "to": to_list,
            "cc": cc_list,
            "subject": subject,
            "body": body,
            "is_html": False
        }
                </div>
                
                <h4>Implementation Notes</h4>
                
                <p>The email tool implementation provides:</p>
                
                <ul>
                    <li><strong>Flexible input handling</strong>: Accepts both JSON strings and dictionary inputs</li>
                    <li><strong>User approval integration</strong>: Uses Chainlit's ask_user functionality to get approval before "sending"</li>
                    <li><strong>Comprehensive logging</strong>: Records all email activities for audit purposes</li>
                    <li><strong>Input validation</strong>: Uses Pydantic models to validate email parameters</li>
                    <li><strong>Error handling</strong>: Gracefully handles JSON parsing errors, validation issues, and runtime exceptions</li>
                    <li><strong>Multiple formats</strong>: Supports both plain text and HTML email formats</li>
                    <li><strong>Optional templates</strong>: Provides reusable templates for common incident-related communications</li>
                    <li><strong>Testing framework</strong>: Includes test cases for various scenarios including approval, rejection, and error conditions</li>
                </ul>
                
                <p>To integrate the email tool with your Incident PIR RAG application:</p>
                
                <ol>
                    <li>Save the <code>email_tool.py</code> file in your project directory</li>
                    <li>Import and use <code>get_send_email_tool()</code> when setting up your agent's tools</li>
                    <li>Optionally add the email templates for enhanced functionality</li>
                    <li>Run the test cases to verify the tool works correctly</li>
                </ol>
                
                <p>Since this is a stub implementation, it doesn't actually send emails but logs all the details as if it did. In a production implementation, you would replace the <code>_handle_approved_email</code> method with code that sends real emails using a library like <code>smtplib</code> or a service like SendGrid or AWS SES.</p>
            </div>
        </div>
    </section>

    <!-- Component 8: End-to-End Workflow Testing -->
    <section id="end-to-end-testing" class="mb-16">
        <h2 class="text-2xl font-bold text-blue-800 mb-4">8. End-to-End Workflow Testing</h2>
        
        <div class="prompt-container">
            <div class="prompt-header">AI IDE Prompt</div>
            <div class="prompt-content">
                <p><strong>Task:</strong> Create a comprehensive end-to-end testing framework for the incident PIR RAG application.</p>
                
                <h4>Context:</h4>
                <p>I'm building an incident PIR RAG application using LangChain, LlamaIndex, and Chainlit. I need a testing framework to verify that all components work together correctly. The application loads incident PIR reports from Confluence, indexes them using a custom embedding model, answers user queries using a ReAct agent with a custom reasoning model, and can take actions like sending emails (using a stub implementation).</p>
                
                <h4>Requirements:</h4>
                <ul>
                    <li>Create a comprehensive testing framework covering all application components</li>
                    <li>Generate realistic test data that simulates Confluence PIR pages</li>
                    <li>Test the indexing and embedding process</li>
                    <li>Test the query retrieval functionality with various query types</li>
                    <li>Test the ReAct agent's reasoning and decision-making</li>
                    <li>Test the email tool functionality</li>
                    <li>Include both unit tests and integration tests</li>
                    <li>Use pytest as the testing framework</li>
                    <li>Implement proper mocking for external dependencies (Confluence API, LLM APIs)</li>
                    <li>Generate test reports and coverage metrics</li>
                </ul>
                
                <h4>Test Scenarios to Cover:</h4>
                <ol>
                    <li>Loading and processing Confluence pages</li>
                    <li>Generating and storing embeddings</li>
                    <li>Retrieving relevant information for different query types:</li>
                    <ul>
                        <li>Incident metadata queries (ID, date, impact)</li>
                        <li>Root cause analysis queries</li>
                        <li>Timeline queries</li>
                        <li>Action item queries</li>
                        <li>Cross-incident comparison queries</li>
                    </ul>
                    <li>ReAct agent decision-making processes</li>
                    <li>Email tool functionality with user approval/rejection</li>
                    <li>End-to-end workflow from query to response</li>
                    <li>Error handling and edge cases</li>
                </ol>
                
                <h4>Expected Output:</h4>
                <ul>
                    <li>A complete test framework with unit tests and integration tests</li>
                    <li>Mock data generator for test incident PIR pages</li>
                    <li>Configurability for running different test subsets</li>
                    <li>CI/CD compatibility for automated testing</li>
                </ul>
                
                <p>Please provide a well-structured testing implementation that I can use to validate my application's functionality before deployment.</p>
            </div>
        </div>
        
        <div class="implementation-container">
            <div class="implementation-header">Implementation Details</div>
            <div class="implementation-content">
                <h3>End-to-End Testing Framework</h3>
                
                <p>We'll create a comprehensive testing framework that covers all aspects of the incident PIR RAG application. This includes test data generation, component testing, and end-to-end workflow validation.</p>
                
                <h4>1. Project Structure for Testing</h4>
                
                <div class="code-block">
project_root/
├── app.py                  # Main application file
├── requirements.txt        # Project dependencies
├── src/                    # Source code directory
│   ├── confluence_loader.py    # Confluence data loader
│   ├── embeddings.py           # Custom embedding functions
│   ├── retriever.py            # Retrieval functionality
│   ├── agent_config.py         # ReAct agent configuration
│   ├── email_tool.py           # Email tool implementation
│   └── utils/                  # Utility functions
├── tests/                  # Test directory
│   ├── conftest.py             # Pytest configuration and fixtures
│   ├── test_data/              # Test data directory
│   │   ├── mock_pir_pages/     # Mock PIR HTML pages
│   │   └── sample_queries.json # Sample test queries
│   ├── test_confluence.py      # Tests for Confluence loader
│   ├── test_embeddings.py      # Tests for embedding functionality
│   ├── test_retriever.py       # Tests for retrieval functionality
│   ├── test_agent.py           # Tests for ReAct agent
│   ├── test_email_tool.py      # Tests for email tool
│   ├── test_integration.py     # Integration tests
│   └── test_e2e.py             # End-to-end tests
└── pytest.ini              # Pytest configuration file
                </div>
                
                <h4>2. Test Data Generator</h4>
                
                <div class="code-block">
Copy# tests/conftest.py (part 1 - test data generation)
import os
import json
import random
import pytest
from datetime import datetime, timedelta
from typing import List, Dict, Any

def generate_incident_id() -> str:
    """Generate a random incident ID."""
    return f"IN{random.randint(100000, 999999)}"

def generate_date(start_date=None, end_date=None) -> str:
    """Generate a random date in YYYYMMDD format."""
    if not start_date:
        start_date = datetime(2023, 1, 1)
    if not end_date:
        end_date = datetime.now()
    
    time_between_dates = end_date - start_date
    days_between_dates = time_between_dates.days
    random_days = random.randrange(days_between_dates)
    random_date = start_date + timedelta(days=random_days)
    
    return random_date.strftime("%Y%m%d")

def generate_time() -> str:
    """Generate a random time in HH:MM format."""
    hours = random.randint(0, 23)
    minutes = random.randint(0, 59)
    return f"{hours:02d}:{minutes:02d}"

def generate_team_name() -> str:
    """Generate a random team name."""
    teams = ["Platform Team", "API Services", "Frontend Team", "Database Team", 
             "Infrastructure Team", "Security Team", "DevOps Team", "Mobile Team"]
    return random.choice(teams)

def generate_subject() -> str:
    """Generate a random incident subject."""
    systems = ["API", "Database", "Frontend", "App", "Server", "Network", "Authentication",
               "Payment", "Storage", "Cache", "Load Balancer", "Kubernetes Cluster"]
    issues = ["Outage", "Degradation", "Latency", "Error", "Failure", "Corruption", 
              "Timeout", "Overload", "Security Breach", "Data Loss", "Connectivity Issue"]
    
    return f"{random.choice(systems)} {random.choice(issues)}"

def generate_impact_level() -> str:
    """Generate a random impact level."""
    impacts = ["High", "Medium", "Low"]
    weights = [0.2, 0.5, 0.3]  # More medium impacts than high or low
    return random.choices(impacts, weights=weights)[0]

def generate_regions() -> List[str]:
    """Generate a list of affected regions."""
    all_regions = ["APAC", "EMEA", "NA", "SA", "Global"]
    num_regions = random.randint(1, 3)
    if "Global" in random.sample(all_regions, 1):
        return ["Global"]
    else:
        return random.sample(all_regions[:-1], num_regions)

def generate_applications() -> List[str]:
    """Generate a list of affected applications."""
    all_apps = ["Frontend Portal", "Customer API", "Admin Dashboard", "Mobile App", 
                "Payment Service", "Authentication Service", "Data Warehouse", 
                "Analytics Platform", "User Management", "Content Delivery System"]
    num_apps = random.randint(1, 3)
    return random.sample(all_apps, num_apps)

def generate_timeline_events() -> Dict[str, Dict[str, str]]:
    """Generate timeline events for an incident."""
    # Base start time for the incident
    incident_date = datetime.now() - timedelta(days=random.randint(1, 30))
    
    # Start time (some random time on the incident date)
    start_hour = random.randint(0, 23)
    start_minute = random.randint(0, 59)
    start_time = incident_date.replace(hour=start_hour, minute=start_minute)
    
    # Detection time (5-30 minutes after start)
    detection_time = start_time + timedelta(minutes=random.randint(5, 30))
    
    # Fix time (30-120 minutes after detection)
    fix_time = detection_time + timedelta(minutes=random.randint(30, 120))
    
    # Recovery time (10-60 minutes after fix)
    recovery_time = fix_time + timedelta(minutes=random.randint(10, 60))
    
    return {
        "Start Time": {
            "Timeline": start_time.strftime("%H:%M"),
            "Details": "Issue began affecting customers"
        },
        "Detected Time": {
            "Timeline": detection_time.strftime("%H:%M"),
            "Details": f"Issue detected via {'alerts' if random.random() > 0.5 else 'customer reports'}"
        },
        "Fixed Time": {
            "Timeline": fix_time.strftime("%H:%M"),
            "Details": "Fix implemented and deployed"
        },
        "Fully Recovery Time": {
            "Timeline": recovery_time.strftime("%H:%M"),
            "Details": "Service fully restored and stable"
        }
    }

def generate_business_impact() -> Dict[str, Any]:
    """Generate business impact information."""
    impact_types = [
        "Customer transactions failed",
        "Users unable to log in",
        "Slow response times",
        "Data inconsistency",
        "Payment processing delays",
        "Feature unavailability",
        "API timeouts",
        "Incorrect data displayed"
    ]
    
    selected_impacts = random.sample(impact_types, random.randint(1, 3))
    impact_description = ". ".join(selected_impacts) + "."
    
    service_outage = random.random() < 0.7  # 70% chance of service outage
    outage_type = random.choice(["Full", "Degraded"]) if service_outage else ""
    sow_added = random.random() < 0.9  # 90% chance of being added to SOW
    
    return {
        "Impact Description": impact_description,
        "Service Outage": "Yes" if service_outage else "No",
        "Outage Type": outage_type,
        "Added to SOW": "Yes" if sow_added else "No"
    }

def generate_change_info() -> Dict[str, str]:
    """Generate change-related information."""
    change_related = random.random() < 0.4  # 40% chance of being change-related
    
    if not change_related:
        return {
            "Change Related": "No",
            "Change Number": "",
            "Change Sub Type": "",
            "Linked in SOW": ""
        }
    
    return {
        "Change Related": "Yes",
        "Change Number": f"CHG{random.randint(100000, 999999)}",
        "Change Sub Type": random.choice(["DevOps", "Normal"]),
        "Linked in SOW": "Yes" if random.random() < 0.9 else "No"
    }

def generate_root_cause() -> Dict[str, str]:
    """Generate root cause information."""
    root_causes = [
        "Configuration error in load balancer settings",
        "Database connection pool exhaustion",
        "Memory leak in application code",
        "Network partition between data centers",
        "Third-party API dependency failure",
        "Storage capacity threshold reached",
        "Insufficient resource allocation",
        "Software deployment error",
        "Hardware failure in primary server",
        "Cache invalidation issue"
    ]
    
    cause = random.choice(root_causes)
    details = f"Investigation revealed that {cause.lower()}. This caused the system to experience degraded performance and eventually fail."
    completed = random.random() < 0.9  # 90% chance of RCA being completed
    
    return {
        "Root Cause": cause,
        "Root Cause Analysis Detail": details,
        "Status": "Completed" if completed else "In Progress"
    }

def generate_actions() -> List[Dict[str, str]]:
    """Generate action items for the incident."""
    potential_actions = [
        {
            "Root Cause Fix": "Update configuration",
            "Action": "Revise and update load balancer configuration settings",
            "Status": "Completed",
            "Jira": f"JIRA-{random.randint(1000, 9999)}"
        },
        {
            "Root Cause Fix": "Increase capacity",
            "Action": "Increase connection pool size and add monitoring alert",
            "Status": "Completed",
            "Jira": f"JIRA-{random.randint(1000, 9999)}"
        },
        {
            "Root Cause Fix": "Fix code issue",
            "Action": "Identify and patch memory leak in application code",
            "Status": "In Progress",
            "Jira": f"JIRA-{random.randint(1000, 9999)}"
        },
        {
            "Root Cause Fix": "Improve redundancy",
            "Action": "Implement cross-region failover capability",
            "Status": "In Progress",
            "Jira": f"JIRA-{random.randint(1000, 9999)}"
        },
        {
            "Root Cause Fix": "Add monitoring",
            "Action": "Enhance monitoring for early detection of similar issues",
            "Status": "Planned",
            "Jira": f"JIRA-{random.randint(1000, 9999)}"
        },
        {
            "Root Cause Fix": "Update documentation",
            "Action": "Update runbooks with new troubleshooting steps",
            "Status": "Completed",
            "Jira": f"JIRA-{random.randint(1000, 9999)}"
        }
    ]
    
    num_actions = random.randint(2, 4)
    return random.sample(potential_actions, num_actions)

def generate_mock_pir_html(incident_data: Dict[str, Any]) -> str:
    """Generate a mock PIR HTML page from incident data."""
    html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Incident PIR: {incident_id}</title>
</head>
<body>
    <h2>{date} - {incident_id} - {team} - {subject}</h2>
    
    <table>
        <tr><td>Reference</td><td>Details</td></tr>
        <tr><td>Impacted Application(s)</td><td>{applications}</td></tr>
        <tr><td>Impacted Regions</td><td>{regions}</td></tr>
        <tr><td>Incident Number</td><td>{incident_id}</td></tr>
        <tr><td>Incident Ticket</td><td>INC{ticket_number}</td></tr>
        <tr><td>Impact</td><td>{impact}</td></tr>
        <tr><td>Problem Ticket</td><td>PRB{problem_ticket}</td></tr>
        <tr><td>Problem Owner</td><td>{problem_owner}</td></tr>
        <tr><td>Post Incident Review Status</td><td>{pir_status}</td></tr>
    </table>
    
    <h2>Incident Summary</h2>
    
    <p>{summary}</p>
    
    <h2>Incident Timeline</h2>
    
    <p>Date: {date}</p>
    
    <p>Timezone: {timezone}</p>
    
    <table>
        <tr><td>Incident Detail</td><td>Timeline</td><td>Details</td></tr>
        {timeline_rows}
    </table>
    
    <h2>Business Impact Summary</h2>
    
    <table>
        <tr><td>Actual Business Impact Summary</td><td>Service Outage</td><td>If "Yes", Full or Degraded Outage</td><td>If "Yes", added in SOW?</td></tr>
        <tr><td>{business_impact}</td><td>{service_outage}</td><td>{outage_type}</td><td>{sow_added}</td></tr>
    </table>
    
    <h2>Was it Change Related?</h2>
    
    <table>
        <tr><td>Change Related?</td><td>If "Yes", what's the Change number</td><td>If "Yes", what's Change Sub Type</td><td>If "Yes", is it linked with Incident in SOW</td></tr>
        <tr><td>{change_related}</td><td>{change_number}</td><td>{change_sub_type}</td><td>{change_linked}</td></tr>
    </table>
    
    <h2>Root Cause</h2>
    
    <table>
        <tr><td>Root Cause Analysis</td><td>Root Cause Analysis Detail</td><td>Status (Completed or not)</td></tr>
        <tr><td>{root_cause}</td><td>{root_cause_detail}</td><td>{root_cause_status}</td></tr>
    </table>
    
    <h2>Actions Arising</h2>
    
    <p>Has an interim work-around been established? Actions as improvement, as well as how to address any lessons learned</p>
    
    <table>
        <tr><td>Root Cause Fix</td><td>Action</td><td>Status (Completed or not)</td><td>Jira (optional)</td></tr>
        {action_rows}
    </table>
    
    <h2>Labels</h2>
    
    <p>{labels}</p>
</body>
</html>
    """
    
    # Format timeline rows
    timeline_rows = ""
    for event_type, event_data in incident_data["timeline"].items():
        timeline_rows += f"<tr><td>{event_type}</td><td>{event_data['Timeline']}</td><td>{event_data['Details']}</td></tr>\n"
    
    # Format action rows
    action_rows = ""
    for action in incident_data["actions"]:
        action_rows += f"<tr><td>{action['Root Cause Fix']}</td><td>{action['Action']}</td><td>{action['Status']}</td><td>{action.get('Jira', '')}</td></tr>\n"
    
    # Create a summary
    summary = f"On {incident_data['date']}, an incident occurred affecting {', '.join(incident_data['applications'])} in the {', '.join(incident_data['regions'])} region(s). "
    summary += f"The issue was a {incident_data['subject'].lower()} with {incident_data['impact'].lower()} impact. "
    summary += f"The incident was detected at {incident_data['timeline']['Detected Time']['Timeline']} and fully resolved at {incident_data['timeline']['Fully Recovery Time']['Timeline']}. "
    summary += f"Root cause was identified as {incident_data['root_cause']['Root Cause'].lower()}."
    
    # Generate random problem owner
    first_names = ["John", "Sarah", "Michael", "Emma", "David", "Lisa", "Robert", "Jennifer"]
    last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Miller", "Davis", "Garcia"]
    problem_owner = f"{random.choice(first_names)} {random.choice(last_names)}"
    
    # Generate random labels
    potential_labels = ["production", "customer-facing", "backend", "frontend", "database", 
                        "network", "security", "performance", "third-party", "infrastructure",
                        "configuration", "deployment", "monitoring", "authentication"]
    
    labels = ", ".join(random.sample(potential_labels, random.randint(3, 5)))
    
    # Fill template with data
    html_content = html_template.format(
        date=incident_data["date"],
        incident_id=incident_data["incident_id"],
        team=incident_data["team"],
        subject=incident_data["subject"],
        applications=", ".join(incident_data["applications"]),
        regions=", ".join(incident_data["regions"]),
        ticket_number=str(random.randint(100000, 999999)),
        impact=incident_data["impact"],
        problem_ticket=str(random.randint(100000, 999999)),
        problem_owner=problem_owner,
        pir_status="Done" if random.random() < 0.8 else "In Progress",
        summary=summary,
        timezone=random.choice(["GMT", "HKT", "IST"]),
        timeline_rows=timeline_rows,
        business_impact=incident_data["business_impact"]["Impact Description"],
        service_outage=incident_data["business_impact"]["Service Outage"],
        outage_type=incident_data["business_impact"]["Outage Type"],
        sow_added=incident_data["business_impact"]["Added to SOW"],
        change_related=incident_data["change_info"]["Change Related"],
        change_number=incident_data["change_info"]["Change Number"],
        change_sub_type=incident_data["change_info"]["Change Sub Type"],
        change_linked=incident_data["change_info"]["Linked in SOW"],
        root_cause=incident_data["root_cause"]["Root Cause"],
        root_cause_detail=incident_data["root_cause"]["Root Cause Analysis Detail"],
        root_cause_status=incident_data["root_cause"]["Status"],
        action_rows=action_rows,
        labels=labels
    )
    
    return html_content

def generate_mock_pir_data(num_incidents: int = 10) -> List[Dict[str, Any]]:
    """Generate mock PIR data for testing."""
    incidents = []
    
    for _ in range(num_incidents):
        incident_data = {
            "date": generate_date(),
            "incident_id": generate_incident_id(),
            "team": generate_team_name(),
            "subject": generate_subject(),
            "applications": generate_applications(),
            "regions": generate_regions(),
            "impact": generate_impact_level(),
            "timeline": generate_timeline_events(),
            "business_impact": generate_business_impact(),
            "change_info": generate_change_info(),
            "root_cause": generate_root_cause(),
            "actions": generate_actions()
        }
        incidents.append(incident_data)
    
    return incidents

@pytest.fixture(scope="session")
def mock_pir_pages(tmp_path_factory) -> str:
    """Fixture that generates mock PIR HTML pages and returns the directory path."""
    # Create a temporary directory for the mock PIR pages
    mock_dir = tmp_path_factory.mktemp("mock_pir_pages")
    
    # Generate mock incident data
    incidents = generate_mock_pir_data(num_incidents=20)  # Generate 20 mock incidents
    
    # Create HTML files for each incident
    for incident in incidents:
        html_content = generate_mock_pir_html(incident)
        filename = f"{incident['date']}_{incident['incident_id']}.html"
        file_path = mock_dir / filename
        
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(html_content)
    
    # Create a JSON file with incident metadata for easier testing
    metadata_file = mock_dir / "incidents_metadata.json"
    with open(metadata_file, "w", encoding="utf-8") as f:
        json.dump(incidents, f, indent=2)
    
    return str(mock_dir)

@pytest.fixture(scope="session")
def sample_queries() -> List[Dict[str, str]]:
    """Fixture providing sample test queries for different types of information."""
    return [
        {"type": "metadata", "query": "What was the incident number and impact level for the database outage?"},
        {"type": "timeline", "query": "How long did it take to detect and fix incident IN123456?"},
        {"type": "root_cause", "query": "What was the root cause of the API failure in APAC region?"},
        {"type": "actions", "query": "What actions were taken to prevent recurrence of the Frontend Portal incident?"},
        {"type": "change", "query": "Was the authentication service outage related to a change?"},
        {"type": "impact", "query": "What was the business impact of the payment service degradation?"},
        {"type": "comparison", "query": "Compare the resolution times of the last three incidents affecting the Mobile App"},
        {"type": "email", "query": "Send an email to the team about the recent database outage root cause"},
        {"type": "complex", "query": "Find all high impact incidents in the last month related to API services and summarize their root causes"},
        {"type": "invalid", "query": "What's the weather like today?"}
    ]
                </div>
                
                <h4>3. Mocking External Services</h4>
                
                <div class="code-block">
Copy# tests/conftest.py (part 2 - mocking services)
import pytest
from unittest.mock import MagicMock, patch
import numpy as np

class MockConfluenceClient:
    """Mock Confluence client for testing."""
    
    def __init__(self, mock_pages_dir):
        self.mock_pages_dir = mock_pages_dir
        self.pages = []
        
        # Load mock page metadata
        metadata_file = os.path.join(mock_pages_dir, "incidents_metadata.json")
        with open(metadata_file, "r", encoding="utf-8") as f:
            self.incident_metadata = json.load(f)
        
        # Create page objects from the metadata
        for incident in self.incident_metadata:
            page_id = f"{incident['date']}_{incident['incident_id']}"
            title = f"Incident PIR: {incident['incident_id']} - {incident['subject']}"
            
            page = {
                "id": page_id,
                "title": title,
                "space": {"key": "INCIDENTS"},
                "metadata": incident
            }
            
            self.pages.append(page)
    
    def get_page_by_id(self, page_id):
        """Get a page by ID."""
        for page in self.pages:
            if page["id"] == page_id:
                return page
        return None
    
    def get_pages_by_space(self, space_key, limit=None):
        """Get pages by space key."""
        space_pages = [page for page in self.pages if page["space"]["key"] == space_key]
        if limit:
            return space_pages[:limit]
        return space_pages
    
    def get_page_content(self, page_id):
        """Get the HTML content of a page."""
        file_path = os.path.join(self.mock_pages_dir, f"{page_id}.html")
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except FileNotFoundError:
            return "<html><body><p>Page not found</p></body></html>"

class MockEmbeddingModel:
    """Mock embedding model for testing."""
    
    def __init__(self, embedding_dimensions=384):
        self.embedding_dimensions = embedding_dimensions
    
    def embed_documents(self, texts):
        """Generate mock embeddings for a list of documents."""
        return [self._generate_embedding() for _ in texts]
    
    def embed_query(self, text):
        """Generate mock embedding for a query."""
        return self._generate_embedding()
    
    def _generate_embedding(self):
        """Generate a random embedding vector."""
        return np.random.normal(0, 1, self.embedding_dimensions).tolist()

class MockRagCustomLLM:
    """Mock reasoning LLM for testing."""
    
    def __init__(self):
        # Load sample responses for different query types
        self.sample_responses = {
            "metadata": "Based on the information provided, incident IN456789 was a high impact Database Outage that occurred on ******** affecting the APAC region.",
            "timeline": "The incident was detected at 14:23, 18 minutes after it started at 14:05. The fix was implemented at 15:47 and full recovery was achieved at 16:12, resulting in a total resolution time of 1 hour and 49 minutes.",
            "root_cause": "The root cause of the API failure in the APAC region was identified as a configuration error in load balancer settings. This caused improper request routing and eventual service degradation.",
            "actions": "To prevent recurrence of the Frontend Portal incident, the following actions were taken: 1) Updated load balancer configuration, 2) Implemented enhanced monitoring for early detection, 3) Updated runbooks with new troubleshooting steps. All actions have been completed except for the monitoring enhancement which is in progress.",
            "change": "Yes, the authentication service outage was related to change CHG456789. It was a DevOps change type and has been linked in the SOW.",
            "impact": "The business impact of the payment service degradation included failed customer transactions and payment processing delays. It was classified as a degraded service outage and was added to the SOW.",
            "comparison": "<think>Analyzing the three most recent Mobile App incidents...\n\nIncident 1: IN345678\n- Resolution time: 1 hour 32 minutes\n- Root cause: Memory leak\n\nIncident 2: IN567890\n- Resolution time: 2 hours 15 minutes\n- Root cause: Third-party API failure\n\nIncident 3: IN789012\n- Resolution time: 45 minutes\n- Root cause: Configuration error</think>\n\nComparing the resolution times of the three most recent Mobile App incidents:\n\n1. The most recent incident (IN789012) had the shortest resolution time at 45 minutes, caused by a configuration error.\n2. The second incident (IN345678) took 1 hour 32 minutes to resolve due to a memory leak issue.\n3. The oldest incident (IN567890) had the longest resolution time at 2 hours 15 minutes, caused by a third-party API failure.\n\nThis suggests that configuration-related issues are typically resolved faster than third-party dependencies or code-level problems.",
            "email": "I'll prepare an email about the database outage root cause. Here's what I'll send:\n\nTo: <EMAIL>\nCC: <EMAIL>\nSubject: Root Cause Analysis: Database Outage (IN456789)\n\nDear Team,\n\nFollowing our investigation of the recent database outage (IN456789), we have identified the root cause as a connection pool exhaustion issue triggered by an unexpected traffic spike.\n\nWould you like me to proceed with sending this email?",
            "complex": "<think>Searching for high impact incidents in the last month related to API services...\n\nFound 3 incidents:\n1. IN123456 - API Service Outage - 20230712\n2. IN234567 - API Authentication Failure - 20230723\n3. IN345678 - API Gateway Timeout - 20230728\n\nAnalyzing root causes:\n- IN123456: Third-party dependency failure\n- IN234567: Configuration error during deployment\n- IN345678: Resource exhaustion due to traffic spike</think>\n\nI found 3 high impact incidents in the last month related to API services:\n\n1. API Service Outage (IN123456) on July 12, 2023\n   Root Cause: Third-party dependency failure in the payment processing service\n\n2. API Authentication Failure (IN234567) on July 23, 2023\n   Root Cause: Configuration error introduced during deployment that affected token validation\n\n3. API Gateway Timeout (IN345678) on July 28, 2023\n   Root Cause: Resource exhaustion due to unexpected traffic spike\n\nSummary of root causes: The majority of high-impact API incidents were caused by either external dependencies or configuration/deployment issues. Only one was related to capacity/scaling problems.",
            "invalid": "I don't have access to current weather information as I'm designed to help with incident reports and related information. Is there something about an incident that you'd like to know?"
        }
    
    def generate(self, prompt, **kwargs):
        """Generate a response based on the prompt."""
        # Determine the type of query
        query_type = self._determine_query_type(prompt)
        
        # Get the appropriate response
        response = self.sample_responses.get(query_type, "I don't have enough information to answer that question based on the incident reports.")
        
        # Return mock response in OpenAI-like format
        return {
            "choices": [
                {
                    "message": {
                        "content": response
                    }
                }
            ]
        }
    
    def _determine_query_type(self, prompt):
        """Determine the type of query from the prompt."""
        prompt_lower = prompt.lower()
        
        if any(term in prompt_lower for term in ["incident number", "incident id", "impact level"]):
            return "metadata"
        elif any(term in prompt_lower for term in ["how long", "time to", "timeline", "detected", "fixed"]):
            return "timeline"
        elif any(term in prompt_lower for term in ["root cause", "why did", "what caused", "reason for"]):
            return "root_cause"
        elif any(term in prompt_lower for term in ["actions", "prevent", "fixed", "resolution", "remediation"]):
            return "actions"
        elif any(term in prompt_lower for term in ["change related", "was there a change"]):
            return "change"
        elif any(term in prompt_lower for term in ["business impact", "affect", "affected", "impact"]):
            return "impact"
        elif any(term in prompt_lower for term in ["compare", "comparison", "differences", "similarities"]):
            return "comparison"
        elif any(term in prompt_lower for term in ["email", "send", "notify", "notification"]):
            return "email"
        elif any(term in prompt_lower for term in ["find all", "summarize", "analyze", "last month"]):
            return "complex"
        else:
            return "invalid"

@pytest.fixture
def mock_confluence_client(mock_pir_pages):
    """Fixture that provides a mock Confluence client."""
    return MockConfluenceClient(mock_pir_pages)

@pytest.fixture
def mock_embedding_model():
    """Fixture that provides a mock embedding model."""
    return MockEmbeddingModel()

@pytest.fixture
def mock_rag_llm():
    """Fixture that provides a mock RAG LLM."""
    return MockRagCustomLLM()

@pytest.fixture
def mock_chainlit():
    """Fixture that mocks Chainlit functionality."""
    with patch('chainlit.Message') as mock_message, \
         patch('chainlit.AskUserMessage') as mock_ask:
        # Set up mock for AskUserMessage.send
        mock_ask_instance = MagicMock()
        mock_ask_instance.send = AsyncMock(return_value="yes")
        mock_ask.return_value = mock_ask_instance
        
        # Set up mock for Message.send
        mock_message_instance = MagicMock()
        mock_message_instance.send = AsyncMock()
        mock_message.return_value = mock_message_instance
        
        yield {
            "message": mock_message,
            "ask_user": mock_ask
        }
                </div>
                
                <h4>4. Component Tests</h4>
                
                <div class="code-block">
Copy# tests/test_confluence.py
import pytest
from unittest.mock import patch, MagicMock
from src.confluence_loader import ConfluenceLoader

def test_confluence_loader_initialization():
    """Test that the ConfluenceLoader initializes correctly."""
    loader = ConfluenceLoader(
        base_url="https://example.atlassian.net",
        username="<EMAIL>",
        api_token="test-token",
        space_key="INCIDENTS",
        home_page_id="12345"
    )
    
    assert loader.base_url == "https://example.atlassian.net"
    assert loader.space_key == "INCIDENTS"
    assert loader.home_page_id == "12345"

@pytest.mark.asyncio
async def test_confluence_loader_load_pages(mock_confluence_client):
    """Test loading pages from Confluence."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client):
        loader = ConfluenceLoader(
            base_url="https://example.atlassian.net",
            username="<EMAIL>",
            api_token="test-token",
            space_key="INCIDENTS",
            home_page_id="12345"
        )
        
        pages = await loader.load_pages()
        
        # Verify we got the pages
        assert len(pages) > 0
        # Verify structure of returned pages
        for page in pages:
            assert "id" in page
            assert "title" in page
            assert "content" in page
            assert "metadata" in page

@pytest.mark.asyncio
async def test_confluence_loader_process_html(mock_confluence_client):
    """Test processing HTML content from Confluence pages."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client):
        loader = ConfluenceLoader(
            base_url="https://example.atlassian.net",
            username="<EMAIL>",
            api_token="test-token",
            space_key="INCIDENTS",
            home_page_id="12345"
        )
        
        pages = await loader.load_pages()
        documents = loader.process_pages(pages)
        
        # Verify documents were created
        assert len(documents) > 0
        # Verify structure of documents
        for doc in documents:
            assert hasattr(doc, "page_content")
            assert hasattr(doc, "metadata")
            # Check that important metadata was extracted
            assert "incident_id" in doc.metadata
            assert "date" in doc.metadata
            assert "team" in doc.metadata
            assert "impact" in doc.metadata

@pytest.mark.asyncio
async def test_confluence_loader_error_handling():
    """Test error handling in Confluence loader."""
    # Mock client that raises exceptions
    mock_error_client = MagicMock()
    mock_error_client.get_pages_by_space.side_effect = Exception("Confluence API error")
    
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_error_client):
        loader = ConfluenceLoader(
            base_url="https://example.atlassian.net",
            username="<EMAIL>",
            api_token="test-token",
            space_key="INCIDENTS",
            home_page_id="12345"
        )
        
        # Should handle exception and return empty list
        with pytest.raises(Exception) as excinfo:
            pages = await loader.load_pages()
        
        assert "Confluence API error" in str(excinfo.value)

# tests/test_embeddings.py
import pytest
from unittest.mock import patch, MagicMock
import numpy as np
from src.embeddings import CustomEmbeddingModel

def test_custom_embedding_model_initialization():
    """Test that the CustomEmbeddingModel initializes correctly."""
    model = CustomEmbeddingModel(
        api_url="https://example.com/embeddings",
        api_key="test-api-key"
    )
    
    assert model.api_url == "https://example.com/embeddings"
    assert model.api_key == "test-api-key"

@pytest.mark.asyncio
async def test_custom_embedding_model_embed_documents(mock_embedding_model):
    """Test embedding documents with the custom model."""
    # Mock the API response
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "data": [
            {"embedding": mock_embedding_model.embed_documents(["test"])[0]}
        ]
    }
    mock_response.status_code = 200
    
    # Mock the aiohttp ClientSession
    mock_session = MagicMock()
    mock_session.__aenter__ = AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = AsyncMock(return_value=None)
    mock_session.post = AsyncMock(return_value=mock_response)
    
    with patch('aiohttp.ClientSession', return_value=mock_session):
        model = CustomEmbeddingModel(
            api_url="https://example.com/embeddings",
            api_key="test-api-key"
        )
        
        embeddings = await model.aembed_documents(["This is a test document"])
        
        # Verify the embeddings
        assert len(embeddings) > 0
        assert len(embeddings[0]) > 0  # Should have dimensions
        assert isinstance(embeddings[0], list)
        assert all(isinstance(x, (int, float)) for x in embeddings[0])  # Should contain numbers

@pytest.mark.asyncio
async def test_custom_embedding_model_embed_query(mock_embedding_model):
    """Test embedding a query with the custom model."""
    # Mock the API response
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "data": [
            {"embedding": mock_embedding_model.embed_query("test")}
        ]
    }
    mock_response.status_code = 200
    
    # Mock the aiohttp ClientSession
    mock_session = MagicMock()
    mock_session.__aenter__ = AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = AsyncMock(return_value=None)
    mock_session.post = AsyncMock(return_value=mock_response)
    
    with patch('aiohttp.ClientSession', return_value=mock_session):
        model = CustomEmbeddingModel(
            api_url="https://example.com/embeddings",
            api_key="test-api-key"
        )
        
        embedding = await model.aembed_query("What was the impact of the incident?")
        
        # Verify the embedding
        assert len(embedding) > 0
        assert isinstance(embedding, list)
        assert all(isinstance(x, (int, float)) for x in embedding)  # Should contain numbers

@pytest.mark.asyncio
async def test_custom_embedding_model_error_handling():
    """Test error handling in the custom embedding model."""
    # Mock a failed API response
    mock_response = MagicMock()
    mock_response.status_code = 500
    mock_response.text = "Internal Server Error"
    
    # Mock the aiohttp ClientSession
    mock_session = MagicMock()
    mock_session.__aenter__ = AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = AsyncMock(return_value=None)
    mock_session.post = AsyncMock(return_value=mock_response)
    
    with patch('aiohttp.ClientSession', return_value=mock_session):
        model = CustomEmbeddingModel(
            api_url="https://example.com/embeddings",
            api_key="test-api-key"
        )
        
        with pytest.raises(Exception) as excinfo:
            await model.aembed_documents(["This is a test document"])
        
        assert "500" in str(excinfo.value)
        assert "Internal Server Error" in str(excinfo.value)

# tests/test_retriever.py (similar pattern for other component tests)
# Tests would cover:
# - Retriever initialization
# - Vector store creation
# - Query processing
# - Document retrieval
# - Metadata filtering
# - Section filtering
# - Table data extraction

# tests/test_agent.py
# Tests would cover:
# - Agent initialization
# - Tool registration
# - Query processing
# - Response generation
# - Error handling

# tests/test_email_tool.py
# Tests would cover:
# - Email tool initialization
# - Request validation
# - User approval flow
# - Success/failure scenarios
                </div>
                
                <h4>5. Integration Tests</h4>
                
                <div class="code-block">
Copy# tests/test_integration.py
import pytest
from unittest.mock import patch, MagicMock
import tempfile
import os
import json
from src.confluence_loader import ConfluenceLoader
from src.embeddings import CustomEmbeddingModel
from src.retriever import IncidentRetriever
from src.agent_config import create_agent
from src.email_tool import get_send_email_tool

@pytest.mark.asyncio
async def test_loading_and_indexing_flow(mock_confluence_client, mock_embedding_model):
    """Test the integration between the Confluence loader and indexing process."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # 1. Load pages from Confluence
            loader = ConfluenceLoader(
                base_url="https://example.atlassian.net",
                username="<EMAIL>",
                api_token="test-token",
                space_key="INCIDENTS",
                home_page_id="12345"
            )
            
            pages = await loader.load_pages()
            documents = loader.process_pages(pages)
            
            # 2. Create embedding model
            embedding_model = CustomEmbeddingModel(
                api_url="https://example.com/embeddings",
                api_key="test-api-key"
            )
            
            # 3. Initialize retriever and index documents
            retriever = IncidentRetriever(
                embedding_model=embedding_model,
                persist_directory=temp_dir
            )
            
            await retriever.index_documents(documents)
            
            # 4. Verify vector store was created
            assert os.path.exists(temp_dir)
            assert len(os.listdir(temp_dir)) > 0  # Should have created files
            
            # 5. Test a query
            results = await retriever.get_relevant_documents("What was the root cause of the database outage?")
            
            # 6. Verify results
            assert len(results) > 0
            for doc in results:
                assert hasattr(doc, "page_content")
                assert hasattr(doc, "metadata")

@pytest.mark.asyncio
async def test_retrieval_and_reasoning_flow(mock_embedding_model, mock_rag_llm):
    """Test the integration between the retriever and reasoning LLM."""
    with patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query), \
         patch('src.agent_config.get_reasoning_llm', return_value=mock_rag_llm):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create pre-loaded retriever with mock data
            mock_docs = [
                Document(
                    page_content="The root cause of the database outage was identified as a connection pool exhaustion issue.",
                    metadata={"incident_id": "IN123456", "impact": "High", "section": "Root Cause"}
                ),
                Document(
                    page_content="Actions taken: 1) Increased connection pool size, 2) Added monitoring alerts, 3) Updated documentation.",
                    metadata={"incident_id": "IN123456", "impact": "High", "section": "Actions Arising"}
                )
            ]
            
            # Initialize components
            embedding_model = CustomEmbeddingModel(
                api_url="https://example.com/embeddings",
                api_key="test-api-key"
            )
            
            retriever = IncidentRetriever(
                embedding_model=embedding_model,
                persist_directory=temp_dir
            )
            
            await retriever.index_documents(mock_docs)
            
            # Create agent with retriever
            agent = create_agent(mock_rag_llm, retriever)
            
            # Test a query
            response = await agent.arun("What was the root cause of the database outage?")
            
            # Verify response
            assert response
            assert "connection pool exhaustion" in response.lower()

@pytest.mark.asyncio
async def test_agent_email_tool_integration(mock_embedding_model, mock_rag_llm, mock_chainlit):
    """Test the integration between the agent and email tool."""
    with patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query), \
         patch('src.agent_config.get_reasoning_llm', return_value=mock_rag_llm):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create pre-loaded retriever with mock data
            mock_docs = [
                Document(
                    page_content="The root cause of the database outage was identified as a connection pool exhaustion issue.",
                    metadata={"incident_id": "IN123456", "impact": "High", "section": "Root Cause"}
                ),
                Document(
                    page_content="The Database Team is responsible for maintaining the database systems.",
                    metadata={"incident_id": "IN123456", "team": "Database Team", "section": "Reference"}
                )
            ]
            
            # Initialize components
            embedding_model = CustomEmbeddingModel(
                api_url="https://example.com/embeddings",
                api_key="test-api-key"
            )
            
            retriever = IncidentRetriever(
                embedding_model=embedding_model,
                persist_directory=temp_dir
            )
            
            await retriever.index_documents(mock_docs)
            
            # Get email tool
            email_tool = get_send_email_tool()
            
            # Mock agent tools
            tools = [
                Tool(
                    name="SearchIncidentReports",
                    func=retriever.get_relevant_documents,
                    description="Search for information in incident reports."
                ),
                email_tool
            ]
            
            # Create agent with tools
            agent = create_agent(mock_rag_llm, tools)
            
            # Test an email request
            response = await agent.arun("Send an email to the Database Team about the root cause of the recent outage")
            
            # Verify Chainlit ask was called
            assert mock_chainlit["ask_user"].called
            
            # Verify response indicates email was sent
            assert "email" in response.lower()
            assert "sent" in response.lower() or "approved" in response.lower()
                </div>
                
                <h4>6. End-to-End Tests</h4>
                
                <div class="code-block">
Copy# tests/test_e2e.py
import pytest
from unittest.mock import patch, MagicMock
import tempfile
import os
import json
import asyncio
from src.app import initialize_app, process_query

@pytest.mark.asyncio
async def test_app_initialization(mock_confluence_client, mock_embedding_model):
    """Test the complete application initialization process."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize the application
            app_config = {
                "confluence": {
                    "base_url": "https://example.atlassian.net",
                    "username": "<EMAIL>",
                    "api_token": "test-token",
                    "space_key": "INCIDENTS",
                    "home_page_id": "12345"
                },
                "embedding": {
                    "api_url": "https://example.com/embeddings",
                    "api_key": "test-api-key"
                },
                "reasoning": {
                    "api_url": "https://example.com/reasoning",
                    "api_key": "test-api-key"
                },
                "vector_store": {
                    "directory": temp_dir
                }
            }
            
            app_components = await initialize_app(app_config)
            
            # Verify all components were initialized
            assert app_components["loader"]
            assert app_components["embedding_model"]
            assert app_components["retriever"]
            assert app_components["agent"]

@pytest.mark.asyncio
async def test_end_to_end_query_flow(mock_confluence_client, mock_embedding_model, mock_rag_llm, mock_chainlit):
    """Test the complete query flow from user input to response."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query), \
         patch('src.agent_config.get_reasoning_llm', return_value=mock_rag_llm):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize the application
            app_config = {
                "confluence": {
                    "base_url": "https://example.atlassian.net",
                    "username": "<EMAIL>",
                    "api_token": "test-token",
                    "space_key": "INCIDENTS",
                    "home_page_id": "12345"
                },
                "embedding": {
                    "api_url": "https://example.com/embeddings",
                    "api_key": "test-api-key"
                },
                "reasoning": {
                    "api_url": "https://example.com/reasoning",
                    "api_key": "test-api-key"
                },
                "vector_store": {
                    "directory": temp_dir
                }
            }
            
            app_components = await initialize_app(app_config)
            
            # Test multiple query types
            test_queries = [
                "What was the impact of the database outage?",
                "When was incident IN123456 detected and resolved?",
                "What actions were taken to prevent recurrence of the API failure?",
                "Send an email to the team about the root cause of the recent incident"
            ]
            
            for query in test_queries:
                # Process the query
                response = await process_query(query, app_components)
                
                # Verify response
                assert response
                assert isinstance(response, str)
                assert len(response) > 0
                
                # If it's an email query, verify Chainlit interaction
                if "email" in query.lower():
                    assert mock_chainlit["ask_user"].called

@pytest.mark.asyncio
async def test_error_handling_and_recovery(mock_confluence_client, mock_embedding_model, mock_rag_llm):
    """Test the application's error handling and recovery mechanisms."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query), \
         patch('src.agent_config.get_reasoning_llm', return_value=mock_rag_llm):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize the application
            app_config = {
                "confluence": {
                    "base_url": "https://example.atlassian.net",
                    "username": "<EMAIL>",
                    "api_token": "test-token",
                    "space_key": "INCIDENTS",
                    "home_page_id": "12345"
                },
                "embedding": {
                    "api_url": "https://example.com/embeddings",
                    "api_key": "test-api-key"
                },
                "reasoning": {
                    "api_url": "https://example.com/reasoning",
                    "api_key": "test-api-key"
                },
                "vector_store": {
                    "directory": temp_dir
                }
            }
            
            app_components = await initialize_app(app_config)
            
            # Test with a failing retriever (simulate API error)
            with patch.object(app_components["retriever"], "get_relevant_documents", 
                             side_effect=Exception("Simulated retrieval error")):
                
                # Process a query
                response = await process_query("What was the impact of the database outage?", app_components)
                
                # Verify we got an error response
                assert response
                assert "error" in response.lower() or "unable" in response.lower()
                
            # Verify the app can recover and process the next query normally
            response = await process_query("What was the impact of the database outage?", app_components)
            assert response
            assert "error" not in response.lower()
            assert "unable" not in response.lower()

# Create a pytest.ini configuration to organize the tests
"""
# pytest.ini
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: unit tests
    integration: integration tests
    e2e: end-to-end tests
    slow: slow running tests
    confluence: tests related to Confluence integration
    embedding: tests related to embedding functionality
    retrieval: tests related to retrieval functionality
    agent: tests related to the agent
    email: tests related to the email tool
"""
                </div>
                
                <h4>7. Performance and Load Testing</h4>
                
                <div class="code-block">
Copy# tests/test_performance.py
import pytest
import time
import asyncio
import statistics
from unittest.mock import patch, MagicMock
import tempfile
import os
from src.app import initialize_app, process_query

@pytest.mark.performance
@pytest.mark.asyncio
async def test_retrieval_latency(mock_confluence_client, mock_embedding_model, mock_rag_llm):
    """Test the retrieval latency under different conditions."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query), \
         patch('src.agent_config.get_reasoning_llm', return_value=mock_rag_llm):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize the application
            app_config = {
                "confluence": {
                    "base_url": "https://example.atlassian.net",
                    "username": "<EMAIL>",
                    "api_token": "test-token",
                    "space_key": "INCIDENTS",
                    "home_page_id": "12345"
                },
                "embedding": {
                    "api_url": "https://example.com/embeddings",
                    "api_key": "test-api-key"
                },
                "reasoning": {
                    "api_url": "https://example.com/reasoning",
                    "api_key": "test-api-key"
                },
                "vector_store": {
                    "directory": temp_dir
                }
            }
            
            app_components = await initialize_app(app_config)
            
            # Test queries
            test_queries = [
                "What was the impact of the database outage?",
                "When was incident IN123456 detected and resolved?",
                "What actions were taken to prevent recurrence of the API failure?",
                "What was the root cause of the network issue?",
                "Was the recent incident change-related?"
            ]
            
            # Measure retrieval latency for each query
            retrieval_times = []
            
            for query in test_queries:
                start_time = time.time()
                documents = await app_components["retriever"].get_relevant_documents(query)
                end_time = time.time()
                
                retrieval_time = end_time - start_time
                retrieval_times.append(retrieval_time)
                
                print(f"Query: {query}")
                print(f"Retrieved {len(documents)} documents in {retrieval_time:.4f} seconds")
                
            # Calculate statistics
            avg_retrieval_time = statistics.mean(retrieval_times)
            max_retrieval_time = max(retrieval_times)
            min_retrieval_time = min(retrieval_times)
            
            print(f"Average retrieval time: {avg_retrieval_time:.4f} seconds")
            print(f"Maximum retrieval time: {max_retrieval_time:.4f} seconds")
            print(f"Minimum retrieval time: {min_retrieval_time:.4f} seconds")
            
            # Assert performance meets requirements
            assert avg_retrieval_time < 2.0  # Average retrieval time should be less than 2 seconds
            assert max_retrieval_time < 5.0  # Maximum retrieval time should be less than 5 seconds

@pytest.mark.performance
@pytest.mark.asyncio
async def test_concurrent_queries(mock_confluence_client, mock_embedding_model, mock_rag_llm):
    """Test the application's performance with concurrent queries."""
    with patch('src.confluence_loader.ConfluenceClient', return_value=mock_confluence_client), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query), \
         patch('src.agent_config.get_reasoning_llm', return_value=mock_rag_llm):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize the application
            app_config = {
                "confluence": {
                    "base_url": "https://example.atlassian.net",
                    "username": "<EMAIL>",
                    "api_token": "test-token",
                    "space_key": "INCIDENTS",
                    "home_page_id": "12345"
                },
                "embedding": {
                    "api_url": "https://example.com/embeddings",
                    "api_key": "test-api-key"
                },
                "reasoning": {
                    "api_url": "https://example.com/reasoning",
                    "api_key": "test-api-key"
                },
                "vector_store": {
                    "directory": temp_dir
                }
            }
            
            app_components = await initialize_app(app_config)
            
            # Test queries
            test_queries = [
                "What was the impact of the database outage?",
                "When was incident IN123456 detected and resolved?",
                "What actions were taken to prevent recurrence of the API failure?",
                "What was the root cause of the network issue?",
                "Was the recent incident change-related?"
            ]
            
            # Process queries concurrently
            start_time = time.time()
            tasks = [process_query(query, app_components) for query in test_queries]
            responses = await asyncio.gather(*tasks)
            end_time = time.time()
            
            # Calculate statistics
            total_time = end_time - start_time
            queries_per_second = len(test_queries) / total_time
            
            print(f"Processed {len(test_queries)} queries in {total_time:.4f} seconds")
            print(f"Queries per second: {queries_per_second:.4f}")
            
            # Verify all queries were successful
            for response in responses:
                assert response
                assert isinstance(response, str)
                assert len(response) > 0
            
            # Assert performance meets requirements
            assert total_time < 15.0  # All queries should complete within 15 seconds
            assert queries_per_second > 0.3  # Should process at least 0.3 queries per second (5 queries in 15 seconds)

@pytest.mark.performance
@pytest.mark.asyncio
async def test_large_document_indexing(mock_embedding_model):
    """Test indexing performance with a large number of documents."""
    # Generate a large number of mock documents
    num_documents = 100
    mock_docs = []
    
    for i in range(num_documents):
        doc = Document(
            page_content=f"This is test document {i} with some content about incidents and their resolution.",
            metadata={
                "incident_id": f"IN{100000 + i}",
                "date": f"2023{i % 12 + 1:02d}{i % 28 + 1:02d}",
                "team": f"Team {i % 5 + 1}",
                "impact": random.choice(["High", "Medium", "Low"]),
                "section": random.choice(["Root Cause", "Actions Arising", "Incident Summary", "Timeline"])
            }
        )
        mock_docs.append(doc)
    
    with patch('src.embeddings.CustomEmbeddingModel.aembed_documents', return_value=mock_embedding_model.embed_documents), \
         patch('src.embeddings.CustomEmbeddingModel.aembed_query', return_value=mock_embedding_model.embed_query):
        
        # Create temporary directory for vector store
        with tempfile.TemporaryDirectory() as temp_dir:
            # Initialize embedding model
            embedding_model = CustomEmbeddingModel(
                api_url="https://example.com/embeddings",
                api_key="test-api-key"
            )
            
            # Initialize retriever
            retriever = IncidentRetriever(
                embedding_model=embedding_model,
                persist_directory=temp_dir
            )
            
            # Measure indexing time
            start_time = time.time()
            await retriever.index_documents(mock_docs)
            end_time = time.time()
            
            indexing_time = end_time - start_time
            docs_per_second = num_documents / indexing_time
            
            print(f"Indexed {num_documents} documents in {indexing_time:.4f} seconds")
            print(f"Documents per second: {docs_per_second:.4f}")
            
            # Assert performance meets requirements
            assert docs_per_second > 5.0  # Should index at least 5 documents per second
                </div>
                
                <h4>Implementation Notes</h4>
                
                <p>The testing framework provides a comprehensive approach to validating all aspects of the incident PIR RAG application:</p>
                
                <ul>
                    <li><strong>Test Data Generation</strong>: Creates realistic mock PIR data with proper structure and relationships</li>
                    <li><strong>Component Testing</strong>: Validates each component's functionality in isolation</li>
                    <li><strong>Integration Testing</strong>: Verifies that components work together correctly</li>
                    <li><strong>End-to-End Testing</strong>: Tests the complete application workflow</li>
                    <li><strong>Performance Testing</strong>: Measures and validates performance metrics</li>
                    <li><strong>Mocking Services</strong>: Provides mock implementations of external dependencies</li>
                    <li><strong>Error Handling</strong>: Tests application behavior under error conditions</li>
                </ul>
                
                <p>To use this testing framework:</p>
                
                <ol>
                    <li>Create the directory structure as shown</li>
                    <li>Implement the test files for each component</li>
                    <li>Create the pytest.ini configuration file</li>
                    <li>Run tests individually using <code>pytest tests/test_filename.py</code></li>
                    <li>Run all tests using <code>pytest</code></li>
                    <li>Run specific test categories using markers, e.g., <code>pytest -m integration</code></li>
                </ol>
                
                <p>The testing framework is designed to be integrated with CI/CD pipelines, allowing for automated testing as part of the development process. It's also configurable to focus on specific areas of functionality or to run comprehensive test suites for full validation.</p>
            </div>
        </div>
    </section>

    <!-- Component 9: Project Requirements Document -->
    <section id="project-requirements-document" class="mb-16">
        <h2 class="text-2xl font-bold text-blue-800 mb-4">9. Project Requirements Document</h2>
        
        <div class="prompt-container">
            <div class="prompt-header">AI IDE Prompt</div>
            <div class="prompt-content">
                <p><strong>Task:</strong> Create a comprehensive Project Requirements Document (PRD) for the incident PIR RAG application.</p>
                
                <h4>Context:</h4>
                <p>I'm developing an incident PIR RAG application using Chainlit, LangChain ReAct agent, and LlamaIndex that will index Confluence pages containing incident reports, retrieve relevant information based on user queries, and potentially take actions like sending emails. I need a comprehensive PRD that covers all aspects of the project for stakeholder alignment and development guidance.</p>
                
                <h4>Requirements:</h4>
                <ul>
                    <li>Create a detailed PRD following standard software development documentation practices</li>
                    <li>Cover all aspects of the application including purpose, features, technical architecture, integrations, and limitations</li>
                    <li>Include clear descriptions of user workflows and interaction patterns</li>
                    <li>Detail technical components and their relationships</li>
                    <li>Outline implementation approach and development phases</li>
                    <li>Include mockups or diagrams where appropriate</li>
                    <li>Document non-functional requirements (performance, security, etc.)</li>
                    <li>Identify potential risks and mitigation strategies</li>
                    <li>Provide acceptance criteria for key features</li>
                </ul>
                
                <h4>Expected Output:</h4>
                <p>A comprehensive PRD in markdown format that covers all aspects of the incident PIR RAG application, suitable for technical and non-technical stakeholders.</p>
            </div>
        </div>
        
        <div class="implementation-container">
            <div class="implementation-header">Implementation Details</div>
            <div class="implementation-content">
                <h3>Project Requirements Document Template</h3>
                
                <p>Below is a comprehensive PRD template for the Incident PIR RAG application. This template follows standard software development documentation practices and covers all required aspects of the project.</p>
                
                <div class="code-block">
Copy# Project Requirements Document: Incident PIR RAG Application

## Document Control
**Document Version:** 1.0  
**Date:** YYYY-MM-DD  
**Author:** [Author Name]  
**Status:** Draft  

## Table of Contents
1. [Introduction](#1-introduction)
2. [Product Overview](#2-product-overview)
3. [Target Users](#3-target-users)
4. [User Workflows](#4-user-workflows)
5. [Feature Requirements](#5-feature-requirements)
6. [Technical Architecture](#6-technical-architecture)
7. [Integration Requirements](#7-integration-requirements)
8. [Non-Functional Requirements](#8-non-functional-requirements)
9. [Implementation Plan](#9-implementation-plan)
10. [Testing Strategy](#10-testing-strategy)
11. [Risks and Mitigations](#11-risks-and-mitigations)
12. [Glossary](#12-glossary)
13. [Appendices](#13-appendices)

## 1. Introduction

### 1.1 Purpose
This document outlines the requirements for the Incident Post-Implementation Review (PIR) Retrieval Augmented Generation (RAG) Application. This application is designed to enhance incident management processes by providing intelligent retrieval and analysis of incident reports stored in Confluence, allowing users to query information and potentially take actions based on the retrieved information.

### 1.2 Scope
The application will:
- Connect to Confluence to retrieve and index incident PIR reports
- Process and analyze the structure of these documents
- Enable natural language querying of incident information
- Provide relevant and accurate responses based on the indexed content
- Support specific actions like sending emails based on incident information
- Present information with explanations of reasoning

The application will not:
- Replace existing incident management tools
- Modify or create new PIR reports in Confluence
- Provide real-time incident monitoring or alerting
- Make autonomous decisions without user confirmation

### 1.3 Definitions and Acronyms
- **PIR**: Post-Implementation Review
- **RAG**: Retrieval Augmented Generation
- **LLM**: Large Language Model
- **ReAct**: Reasoning and Acting agent pattern
- **Confluence**: Atlassian's collaboration wiki software
- **Chainlit**: UI framework for LLM applications
- **LangChain**: Framework for building LLM applications
- **LlamaIndex**: Data framework for LLM applications

### 1.4 References
- Incident PIR Template Structure (as described in project specifications)
- LangChain Documentation: https://python.langchain.com/docs/
- LlamaIndex Documentation: https://docs.llamaindex.ai/
- Chainlit Documentation: https://docs.chainlit.io/

## 2. Product Overview

### 2.1 Product Vision
The Incident PIR RAG Application aims to revolutionize how teams access and utilize historical incident information by providing a natural language interface to quickly retrieve, analyze, and act upon relevant incident data. By leveraging advanced retrieval techniques specific to PIR document structures, the application enhances the organization's ability to learn from past incidents and improve incident response processes.

### 2.2 Product Goals
1. Reduce time spent searching for incident information by 75%
2. Improve accuracy of retrieved incident information compared to keyword searches
3. Enable cross-incident analysis and pattern recognition
4. Facilitate knowledge sharing across teams
5. Support compliance and audit requirements through comprehensive information retrieval
6. Enable action-taking based on incident information

### 2.3 Success Metrics
- Average query response time < 5 seconds
- Retrieval accuracy > 90% (as measured by relevance tests)
- User satisfaction rating > 4.5/5
- 70% reduction in time spent manually reviewing incident reports
- 80% of users report discovering new insights about incidents

## 3. Target Users

### 3.1 User Personas

#### 3.1.1 Incident Manager
- **Description**: Responsible for overseeing incident management processes and ensuring proper resolution and documentation
- **Goals**: Quickly access historical incident information, compare incidents, identify patterns, communicate with stakeholders
- **Pain Points**: Time-consuming manual searches, difficulty finding specific details, challenges in cross-incident analysis
- **Usage Patterns**: Regular use during incident reviews and planning meetings

#### 3.1.2 Technical Lead
- **Description**: Responsible for technical systems and resolving complex issues
- **Goals**: Find technical details of past incidents, understand root causes, verify action item implementation
- **Pain Points**: Difficulty finding specific technical information across many reports, time spent digging through documentation
- **Usage Patterns**: Focused use during incident investigations and technical planning

#### 3.1.3 Operations Team Member
- **Description**: Frontline staff handling day-to-day operations and incident response
- **Goals**: Learn from past incidents, find similar past issues, understand resolution approaches
- **Pain Points**: Limited knowledge of historical incidents, uncertainty about how similar issues were resolved
- **Usage Patterns**: Ad-hoc use when handling incidents or during training

#### 3.1.4 Executive Stakeholder
- **Description**: Upper management requiring high-level incident information
- **Goals**: Understand impact and patterns, get summary information, receive reports
- **Pain Points**: Too much technical detail, difficulty getting concise summaries
- **Usage Patterns**: Infrequent use, primarily for reports and summaries

### 3.2 User Needs
1. Natural language querying of incident information
2. Fast, accurate retrieval of relevant incident details
3. Structured presentation of results with clear source attribution
4. Ability to compare incidents and identify patterns
5. Support for communication actions (e.g., sending emails)
6. Explanation of reasoning and sourcing
7. Accessible interface requiring minimal training

## 4. User Workflows

### 4.1 Primary User Workflows

#### 4.1.1 Incident Information Retrieval
1. User accesses the application through web interface
2. User enters a natural language query about incident(s)
3. System processes query and retrieves relevant information
4. System presents structured response with citation of sources
5. User can ask follow-up questions to drill down further
6. User can collapse/expand reasoning details

**Example Query Flow**:
- User: "What was the root cause of the database outage on July 15, 2023?"
- System: [Retrieves and processes relevant PIR documents]
- System: "The root cause of the database outage on July 15, 2023 (incident IN123456) was identified as a connection pool exhaustion issue triggered by an unexpected traffic spike from a new feature release. This caused database connections to be exhausted faster than they could be released, eventually leading to timeouts and service degradation. [Source: PIR IN123456, Root Cause section]"

#### 4.1.2 Cross-Incident Analysis
1. User enters query comparing multiple incidents or requesting pattern analysis
2. System retrieves information across multiple incidents
3. System analyzes commonalities, differences, or patterns
4. System presents structured comparison/analysis with reasoning
5. User can drill down into specific aspects of the comparison

**Example Query Flow**:
- User: "Compare the resolution times of the last three payment service incidents"
- System: [Retrieves and processes relevant PIR documents]
- System: "Here's a comparison of resolution times for the three most recent payment service incidents:
  1. IN789012 (Aug 5, 2023): 45 minutes - Configuration error
  2. IN678901 (Jul 22, 2023): 1 hour 32 minutes - Memory leak
  3. IN567890 (Jun 10, 2023): 2 hours 15 minutes - Third-party API failure
  
  The data suggests that configuration-related issues are typically resolved faster than third-party dependencies or code-level problems. [Sources: PIR IN789012, IN678901, IN567890]"

#### 4.1.3 Action Initiation
1. User requests an action based on incident information (e.g., sending an email)
2. System retrieves relevant information to support the action
3. System generates action details (e.g., email content)
4. System presents action details for user approval
5. User approves or rejects the action
6. System executes the approved action and confirms completion

**Example Query Flow**:
- User: "Send an email to the database team about the root cause of the recent outage"
- System: [Retrieves relevant information and prepares email]
- System: "I'll prepare an email about the database outage root cause. Here's what I'll send:
  
  To: <EMAIL>
  CC: <EMAIL>
  Subject: Root Cause Analysis: Database Outage (IN456789)
  
  Dear Team,
  
  Following our investigation of the recent database outage (IN456789), we have identified the root cause as a connection pool exhaustion issue triggered by an unexpected traffic spike.
  
  [Email content continues...]
  
  Do you approve sending this email?"
- User: "Yes"
- System: "Email sent <NAME_EMAIL>"

### 4.2 Secondary User Workflows

#### 4.2.1 Application Initialization
1. Administrator configures Confluence connection details
2. Administrator initiates initial document loading and indexing
3. System retrieves PIR documents from Confluence
4. System processes and indexes documents
5. System confirms indexing completion and readiness

#### 4.2.2 Index Refresh
1. Administrator or scheduled job initiates index refresh
2. System retrieves new or updated PIR documents
3. System processes and indexes new/updated documents
4. System confirms refresh completion

## 5. Feature Requirements

### 5.1 Confluence Integration

#### 5.1.1 Confluence Connection
- **Description**: Connect to Confluence API to retrieve PIR documents
- **Priority**: High
- **Acceptance Criteria**:
  - Successfully authenticate with Confluence using provided credentials
  - Retrieve pages from specified Confluence space
  - Handle pagination for large result sets
  - Properly handle API rate limits
  - Log connection errors with appropriate details

#### 5.1.2 PIR Document Loading
- **Description**: Load PIR documents from Confluence and extract content
- **Priority**: High
- **Acceptance Criteria**:
  - Load HTML content of PIR pages
  - Extract page metadata (title, ID, last updated)
  - Handle different PIR document versions/formats
  - Process embedded tables and structured content
  - Support incremental loading of new/updated documents

### 5.2 Document Processing

#### 5.2.1 Structure-Aware Parsing
- **Description**: Parse PIR documents with awareness of their specific structure
- **Priority**: High
- **Acceptance Criteria**:
  - Identify and extract header information (date, incident ID, team, subject)
  - Recognize and extract section content (Summary, Timeline, Root Cause, etc.)
  - Process table structures and extract key-value pairs
  - Maintain relationships between document elements
  - Extract metadata fields for filtering

#### 5.2.2 Custom Embedding Generation
- **Description**: Generate embeddings for document chunks using custom embedding model
- **Priority**: High
- **Acceptance Criteria**:
  - Successfully call custom embedding API
  - Generate embeddings for document chunks
  - Handle API errors gracefully
  - Support batching for performance optimization
  - Cache embeddings to avoid redundant processing

### 5.3 Retrieval System

#### 5.3.1 Specialized Retrieval Strategy
- **Description**: Implement the specialized retrieval strategy for PIR documents
- **Priority**: High
- **Acceptance Criteria**:
  - Support metadata-based filtering
  - Implement section-aware retrieval
  - Support table-specific data extraction
  - Handle parent-child document relationships
  - Use multi-vector retrieval for improved accuracy
  - Implement hybrid retrieval (keyword + semantic)
  - Rerank results based on relevance criteria

#### 5.3.2 Query Processing
- **Description**: Process natural language queries and convert to appropriate retrieval parameters
- **Priority**: High
- **Acceptance Criteria**:
  - Extract query intent and parameters
  - Identify relevant document sections for retrieval
  - Generate appropriate embedding for query
  - Route queries to specialized retrievers based on type
  - Support complex queries requiring multiple retrieval steps

### 5.4 ReAct Agent

#### 5.4.1 Agent Configuration
- **Description**: Configure ReAct agent with tools and reasoning capabilities
- **Priority**: High
- **Acceptance Criteria**:
  - Set up LangChain ReAct agent
  - Configure retrieval tool
  - Integrate custom reasoning model
  - Implement action tools (e.g., email)
  - Define agent prompt and instructions
  - Support conversational context

#### 5.4.2 Tool Integration
- **Description**: Integrate necessary tools for agent actions
- **Priority**: Medium
- **Acceptance Criteria**:
  - Implement retrieval tool for accessing document information
  - Create email tool with user approval flow
  - Design extensible tool framework for future additions
  - Support tool chaining for complex operations
  - Handle tool errors gracefully

### 5.5 User Interface

#### 5.5.1 Chainlit Chat Interface
- **Description**: Create user-friendly chat interface using Chainlit
- **Priority**: High
- **Acceptance Criteria**:
  - Implement clean, responsive chat UI
  - Display system and user messages appropriately
  - Show thinking/reasoning in collapsible blocks
  - Support markdown and code formatting
  - Handle SVG content from LLM responses
  - Provide clear user feedback on processing status

#### 5.5.2 Interactive Approval Flow
- **Description**: Implement interactive approval for actions like sending emails
- **Priority**: Medium
- **Acceptance Criteria**:
  - Present action details clearly for approval
  - Provide approve/reject options
  - Handle user responses appropriately
  - Confirm action execution
  - Support timeout handling for approvals

### 5.6 Additional Features

#### 5.6.1 Authentication & Authorization
- **Description**: Implement user authentication and authorization
- **Priority**: Low (for MVP)
- **Acceptance Criteria**:
  - Support basic authentication
  - Integrate with organization SSO (if applicable)
  - Control access based on user roles
  - Log authentication events

#### 5.6.2 Usage Analytics
- **Description**: Track usage patterns and performance metrics
- **Priority**: Low (for MVP)
- **Acceptance Criteria**:
  - Log query types and frequency
  - Track response times
  - Monitor retrieval accuracy
  - Generate usage reports

## 6. Technical Architecture

### 6.1 System Components

#### 6.1.1 Data Ingestion Layer
- **Components**: Confluence Connector, HTML Parser, Document Processor
- **Functionality**: Retrieves PIR documents from Confluence, parses HTML structure, extracts metadata and content sections
- **Technologies**: Confluence API, Beautiful Soup, LangChain Document Loaders

#### 6.1.2 Embedding Layer
- **Components**: Custom Embedding Model Client, Embedding Cache
- **Functionality**: Generates vector representations of document chunks and queries
- **Technologies**: Custom OpenAI-compatible API, Vector Caching

#### 6.1.3 Storage Layer
- **Components**: Vector Store, Metadata Store
- **Functionality**: Stores document embeddings and metadata for efficient retrieval
- **Technologies**: Chroma, Redis/SQLite (optional for metadata)

#### 6.1.4 Retrieval Layer
- **Components**: Specialized Retrievers, Query Router, Reranker
- **Functionality**: Processes queries, retrieves relevant information, reranks results
- **Technologies**: LangChain Retrievers, LlamaIndex Retrievers, Custom Processing Logic

#### 6.1.5 Reasoning Layer
- **Components**: Custom Reasoning LLM, ReAct Agent, Tools
- **Functionality**: Analyzes retrieved information, generates responses, executes actions
- **Technologies**: Custom OpenAI-compatible API, LangChain Agents, Tool Integrations

#### 6.1.6 Presentation Layer
- **Components**: Chainlit UI, Response Formatter
- **Functionality**: Presents results to users, handles interaction
- **Technologies**: Chainlit, HTML/CSS/JavaScript

### 6.2 System Architecture Diagram

+---------------------+ +-------------------------+ +------------------+ | | | | | | | Confluence Server | | Embedding Model API | | Reasoning LLM | | | | | | | +----------+----------+ +-----------+-------------+ +--------+---------+ | | | v v v +----------+----------------------------+-----------------------------+----------+ | | | Incident PIR RAG App | | | | +----------------+ +------------------+ +------------------------+ | | | | | | | | | | | Data Ingestion | | Vector Database | | LangChain ReAct Agent | | | | | | | | | | | +-------+--------+ +--------+---------+ +-------------+----------+ | | | | | | | v v v | | +-------+----------------------+----------------------------+---------+ | | | | | | | Specialized Retrieval System | | | | | | | +---------------------------------------------------------------------+ | | | | | v | | +---------------------------------------------------------------------+ | | | | | | | Chainlit UI Layer | | | | | | | +---------------------------------------------------------------------+ | | | +--------------------------------------------------------------------------------+ ^ | v +------------------+ | | | User | | | +------------------+


### 6.3 Data Flow

1. **Ingestion Flow**:
   - Confluence API → HTML Content → Document Chunks → Embeddings → Vector Store
   - Confluence API → Metadata → Metadata Store

2. **Query Flow**:
   - User Query → Query Embedding → Vector Similarity Search → Relevant Documents → ReAct Agent → Response

3. **Action Flow**:
   - User Request → ReAct Agent → Tool Selection → Action Preparation → User Approval → Action Execution → Confirmation

### 6.4 Data Model

#### 6.4.1 Document Schema
- **ID**: Unique identifier (typically based on Confluence page ID)
- **Title**: Document title
- **Content**: Document content
- **Metadata**:
  - **incident_id**: Incident ID (e.g., IN000000)
  - **date**: Incident date (YYYYMMDD)
  - **team**: Team name
  - **subject**: Incident subject
  - **impact**: Impact level
  - **impacted_applications**: Applications affected
  - **impacted_regions**: Regions affected
  - **service_outage**: Whether a service outage occurred
  - **outage_type**: Type of outage (Full/Degraded)
  - **change_related**: Whether related to a change
  - **change_number**: Related change number
  - **url**: Confluence URL
  - **last_updated**: Last update timestamp

#### 6.4.